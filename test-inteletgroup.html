<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test InteletGroup Modal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Test del Modal InteletGroup</h1>
        <p>Esta página permite probar la funcionalidad del modal sin duplicación de eventos.</p>
        
        <button type="button" class="btn btn-primary" onclick="abrirModalInteletGroupProspecto()">
            <i class="bi bi-plus-circle me-2"></i>Abrir Modal InteletGroup
        </button>
        
        <div class="mt-4">
            <h3>Registro de eventos:</h3>
            <div id="event-log" class="border p-3" style="height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;">
            </div>
        </div>
        
        <div class="mt-4">
            <button class="btn btn-warning" onclick="clearLog()">Limpiar Log</button>
            <button class="btn btn-info" onclick="checkEventListeners()">Verificar Event Listeners</button>
        </div>
    </div>

    <!-- Modal de InteletGroup -->
    <div class="modal fade" id="inteletGroupProspectModal" tabindex="-1" aria-labelledby="inteletGroupProspectModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="inteletGroupProspectModalLabel">
                        <i class="bi bi-building-add me-2"></i>Nuevo Prospecto InteletGroup
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Cerrar"></button>
                </div>
                <div class="modal-body">
                    <!-- Container para mensajes -->
                    <div id="inteletgroup-message-container" class="mb-3" style="display: none;">
                        <div id="inteletgroup-success-message" class="alert alert-success" style="display: none;">
                            <i class="bi bi-check-circle me-2"></i>
                            <span class="message-text"></span>
                        </div>
                        <div id="inteletgroup-error-message" class="alert alert-danger" style="display: none;">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <span class="message-text"></span>
                        </div>
                        <div id="inteletgroup-loading-message" class="alert alert-info" style="display: none;">
                            <i class="bi bi-hourglass-split me-2"></i>
                            <span class="message-text"></span>
                        </div>
                    </div>
                    
                    <form id="inteletGroupProspectForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tipo_persona" class="form-label">Tipo de Persona <span class="text-danger">*</span></label>
                                    <select class="form-select" id="tipo_persona" name="tipo_persona" required>
                                        <option value="">Seleccione...</option>
                                        <option value="Natural">Natural</option>
                                        <option value="Juridica">Jurídica</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="rut_cliente" class="form-label">RUT Cliente <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="rut_cliente" name="rut_cliente" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="razon_social" class="form-label">Razón Social <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="razon_social" name="razon_social" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="telefono_celular" class="form-label">Teléfono <span class="text-danger">*</span></label>
                            <input type="tel" class="form-control" id="telefono_celular" name="telefono_celular" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        
                        <div id="document-checklist-container"></div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-2"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" id="saveInteletGroupProspectBtn">
                        <span class="btn-text">
                            <i class="bi bi-save me-2"></i>Guardar Prospecto
                        </span>
                        <span class="btn-loading" style="display: none;">
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            Guardando...
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Variables globales para testing
        window.currentUserName = 'Usuario de Prueba';
        window.currentUserId = 1;
        
        // Log de eventos
        function logEvent(message) {
            const log = document.getElementById('event-log');
            const time = new Date().toLocaleTimeString();
            log.innerHTML += `[${time}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('event-log').innerHTML = '';
            logEvent('Log limpiado');
        }
        
        // Interceptar clicks en el botón de guardar
        const originalHandleSaveProspect = window.handleSaveProspect;
        window.handleSaveProspect = function() {
            logEvent('handleSaveProspect llamado');
            // Simular guardado exitoso después de 2 segundos
            setTimeout(() => {
                logEvent('Simulando respuesta exitosa del servidor');
                if (window.modalInstance) {
                    window.modalInstance.hide();
                }
            }, 2000);
        };
        
        // Verificar event listeners
        function checkEventListeners() {
            const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
            if (saveBtn) {
                const listeners = getEventListeners(saveBtn);
                logEvent(`Listeners en botón guardar: ${JSON.stringify(listeners)}`);
            }
            
            const modal = document.getElementById('inteletGroupProspectModal');
            if (modal) {
                logEvent(`Modal instance existe: ${!!window.modalInstance}`);
            }
        }
        
        // Helper para Chrome DevTools
        function getEventListeners(element) {
            if (typeof getEventListeners !== 'undefined') {
                return getEventListeners(element);
            }
            return 'getEventListeners solo disponible en Chrome DevTools';
        }
        
        // Log inicial
        logEvent('Página de prueba cargada');
    </script>
    
    <!-- Cargar el script de InteletGroup -->
    <script src="js/inteletgroup-prospect.js"></script>
</body>
</html>