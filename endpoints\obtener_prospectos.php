<?php
// Configurar encabezados para permitir CORS y especificar el tipo de contenido
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Max-Age: 3600');

// Iniciar sesión si no está iniciada
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verificar si el usuario está autenticado
if (!isset($_SESSION['usuario_id'])) {
    echo json_encode(['success' => false, 'message' => 'Usuario no autenticado']);
    exit;
}

try {
    // Establecer conexión a la base de datos usando PDO
    try {
        // Configuración de la conexión a la base de datos
        $host = 'localhost';
        $port = 3306;
        $dbname = 'gestarse_experian';
        $username = 'gestarse_ncornejo7_experian';
        $password = 'N1c0l7as17';

        $connection = new PDO(
            "mysql:host=$host;dbname=$dbname;charset=utf8",
            $username,
            $password,
            array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
        );
        error_log("Conexión a la base de datos establecida correctamente en obtener_prospectos.php");
    } catch (PDOException $e) {
        error_log("Error al conectar a la base de datos en obtener_prospectos.php: " . $e->getMessage());
        throw new Exception("No se pudo establecer conexión con la base de datos: " . $e->getMessage());
    }

    // Obtener el ID del usuario de la sesión
    $usuario_id = $_SESSION['usuario_id'];

    // Verificar si el usuario tiene rol de administrador
    $es_admin = false;
    $query_rol = "SELECT id, correo, nombre_usuario, rol FROM tb_experian_usuarios WHERE id = ?";
    $stmt_rol = $connection->prepare($query_rol);
    $stmt_rol->execute([$usuario_id]);
    $usuario = $stmt_rol->fetch(PDO::FETCH_ASSOC);

    // Registrar información del usuario para depuración
    error_log("Usuario ID: " . $usuario_id . " - Datos: " . json_encode($usuario));

    // Forzar a que todos los usuarios que no sean explícitamente administradores vean solo sus prospectos
    if ($usuario && isset($usuario['rol']) && ($usuario['rol'] == 'admin' || $usuario['rol'] == 'administrador')) {
        $es_admin = true;
        error_log("Usuario " . $usuario['nombre_usuario'] . " (ID: " . $usuario_id . ") es administrador");
    } else {
        $es_admin = false;
        error_log("Usuario " . ($usuario ? $usuario['nombre_usuario'] : 'desconocido') . " (ID: " . $usuario_id . ") NO es administrador");
    }

    // Consulta SQL para obtener los prospectos con la última bitácora
    // Si es admin, mostrar todos; si no, filtrar por usuario_id
    if ($es_admin) {
        $query = "SELECT p.*,
                  b.estado AS ultimo_estado,
                  b.observaciones AS ultima_observacion,
                  b.fecha_registro AS ultima_fecha_gestion,
                  u.nombre_usuario AS nombre_usuario_creador
                FROM tb_experian_prospecto p
                LEFT JOIN (
                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                    FROM tb_experian_prospecto_bitacora
                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                ORDER BY p.fecha_registro DESC";
        $stmt = $connection->prepare($query);
        error_log("Consulta SQL para administrador: " . $query);
    } else {
        // FORZAR FILTRADO POR USUARIO_ID
        $query = "SELECT p.*,
                  b.estado AS ultimo_estado,
                  b.observaciones AS ultima_observacion,
                  b.fecha_registro AS ultima_fecha_gestion,
                  u.nombre_usuario AS nombre_usuario_creador
                FROM tb_experian_prospecto p
                LEFT JOIN (
                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                    FROM tb_experian_prospecto_bitacora
                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                WHERE p.usuario_id = :usuario_id
                ORDER BY p.fecha_registro DESC";
        $stmt = $connection->prepare($query);
        $stmt->bindParam(':usuario_id', $usuario_id, PDO::PARAM_INT);
        error_log("Consulta SQL para usuario regular: " . $query . " (usuario_id = " . $usuario_id . ")");
    }

    // Registrar en el log qué tipo de consulta se está ejecutando
    error_log("Ejecutando consulta de prospectos como " . ($es_admin ? "administrador" : "usuario regular") . ". Usuario ID: " . $usuario_id);

    // Ejecutar la consulta (ya se preparó arriba)
    $stmt->execute();
    $prospectos = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Registrar la cantidad de registros encontrados
    error_log("Se encontraron " . count($prospectos) . " prospectos para el usuario ID: " . $usuario_id);

    // Devolver los datos en formato JSON
    echo json_encode([
        'success' => true,
        'data' => $prospectos
    ]);
    exit;

} catch (Exception $e) {
    // En caso de error, devolver un mensaje de error
    error_log("Error en obtener_prospectos.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error al obtener los prospectos: ' . $e->getMessage()
    ]);
    exit;
}
?>
