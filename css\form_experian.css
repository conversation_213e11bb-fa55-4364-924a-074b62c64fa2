:root {
            --primary-color: #0046ad; /* <PERSON>zu<PERSON> Experian */
            --primary-light: #e0ebff; /* <PERSON>zul claro Experian */
            --secondary-color: #f8f9fa;
            --border-color: #dee2e6;
            --text-color: #333;
            --header-blue: #0046ad; /* Azul Experian */
            --table-header-bg: #0046ad; /* Azul Experian para encabezados */
            --table-header-text: #ffffff; /* White text for headers */
            --table-cell-text: #495057; /* Dark gray for cell text */
            --table-border: #d0d8e8; /* Borde azulado para tablas */
            --table-row-alt: #f5f9ff; /* Azul muy claro para filas alternas */
            --table-row-hover: #e0ebff; /* Azul claro para hover */
        }

        /* Estilos para el header */
        .site-header {
            background-color: var(--header-blue);
            color: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            position: sticky;
            top: 0;
            z-index: 1000;

            height: 60px; /* Altura fija para diseño consistente */
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            max-width: 1200px;
            margin: 0 auto;
            height: 100%;
        }

        .header-logo {
            height: 35px;
            background-color: white;
            padding: 5px;
            border-radius: 4px;
        }

        .user-info {
            flex: 1;
            text-align: right;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            color: white;
            padding-right: 15px;
        }

        .user-name {
            position: relative;
            padding-left: 25px;
            font-weight: 500;
        }

        .user-name:before {
            content: '\f007'; /* Icono de usuario de FontAwesome */
            font-family: 'FontAwesome';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
        }

        .header-nav ul {
            list-style: none;
            display: flex;
            margin: 0;
            padding: 0;
        }

        .header-nav li {
            margin-left: 20px;
        }

        .header-nav a {
            text-decoration: none;
            color: var(--text-color);
            font-weight: 500;
            padding: 8px 15px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .header-nav a:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .header-nav a.active {
            background-color: var(--primary-color);
            color: white;
        }

        .logout-btn {
            background-color: rgba(231, 76, 60, 0.85);
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 0.85em;
            transition: background-color 0.2s ease;
            border: none;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .logout-btn:hover {
            background-color: #e74c3c;
            text-decoration: none;
            color: white;
        }

        /* Media query para dispositivos móviles */
        @media screen and (max-width: 768px) {
            .header-container {
                padding: 0 10px;
            }

            .header-logo {
                height: 30px;
            }

            .user-info {
                font-size: 0.9em;
                color: white;
                display: flex;
                align-items: center;
            }

            .user-name {
                margin-right: 5px;
            }

            .logout-btn {
                padding: 5px 8px;
                font-size: 0.8em;
            }
            
            /* Ajustes especiales para scroll en móvil - solo para formulario */
            #form-tab {
                height: calc(100vh - 125px);
                overflow-y: auto;
                -webkit-overflow-scrolling: touch;
            }
            
            #form-tab .container {
                padding-bottom: 150px; /* Aún más espacio en móvil */
            }
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-color);
            line-height: 1.6;
            margin: 0;
            background-color: #f5f7fa;
        }

        .container {
            /* max-width: 90%;
            margin: 0 auto; */
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

        }

        header {
            margin-bottom: 30px;
            text-align: center;
        }

        .logo {
            max-height: 60px;
            margin-bottom: 15px;
        }

        h1 {
            color: var(--primary-color);
            margin: 0;
            font-size: 24px;
        }

        .section-header {
            background-color: var(--header-blue);
            padding: 12px 15px;
            margin: 20px 0 15px 0;
            border-radius: 5px;
            font-weight: bold;
            color: white;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            font-size: 16px;
            letter-spacing: 0.5px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        td {
            padding: 8px 10px;
            border: 1px solid var(--border-color);
            width: 25%;
        }

        td.label {
            width: 20%;
            font-weight: 500;
            background-color: var(--secondary-color);
        }

        td.input-cell {
            width: 30%;
        }

        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            box-sizing: border-box;
            font-family: inherit;
        }

        .field-info, .field-type {
            display: none;
        }

        .has-dropdown::after {
            content: "▼";
            color: var(--primary-color);
            margin-left: 5px;
            font-size: 0.8em;
        }

        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            margin-top: 20px;
        }

        button:hover {
            opacity: 0.9;
        }

        .actions {
            text-align: center;
            margin-top: 20px;
        }

        /* Reglas responsive para dispositivos móviles - solo para tablas de formularios */
        @media screen and (max-width: 768px) {
            .container {
                padding: 10px;
                width: 100%;
            }

            /* Aplicar estos estilos solo a tablas de formularios, no a tablas de datos */
            table:not(#user-table):not(#ejecutivos-table) {
                display: block;
                width: 100%;
            }

            table:not(#user-table):not(#ejecutivos-table) tr {
                display: flex;
                flex-direction: column;
                margin-bottom: 15px;
                border: 1px solid var(--border-color);
                width: 100%;
            }

            table:not(#user-table):not(#ejecutivos-table) td {
                width: 100% !important;
                display: block;
                box-sizing: border-box;
                border: none;
                border-bottom: 1px solid var(--border-color);
            }

            table:not(#user-table):not(#ejecutivos-table) td:last-child {
                border-bottom: none;
            }

            table:not(#user-table):not(#ejecutivos-table) td.label {
                background: var(--primary-light);
                text-align: center;
                padding: 10px;
            }

            table:not(#user-table):not(#ejecutivos-table) td.input-cell {
                padding: 10px;
                width: 100%;
            }

            input, select {
                width: 100%;
                max-width: 100%;
                box-sizing: border-box;
            }

            .section-header {
                margin: 30px 0 15px 0;
            }
        }

        /* Ajustes adicionales para pantallas muy pequeñas */
        @media screen and (max-width: 480px) {
            body {
                padding: 0;
                margin: 0;
                overflow-x: hidden;
            }

            .container {
                padding: 10px;
                overflow-y: visible !important;
            }

            h1 {
                font-size: 20px;
            }

            .logo {
                max-height: 40px;
            }
            
            /* Ajustes específicos para el formulario en móviles pequeños */
            #form-tab {
                height: auto !important;
                overflow-y: visible !important;
                -webkit-overflow-scrolling: touch !important;
                padding-bottom: 100px !important;
            }
            
            #form-tab .container {
                min-height: calc(100vh - 125px);
                padding-bottom: 200px !important; /* Mucho espacio para ver el botón */
            }
            
            /* Arreglar el tab de prospectos */
            #new-tab.active {
                display: block !important;
                min-height: calc(100vh - 125px);
            }
        }

        /* Eliminar los estilos para inputs inválidos */
        input:invalid {
            border-color: var(--border-color);
            background-color: white;
        }

        /* Mensaje de error */
        .error-message {
            color: #dc3545;
            font-size: 0.8em;
            margin-top: 4px;
            display: none;
        }

        input:invalid + .error-message {
            display: block;
        }

        /* Estilo para mensajes informativos */
        .info-message {
            color: #6c757d;
            font-size: 0.8em;
            margin-top: 4px;
            display: block;
        }

        /* Reset básico para todos los elementos */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        /* Contenedor principal */
        /* .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            box-sizing: border-box;
        } */

        /* Estilos base para la tabla e inputs */
        table {
            width: 100%;
            border-collapse: collapse;
        }

        td {
            padding: 8px;
            border: 1px solid var(--border-color);
        }

        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 16px; /* Mejor tamaño para móviles */
        }

        /* Reglas responsive mejoradas - solo para tablas de formularios */
        @media screen and (max-width: 768px) {
            body {
                padding: 0;
            }

            .container {
                padding: 10px;
                width: 100%;
                max-width: 100%;
            }

            /* Aplicar estos estilos solo a tablas de formularios, no a tablas de datos */
            table:not(#user-table):not(#ejecutivos-table),
            table:not(#user-table):not(#ejecutivos-table) tbody,
            table:not(#user-table):not(#ejecutivos-table) tr {
                display: block;
                width: 100%;
            }

            table:not(#user-table):not(#ejecutivos-table) tr {
                margin-bottom: 15px;
                background: #fff;
                border: 1px solid var(--border-color);
            }

            table:not(#user-table):not(#ejecutivos-table) td {
                display: block;
                width: 100% !important;
                padding: 10px;
                border: none;
                border-bottom: 1px solid var(--border-color);
            }

            table:not(#user-table):not(#ejecutivos-table) td:last-child {
                border-bottom: none;
            }

            table:not(#user-table):not(#ejecutivos-table) td.label {
                background: var(--primary-light);
                text-align: center;
                font-weight: bold;
            }

            table:not(#user-table):not(#ejecutivos-table) td.input-cell {
                padding: 15px;
            }

            /* Asegurar que los inputs ocupen todo el ancho */
            input,
            select {
                display: block;
                width: 100%;
                max-width: none;
                min-width: 0;
                margin: 0;
                box-sizing: border-box;
            }

            /* Ajuste para los mensajes de error */
            .error-message {
                width: 100%;
                margin-top: 5px;
                font-size: 14px;
            }
        }

        /* Ajustes específicos para pantallas muy pequeñas */
        @media screen and (max-width: 480px) {
            body {
                padding: 0;
            }

            .container {
                padding: 8px;
                margin: 0;
                border-radius: 0;
                box-shadow: none;
                max-width: 100%;
            }

            td.input-cell {
                padding: 12px 10px;
            }

            td.label {
                font-size: 14px;
                padding: 10px;
                background-color: var(--primary-light);
                color: var(--primary-color);
                font-weight: bold;
            }

            input,
            select {
                font-size: 16px;
                padding: 12px 10px;
                border-radius: 5px;
                border: 1px solid #ccd0d9;
            }

            .section-header {
                margin: 15px 0 10px 0;
                padding: 10px;
                font-size: 15px;
                border-radius: 4px;
                text-align: center;
            }

            .subsection-header {
                font-size: 13px;
                padding: 8px 10px;
                margin: 10px 0 8px 0;
            }

            /* Ajustar espaciado del footer en móvil */


            .footer-tab i {
                font-size: 1.1em;
                margin-bottom: 3px;
            }

            .footer-tab span {
                font-size: 0.75em;
            }
        }

        /* Estilos para el indicador de pasos */
        .steps-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            position: relative;
        }

        .step-indicator {
            width: 30%;
            text-align: center;
            padding: 15px 0;
            border-radius: 5px;
            background-color: var(--secondary-color);
            position: relative;
            z-index: 2;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .step-indicator.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            font-weight: bold;
        }

        .step-indicator:hover:not(.active) {
            background-color: #e0e0e0;
        }

        .progress-line {
            position: absolute;
            top: 50%;
            height: 3px;
            width: 100%;
            background-color: var(--secondary-color);
            z-index: 1;
            transform: translateY(-50%);
        }

        .progress-line .fill {
            height: 100%;
            background-color: var(--primary-color);
            width: 0%;
            transition: width 0.3s ease;
        }

        .step-indicator::before {
            content: attr(data-step);
            display: inline-block;
            width: 24px;
            height: 24px;
            line-height: 24px;
            border-radius: 50%;
            background-color: var(--header-blue);
            color: white;
            margin-right: 8px;
        }

        /* Contenedor de secciones */
        .section-container {
            display: none;
        }

        .section-container.active {
            display: block;
        }

        /* Botones de navegación */
        .nav-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            padding: 10px 0;
        }

        .btn-prev, .btn-next, .btn-submit {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }

        .btn-prev {
            background-color: #6c757d;
        }

        .btn-prev:hover, .btn-next:hover, .btn-submit:hover {
            opacity: 0.9;
        }

        /* Ocultamos la sección de acciones original */
        .actions {
            display: none;
        }

        @media screen and (max-width: 768px) {
            .steps-container {
                flex-direction: column;
                margin-bottom: 20px;
            }

            .step-indicator {
                width: 100%;
                margin-bottom: 10px;
            }

            .progress-line {
                display: none;
            }
        }

        /* Estilo para el mensaje de validación del RUT */
        .rut-message {
            color: #6c757d;
            font-size: 0.8em;
            margin-top: 4px;
            display: block;
            font-style: italic;
        }

        /* Estilo para los encabezados de subsección */
        .subsection-header {
            background-color: var(--primary-light);
            color: var(--primary-color);
            padding: 10px 15px;
            margin: 15px 0;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.95em;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            text-align: left;
            border-left: 4px solid var(--primary-color);
        }

        /* Estilo para inputs readonly */
        input[readonly] {
            background-color: #f8f9fa;
            cursor: not-allowed;
        }

        /* Ajuste de márgenes entre subsecciones */
        table {
            margin-bottom: 25px;
        }

        /* Estilos para el sistema de tabs */
        .tabs-container {
            width: 100%;
            margin: 20px auto;
            max-width: 90%;
        }

        .tabs-header {
            display: flex;
            background-color: #f0f0f0;
            border-radius: 5px 5px 0 0;
            overflow: hidden;
        }

        .tab-button {
            padding: 15px 20px;
            background-color: #f0f0f0;
            border: none;
            cursor: pointer;
            font-size: 16px;
            flex-grow: 1;
            text-align: center;
            transition: all 0.3s ease;
            font-weight: bold;
            color: #555;
        }

        .tab-button:hover {
            background-color: #e0e0e0;
        }

        .tab-button.active {
            background-color: var(--primary-color);
            color: white;
        }

        .tab-content {
            display: none;
            padding: 20px;
            background: #fff;
            border-radius: 0 0 5px 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .tab-content.active {
            display: block;
        }

        /* Ajuste para el contenedor de tabla */
        #table-container {
            display: block;
            margin: 0;
            padding: 0;
            box-shadow: none;
        }

        /* Estilos para la tabla de datos */
        .table-wrapper {
            max-height: calc(86vh - 200px); /* Altura viewport menos espacio para header, controles y footer */
            overflow-x: auto;
            overflow-y: auto;
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            width: 100%;
            display: block;
        }

        /* Ajuste específico para móviles */
        @media (max-width: 768px) {
            .table-wrapper {
                max-height: calc(86vh - 220px); /* Ajuste para móviles donde pueden haber más elementos */
            }
        }

        #user-table {
            width: auto;
            min-width: 100%;
            border-collapse: collapse;
            font-size: 14px;
            white-space: nowrap;
            table-layout: auto;
        }

        #user-table th,
        #user-table td {
            padding: 8px;
            border: 1px solid #ddd;
            text-align: left;
            min-width: 120px;
        }

        #user-table th {
            background-color: #f5f5f5;
            position: sticky;
            top: 0;
            z-index: 1;
        }

        #user-table tbody tr:nth-child(even) {
            background-color: var(--table-row-alt);
        }

        #user-table tbody tr:hover {
            background-color: var(--table-row-hover);
        }

        .no-data,
        .error-data {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .error-data {
            color: #dc3545;
        }

        /* Responsive adjustments for tables */
        @media (max-width: 768px) {
            /* Estilos responsive mejorados para tablas de datos */

            /* Contenedor de tabla con scroll horizontal */
            .table-wrapper {
                max-height: 70vh; /* Altura máxima como porcentaje de la ventana visible */
                overflow-y: auto; /* Scroll vertical */
                overflow-x: auto; /* Scroll horizontal */
                border-radius: 8px; /* Bordes redondeados */
                border: 1px solid var(--table-border); /* Borde */
                box-shadow: 0 2px 5px rgba(0,0,0,0.05); /* Sombra sutil */
                margin: 15px 0; /* Margen vertical */
                background-color: white; /* Fondo blanco para la tabla */
                display: block; /* Asegurar que sea un bloque para el scroll */
                width: 100%; /* Ancho completo */
            }

            /* Tablas de datos mantienen su estructura */
            #user-table, #ejecutivos-table {
                font-size: 12px; /* Tamaño de fuente más legible */
                width: 100%; /* Ancho completo */
                min-width: 800px; /* Ancho mínimo para asegurar scroll horizontal */
                border-collapse: collapse;
                table-layout: auto; /* Permitir que las columnas se ajusten al contenido */
            }

            #user-table th, #user-table td,
            #ejecutivos-table th, #ejecutivos-table td {
                padding: 10px 12px;
                min-width: 120px; /* Ancho mínimo para legibilidad */
                white-space: nowrap; /* Evitar que el texto se rompa */
                color: #333; /* Color de texto oscuro para legibilidad */
                border: 1px solid var(--table-border);
            }

            #user-table th, #ejecutivos-table th {
                position: sticky; /* Mantener el encabezado fijo durante scroll */
                top: 0; /* Fijar en la parte superior */
                background-color: var(--table-header-bg); /* Fondo azul */
                color: white; /* Texto blanco para contraste */
                font-weight: 600; /* Texto más grueso */
                z-index: 2; /* Asegurar que los encabezados estén sobre el contenido */
                box-shadow: 0 2px 3px rgba(0,0,0,0.1); /* Sombra sutil para profundidad */
                text-transform: uppercase; /* Texto en mayúsculas para destacar */
                font-size: 11px; /* Tamaño reducido para móvil */
                letter-spacing: 0.5px; /* Espaciado de letras para legibilidad */
                white-space: nowrap; /* Evitar que el texto se rompa en los encabezados */
            }

            /* Optional: Style first column to be sticky */
            #user-table td:first-child,
            #ejecutivos-table td:first-child {
                position: sticky;
                left: 0;
                background-color: white; /* Match background */
                z-index: 1;
            }

            #user-table th:first-child,
            #ejecutivos-table th:first-child {
                position: sticky;
                left: 0;
                z-index: 3; /* Ensure first header is above first cell */
            }
        }

        .table-controls {
            margin-bottom: 15px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            padding: 0 10px; /* Añadir padding si es necesario */
        }

        .table-search {
            padding: 8px 12px;
            border: 1px solid var(--border-color); /* Borde consistente */
            border-radius: 6px; /* Esquinas redondeadas */
            width: 250px;
            font-size: 13px; /* Tamaño de fuente consistente */
        }

        /* Responsive para tabs y controles de tabla */
        @media (max-width: 768px) {
            .tabs-header {
                flex-direction: column;
            }

            .tab-button {
                width: 100%;
                border-radius: 0;
                font-size: 15px; /* Ajuste tamaño fuente en móvil */
            }

            .tab-content {
                padding: 10px;
            }

            .table-search {
                width: 100%;
                margin-bottom: 10px; /* Espacio debajo del buscador */
            }

            .table-controls {
                flex-direction: column;
                align-items: stretch;
                padding: 0;
            }

            /* Ajustes para el contenedor de tabla */
            #table-container {
                padding: 10px;
            }
        }

        /* Contenedor principal de la tabla */
        #table-container {
            width: 100%;
            padding: 20px;
            overflow: hidden;
        }

        /* Wrapper de la tabla con scroll horizontal */
        .table-wrapper {
            width: 100%;
            overflow-x: auto;
            margin-top: 20px;
            padding-bottom: 15px; /* Espacio para el scrollbar */
            display: block;
        }

        /* Estilos de la tabla */
        #user-table {
            min-width: 100%;
            width: auto;
            border-collapse: separate;
            border-spacing: 0;
            border: 1px solid #ddd;
            table-layout: auto;
        }

        /* Estilos para las celdas */
        #user-table th,
        #user-table td {
            min-width: 150px; /* Ancho mínimo para cada columna */
            padding: 12px 15px;
            border: 1px solid #ddd;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }

        /* Asegurar que la primera columna (ID) sea más estrecha */
        #user-table th:first-child,
        #user-table td:first-child {
            min-width: 60px;
            max-width: 80px;
        }

        /* Estilos para el encabezado */
        #user-table th {
            background-color: var(--table-header-bg);
            color: white;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 1;
            box-shadow: 0 1px 0 rgba(0,0,0,0.1);
            padding: 12px 15px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Aplicar los mismos estilos a la tabla de ejecutivos */
        #ejecutivos-table {
            width: auto;
            min-width: 100%;
            border-collapse: collapse;
            font-size: 14px;
            white-space: nowrap;
            table-layout: auto;
        }

        #ejecutivos-table th,
        #ejecutivos-table td {
            min-width: 120px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            text-align: left;
            white-space: nowrap;
        }

        #ejecutivos-table th {
            background-color: var(--table-header-bg);
            color: white;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 1;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            padding: 12px 10px;
            text-transform: uppercase;
            font-size: 13px;
            letter-spacing: 0.5px;
        }

        /* Estilos para las filas */
        #user-table tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        #user-table tbody tr:hover {
            background-color: #f5f5f5;
        }

        /* Estilos para el scrollbar horizontal - solo para WebKit/Blink */
        .table-wrapper::-webkit-scrollbar {
            height: 8px;
            width: 8px;
        }

        .table-wrapper::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .table-wrapper::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        .table-wrapper::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* Ajustes responsivos */
        @media (max-width: 768px) {
            #table-container {
                padding: 10px;
            }

            .table-wrapper {
                margin-top: 10px;
            }

            #user-table th,
            #user-table td {
                min-width: 120px;
                padding: 8px 10px;
            }
        }

        /* Estilos para el buscador */
        .table-controls {
            margin-bottom: 20px;
            display: flex;
            justify-content: flex-end;
        }

        .table-search {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 250px;
        }

        .btn-modal-open, .export-button {
            background-color: #0d6efd;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }

        .btn-modal-open:hover, .export-button:hover {
            background-color: #0b5ed7;
            opacity: 0.9;
        }

        .btn-modal-open i, .export-button i {
            font-size: 16px;
        }

        /* Estilos comunes para ambos botones */
        #openProspectoModal.btn-modal-open,
        #exportEjecutivos.export-button {
            background-color: #0d6efd !important;
            color: white !important;
            border: none !important;
            padding: 10px 20px !important;
            border-radius: 4px !important;
            cursor: pointer !important;
            font-weight: 500 !important;
            display: inline-flex !important;
            align-items: center !important;
            gap: 8px !important;
            font-size: 14px !important;
            text-decoration: none !important;
            margin-right: 10px !important;
            box-shadow: none !important;
        }

        /* Hover para ambos botones */
        #openProspectoModal.btn-modal-open:hover,
        #exportEjecutivos.export-button:hover {
            background-color: #0b5ed7 !important;
            opacity: 0.9 !important;
        }

        /* Estilos para los íconos */
        #openProspectoModal.btn-modal-open i,
        #exportEjecutivos.export-button i {
            font-size: 16px !important;
            margin-right: 5px !important;
        }

        /* App footer styles */
        .app-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: #0046ad;
            display: flex;
            justify-content: space-around;
            padding: 8px 0;
            box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
            z-index: 100;
            height: 65px; /* Aumentado de 55px a 65px */
        }
        
        /* Ajustar el contenido principal para scroll vertical */
        .main-content {
            padding-bottom: 65px; /* Espacio para el footer */
        }
        
        .tab-content {
            padding-bottom: 80px; /* Espacio para evitar que el footer tape el contenido */
        }
        
        /* Ajustes específicos solo para el formulario */
        #form-tab {
            overflow-y: auto;
            padding-bottom: 70px; /* Espacio para el footer */
        }
        
        #form-tab .container {
            padding: 20px;
            padding-bottom: 100px; /* Espacio adicional para ver botón siguiente */
        }
        
        /* Asegurar que el scroll sea visible */
        #form-tab, 
        #form-tab .section-container, 
        #section1, #section2, #section3 {
            overflow-y: visible;
        }

        .footer-tab {
            background: none;
            border: none;
            color: rgba(255,255,255,0.7);
            display: flex;
            flex-direction: column-reverse;
            align-items: center;
            padding: 6px 0; /* Aumentado de 4px a 6px */
            flex: 1;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .footer-tab i {
            font-size: 1.3em; /* Aumentado de 1.1em a 1.3em */
            margin-top: 5px; /* Aumentado de 4px a 5px */
        }

        .footer-tab span {
            font-size: 1.05em; /* Aumentado de 0.95em a 1.05em */
            white-space: nowrap;
            opacity: 1;
            margin-bottom: 5px; /* Aumentado de 4px a 5px */
            order: -1;
            font-weight: 500; /* Añadido para hacer el texto más visible */
        }

        /* Estado activo del tab */
        .footer-tab.active {
            color: white;
            position: relative;
            font-weight: bold;
        }

        /* Añadir un indicador visual para el tab activo */
        .footer-tab.active::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 3px;
            background-color: white;
            border-radius: 3px;
        }

        /* Estilos del modal */
        .modal {
            display: none;
            position: fixed;
            z-index: 1050;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            overflow-y: auto;
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 90%;
            max-width: 800px;
            border-radius: 8px;
            position: relative;
        }

        .close-modal {
            position: absolute;
            right: 15px;
            top: 10px;
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close-modal:hover {
            color: #000;
        }

        /* Asegurar que el botón del modal sea visible y tenga estilo */
        .btn-modal-open {
            background-color: var(--primary-color);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 20px;
        }

        .btn-modal-open:hover {
            background-color: #003d94;
        }

        /* Botones en la misma fila */
        .button-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            gap: 10px;
        }

        .button-container .btn-modal-open,
        .button-container .export-button {
            margin: 0;
            flex: 1;
            font-size: 0.9em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .export-button {
            background-color: #27ae60;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .export-button:hover {
            background-color: #2ecc71;
        }

        /* Ajustes adicionales para mejorar la experiencia en móvil */
        @media screen and (max-width: 768px) {
            .container {
                padding: 10px;
                width: 100%;
                max-width: 100%;
            }

            .table-search {
                width: 100%;
                margin-bottom: 10px;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
            }

            .section-header {
                font-size: 1.1em;
                padding: 10px;
                margin-bottom: 15px;
                background-color: var(--header-blue);
                border-radius: 4px;
                text-align: center;
            }
        }

        #openProspectoModal.btn-modal-open {
            background-color: #2ecc4a !important; /* Verde */
            color: white !important;
            border: none !important;
            padding: 10px 20px !important;
            border-radius: 4px !important;
            cursor: pointer !important;
            font-weight: 500 !important;
            display: inline-flex !important;
            align-items: center !important;
            gap: 8px !important;
            font-size: 14px !important;
            text-decoration: none !important;
            margin-right: 10px !important;
            box-shadow: none !important;
        }

        #openProspectoModal.btn-modal-open:hover {
            background-color: #27b340 !important; /* Verde un poco más oscuro para el hover */
            opacity: 0.9 !important;
        }

        table thead th {
            background-color: #4682B4 !important; /* Un azul más suave (Steel Blue) */
            color: white !important;
            padding: 12px 8px !important;
            text-align: left !important;
            font-weight: 500 !important;
            border: 1px solid #dee2e6 !important;
        }

        /* Específicamente para las tablas de ejecutivos y usuarios */
        #ejecutivos-table thead th,
        #user-table thead th {
            background-color: #4682B4 !important;
            color: white !important;
        }

        /* Estilos para el botón de bitácora */
        .btn-bitacora {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 6px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.3s;
            white-space: nowrap;
            display: block;
            margin: 0 auto; /* Centrar horizontalmente */
            width: 90px; /* Ancho fijo para consistencia */
        }

        .btn-bitacora:hover {
            background-color: #45a049;
        }

        /* Centrar el contenido de la celda que contiene el botón */
        #ejecutivos-table td:first-child {
            text-align: center;
            vertical-align: middle;
        }

        /* Estilos para la bitácora - Nuevo diseño de timeline */
        .bitacora-historial {
            margin-top: 30px;
            border-top: 1px solid #f0f0f0;
            padding-top: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .bitacora-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px 15px 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .bitacora-historial h3 {
            margin: 0;
            color: #333;
            font-size: 18px;
            font-weight: 600;
        }

        .view-all {
            color: var(--primary-color);
            font-size: 14px;
            text-decoration: none;
            opacity: 0.8;
        }

        .view-all:hover {
            opacity: 1;
            text-decoration: underline;
        }

        /* Contenedor para la timeline */
        .timeline-container {
            padding: 15px 0;
            max-height: 400px;
            overflow-y: auto;
        }

        /* Estilos para la timeline */
        .timeline {
            position: relative;
            padding: 0 20px;
        }

        /* Línea vertical de la timeline */
        .timeline::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: 30px;
            width: 2px;
            background-color: #e9ecef;
            z-index: 0; /* Asegurar que esté detrás de los puntos */
        }

        /* Elementos individuales de la timeline */
        .timeline-item {
            position: relative;
            padding-left: 45px;
            padding-bottom: 25px;
        }

        /* Punto de color para cada elemento */
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 22px;
            top: 50%; /* Centrar verticalmente */
            transform: translateY(-50%); /* Asegurar centrado perfecto */
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: #4a6cf7;
            z-index: 1;
        }

        /* Colores diferentes para los puntos según el estado */
        .timeline-item.estado-pendiente::before { background-color: #f7c14a; } /* Amarillo */
        .timeline-item.estado-en-proceso::before { background-color: #4a6cf7; } /* Azul */
        .timeline-item.estado-completado::before { background-color: #4ac77c; } /* Verde */
        .timeline-item.estado-cancelado::before { background-color: #f74a4a; } /* Rojo */
        .timeline-item.estado-default::before { background-color: #a0a0a0; } /* Gris por defecto */

        /* Contenido del elemento de timeline */
        .timeline-content {
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        /* Encabezado del elemento de timeline */
        .timeline-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        /* Texto principal del elemento */
        .timeline-text {
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        /* Texto secundario (observaciones) */
        .timeline-subtext {
            color: #666;
            font-size: 14px;
            margin-bottom: 8px;
        }

        /* Pie del elemento con información adicional */
        .timeline-footer {
            display: flex;
            justify-content: space-between;
            font-size: 13px;
            color: #888;
            margin-top: 8px;
        }

        /* Destacar texto importante */
        .highlight {
            color: var(--primary-color);
            font-weight: 500;
        }

        /* Estilos para el tiempo transcurrido */
        .timeline-time {
            color: #999;
            font-size: 13px;
            white-space: nowrap;
        }

        /* Estilos para mensajes de carga y error */
        .loading-data, .error-data, .no-data {
            padding: 20px;
            text-align: center;
            margin: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .loading-data {
            color: #666;
        }

        .error-data {
            color: #dc3545;
            background-color: #f8d7da;
        }

        .no-data {
            color: #6c757d;
        }

        /* Responsive para móviles */
        @media (max-width: 576px) {
            .timeline::before {
                left: 20px;
            }
            .timeline-item {
                padding-left: 35px;
            }
            .timeline-item::before {
                left: 12px;
            }
            .bitacora-header {
                padding: 0 15px 10px 15px;
            }
            .timeline {
                padding: 0 10px;
            }
        }

        /* Estilos para mensajes de carga y error */
        .loading-data, .error-data, .no-data {
            padding: 20px;
            text-align: center;
        }

        .loading-data {
            color: #666;
            background-color: #f8f9fa;
        }

        .error-data {
            color: #dc3545;
            background-color: #f8d7da;
        }

        .no-data {
            color: #6c757d;
            background-color: #f8f9fa;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .form-group textarea {
            resize: vertical;
        }

        .modal-subtitle {
            margin-top: 5px;
            font-size: 14px;
            opacity: 0.9;
        }

        /* Responsive adjustments for tables */
        @media (max-width: 768px) {
            #ejecutivos-table {
                font-size: 12px;
                width: 100%;
                min-width: 800px;
                border-collapse: collapse;
                table-layout: auto;
            }

            #ejecutivos-table th,
            #ejecutivos-table td {
                position: static !important; /* Elimina cualquier posicionamiento fijo */
                left: auto !important; /* Elimina el left fijo */
                width: auto !important; /* Permite que el ancho sea automático */
                min-width: 120px;
                white-space: nowrap;
                color: #333;
                border: 1px solid var(--table-border);
            }

            /* Asegurarse de que no haya estilos que fijen columnas */
            #ejecutivos-table th:first-child,
            #ejecutivos-table td:first-child {
                position: static !important;
                left: auto !important;
                z-index: auto !important;
            }
        }
        
        /* Ajustes finales específicos para el formulario */
        #form-tab .section-container {
            min-height: auto;
            padding-bottom: 100px;
            position: relative;
            z-index: 10; /* Asegurar que esté por encima y sea interactivo */
        }
        
        /* Estilos específicos para botones dentro del formulario */
        #form-tab .btn-next, 
        #form-tab .btn-prev, 
        #form-tab .btn-submit {
            position: relative;
            z-index: 20; /* Mayor que el contenedor para asegurar que reciba clicks */
            cursor: pointer !important;
            pointer-events: auto !important;
        }
        
        /* Ajustar la visualización de las tabs para que no queden en blanco */
        .tab-content {
            min-height: 300px;
        }
        
        /* Asegurar que el tab de prospectos sea visible */
        #new-tab {
            display: none;
            padding: 20px;
            height: auto !important;
            overflow: visible !important;
            background-color: #fff;
        }
        
        #new-tab.active {
            display: block !important;
        }
        
        #new-tab .container {
            height: auto !important;
            overflow: visible !important;
            padding-bottom: 70px;
        }

