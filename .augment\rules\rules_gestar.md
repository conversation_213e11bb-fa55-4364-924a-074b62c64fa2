---
type: "always_apply"
---

Cuando solicite modificaciones en archivos del sistema web, necesito que sigas este flujo de trabajo estructurado:

1. **Obtención de credenciales**: Usa el MCP mysql-gestar para consultar la tabla `tb_experian_usuarios` y obtener credenciales válidas de usuarios (especialmente usuarios con proyecto 'inteletGroup'  según corresponda).

2. **Acceso al sistema**: Navega a http://localhost/intranet/dist/login.php usando Playwright y haz login con las credenciales obtenidas.

3. **Navegación y pruebas**: Ve al archivo/funcionalidad específica mencionada en mi solicitud y reproduce el problema reportado usando Playwright para interactuar con la interfaz.

4. **Confirmación del problema**: Documenta y confirma que el problema existe tal como fue descrito.

5. **Análisis y solución**: 
   - Revisa el código fuente relevante usando las herramientas de edición
   - Identifica las posibles causas del problema
   - Propón y aplica las modificaciones necesarias en los archivos correspondientes

6. **Verificación**: Repite las pruebas con Playwright para confirmar que la modificación resuelve el problema.

7. **Iteración**: Si el problema persiste, repite el ciclo desde el paso 5 hasta que esté completamente solucionado.

8. ** Generar un resumen con el problema identificado y la solucion implementada. 

**Consideraciones importantes**:
- Usa siempre Playwright para las pruebas de interfaz
- Mantén compatibilidad con PHP 7.3.33 (usar `bind_result()` en lugar de `get_result()`)
- Documenta cada paso del proceso y los cambios realizados
- Verifica que las modificaciones no rompan otras funcionalidades existentes
- Puedes ocupar el mcp mysql-gestar , para revisar las estructuras de las tablas 
- Manten siempre las respuesas en español 