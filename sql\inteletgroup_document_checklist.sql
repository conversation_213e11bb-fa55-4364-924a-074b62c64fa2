-- =====================================================
-- Script de actualización para InteletGroup
-- Agrega soporte para checklist de documentos
-- =====================================================

-- 1. Agregar campo tipo_persona a la tabla de prospectos
ALTER TABLE tb_inteletgroup_prospectos 
ADD COLUMN tipo_persona ENUM('Natural', 'Juridica') NOT NULL DEFAULT 'Natural' AFTER competencia_actual;

-- 2. Crear tabla para tipos de documentos
CREATE TABLE IF NOT EXISTS tb_inteletgroup_tipos_documento (
    id INT PRIMARY KEY AUTO_INCREMENT,
    codigo VARCHAR(50) NOT NULL UNIQUE,
    nombre VARCHAR(255) NOT NULL,
    descripcion TEXT,
    tipo_persona ENUM('Natural', 'Juridica', 'Ambos') NOT NULL,
    es_obligatorio BOOLEAN DEFAULT TRUE,
    orden INT DEFAULT 0,
    estado ENUM('Activo', 'Inactivo') DEFAULT 'Activo',
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tipo_persona (tipo_persona),
    INDEX idx_estado (estado)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish_ci;

-- 3. Insertar tipos de documentos para Persona Natural
INSERT INTO tb_inteletgroup_tipos_documento (codigo, nombre, descripcion, tipo_persona, es_obligatorio, orden) VALUES
('PN_CEDULA_FRONTAL', 'Cédula de identidad frontal', 'Fotografía o escaneo del frente de la cédula de identidad', 'Natural', TRUE, 1),
('PN_CEDULA_TRASERA', 'Cédula de identidad trasera', 'Fotografía o escaneo del reverso de la cédula de identidad', 'Natural', TRUE, 2),
('PN_BOLETA_PATENTE', 'Boleta y/o Patente Comercial', 'Documento que acredite la actividad comercial', 'Natural', TRUE, 3),
('PN_FOTO_INTERIOR', 'Fotografía interior del establecimiento', 'Fotografía del interior del establecimiento que muestre la actividad económica', 'Natural', TRUE, 4),
('PN_FOTO_EXTERIOR', 'Fotografía exterior del establecimiento', 'Fotografía del exterior del establecimiento comercial, que muestre fachada y dirección', 'Natural', TRUE, 5),
('PN_RESPALDO_CUENTA', 'Respaldo de cuenta de Abono', 'Documento que indique Titular, número de cuenta y logo del banco (Cta Cte o Cta Vista)', 'Natural', TRUE, 6),
('PN_CARPETA_TRIBUTARIA', 'Carpeta Tributaria (FAPRO)', 'Carpeta tributaria del SII', 'Natural', TRUE, 7),
('PN_MANDATO_PAC', 'Mandato PAC', 'Para clientes con cuenta de abono distinta a BCI donde el titular es el establecimiento', 'Natural', FALSE, 8),
('PN_PODER_NOTARIAL', 'Poder Notarial', 'Para clientes con cuenta de abono distinta a BCI, donde el titular es distinto al establecimiento', 'Natural', FALSE, 9),
('PN_CARTA_ACEPTACION', 'Carta de Aceptación', 'Excepción: Clientes con facturación inferior a 100UF, Carta de Aceptación a la comisión garantizada', 'Natural', FALSE, 10);

-- 4. Insertar tipos de documentos para Persona Jurídica
INSERT INTO tb_inteletgroup_tipos_documento (codigo, nombre, descripcion, tipo_persona, es_obligatorio, orden) VALUES
('PJ_CEDULA_REPRESENTANTES', 'Cédula de identidad representantes legales', 'Cédula de identidad por ambos lados, de él o los representantes legales', 'Juridica', TRUE, 1),
('PJ_CONSTITUCION_PODERES', 'Constitución o Poderes Vigentes', 'Estatutos y Vigencia (FAPRO)', 'Juridica', TRUE, 2),
('PJ_BOLETA_PATENTE', 'Boleta y/o Patente Comercial', 'Documento que acredite la actividad comercial', 'Juridica', TRUE, 3),
('PJ_FOTO_INTERIOR', 'Fotografía interior del establecimiento', 'Fotografía del interior del establecimiento que muestre la actividad económica', 'Juridica', TRUE, 4),
('PJ_FOTO_EXTERIOR', 'Fotografía exterior del establecimiento', 'Fotografía del exterior del establecimiento comercial, que muestre fachada y dirección', 'Juridica', TRUE, 5),
('PJ_RESPALDO_CUENTA', 'Respaldo de cuenta de Abono', 'Documento que indique Titular, número de cuenta y logo del banco (Cta Cte o Cta Vista)', 'Juridica', TRUE, 6),
('PJ_CARPETA_TRIBUTARIA', 'Carpeta Tributaria (FAPRO)', 'Carpeta tributaria del SII', 'Juridica', TRUE, 7),
('PJ_ERUT', 'E-Rut (FAPRO)', 'E-Rut que indique nombre del representante que firma', 'Juridica', TRUE, 8);

-- 5. Modificar tabla de documentos para incluir el tipo de documento
ALTER TABLE tb_inteletgroup_documentos 
ADD COLUMN tipo_documento_id INT AFTER prospecto_id,
ADD FOREIGN KEY (tipo_documento_id) REFERENCES tb_inteletgroup_tipos_documento(id);

-- 6. Crear tabla para tracking del checklist de documentos
CREATE TABLE IF NOT EXISTS tb_inteletgroup_documento_checklist (
    id INT PRIMARY KEY AUTO_INCREMENT,
    prospecto_id INT NOT NULL,
    tipo_documento_id INT NOT NULL,
    documento_id INT DEFAULT NULL,
    estado ENUM('Pendiente', 'Subido', 'Aprobado', 'Rechazado') DEFAULT 'Pendiente',
    observaciones TEXT,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (prospecto_id) REFERENCES tb_inteletgroup_prospectos(id),
    FOREIGN KEY (tipo_documento_id) REFERENCES tb_inteletgroup_tipos_documento(id),
    FOREIGN KEY (documento_id) REFERENCES tb_inteletgroup_documentos(id),
    UNIQUE KEY unique_prospecto_tipo (prospecto_id, tipo_documento_id),
    INDEX idx_prospecto (prospecto_id),
    INDEX idx_estado (estado)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish_ci;

-- 7. Vista para facilitar la consulta del estado del checklist
CREATE OR REPLACE VIEW vw_inteletgroup_checklist_estado AS
SELECT 
    p.id AS prospecto_id,
    p.rut_cliente,
    p.razon_social,
    p.tipo_persona,
    td.id AS tipo_documento_id,
    td.codigo,
    td.nombre AS tipo_documento,
    td.es_obligatorio,
    COALESCE(dc.estado, 'Pendiente') AS estado_documento,
    dc.documento_id,
    d.nombre_original,
    d.fecha_subida,
    dc.observaciones
FROM tb_inteletgroup_prospectos p
CROSS JOIN tb_inteletgroup_tipos_documento td
LEFT JOIN tb_inteletgroup_documento_checklist dc 
    ON dc.prospecto_id = p.id AND dc.tipo_documento_id = td.id
LEFT JOIN tb_inteletgroup_documentos d 
    ON d.id = dc.documento_id
WHERE td.tipo_persona = p.tipo_persona OR td.tipo_persona = 'Ambos'
    AND td.estado = 'Activo'
ORDER BY p.id, td.orden;