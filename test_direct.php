<?php
/**
 * Test directo del endpoint
 */

// Simular una petición POST
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['HTTP_ACCEPT'] = 'application/json';

// Simular el contenido del body
$input_data = json_encode(['tipo_persona' => 'Natural']);

// Crear un stream temporal con los datos
$temp = fopen('php://temp', 'r+');
fwrite($temp, $input_data);
rewind($temp);

// Redirigir php://input al stream temporal
stream_wrapper_unregister("php");
stream_wrapper_register("php", "MockPhpStream");

class MockPhpStream {
    protected $position = 0;
    protected static $data;
    
    public function stream_open($path, $mode, $options, &$opened_path) {
        if ($path === 'php://input') {
            global $temp;
            self::$data = stream_get_contents($temp);
            rewind($temp);
        }
        return true;
    }
    
    public function stream_read($count) {
        $ret = substr(self::$data, $this->position, $count);
        $this->position += strlen($ret);
        return $ret;
    }
    
    public function stream_eof() {
        return $this->position >= strlen(self::$data);
    }
    
    public function stream_stat() {
        return [];
    }
    
    public function stream_tell() {
        return $this->position;
    }
}

// Capturar la salida
ob_start();

// Incluir el endpoint
require_once __DIR__ . '/endpoints/obtener_tipos_documento.php';

$output = ob_get_clean();

// Restaurar el wrapper original
stream_wrapper_restore("php");

// Mostrar resultado
echo "<h2>Test del endpoint obtener_tipos_documento.php</h2>";
echo "<h3>Salida:</h3>";
echo "<pre>" . htmlspecialchars($output) . "</pre>";

// Decodificar JSON
$result = json_decode($output, true);
if ($result) {
    echo "<h3>Resultado decodificado:</h3>";
    echo "<pre>" . print_r($result, true) . "</pre>";
} else {
    echo "<p style='color: red;'>Error al decodificar JSON: " . json_last_error_msg() . "</p>";
}

// Verificar si se creó el archivo debug.log
$debug_log = __DIR__ . '/endpoints/debug.log';
if (file_exists($debug_log)) {
    echo "<h3>Contenido de debug.log:</h3>";
    echo "<pre>" . htmlspecialchars(file_get_contents($debug_log)) . "</pre>";
}
?>