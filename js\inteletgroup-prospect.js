// JavaScript para el formulario de prospectos InteletGroup

// Variables globales
let currentUserName = '';
let currentUserId = 0;
let documentChecklist = {}; // Para almacenar el checklist de documentos
let uploadedFiles = {}; // Para almacenar los archivos subidos por tipo de documento
let modalInstance = null; // Para mantener una única instancia del modal

// Sistema de protección anti-doble envío (mutex)
let isSubmitting = false; // Flag principal de control

// IMPORTANTE: Creamos un objeto de mutex para almacenar un timestamp de bloqueo
// Esto garantiza una protección a nivel de página que persiste incluso si las variables se reinician
window.prospectMutex = {
    locked: false,
    timestamp: 0,
    lock: function() {
        this.locked = true;
        this.timestamp = Date.now();
        console.log('[MUTEX] 🔒 Mutex BLOQUEADO:', this.timestamp);
        return true;
    },
    unlock: function() {
        const wasLocked = this.locked;
        this.locked = false;
        console.log('[MUTEX] 🔓 Mutex DESBLOQUEADO:', wasLocked);
        return wasLocked;
    },
    isLocked: function() {
        // Autoliberación después de 30 segundos por seguridad
        if (this.locked && (Date.now() - this.timestamp) > 30000) {
            console.log('[MUTEX] ⚠️ Auto-liberando mutex después de 30s');
            this.locked = false;
        }
        return this.locked;
    }
};

// Inicialización cuando el DOM está listo
document.addEventListener('DOMContentLoaded', function() {
    console.log('[MONITOR] DOMContentLoaded: Iniciando configuración');
    
    // Resetear variables globales al cargar la página
    isSubmitting = false;
    formSubmissionInProgress = false;
    lastFormData = null;
    lastSubmissionTime = 0;
    submissionCount = 0;
    console.log('[MONITOR] Variables globales de control reseteadas al inicio');
    
    initializeInteletGroupProspectForm();
    
    // Prevenir notificaciones duplicadas de otros scripts
    setupNotificationPrevention();
    
    // Configurar monitoreo global de clics
    setupGlobalClickMonitoring();
    
    console.log('[MONITOR] Inicialización DOMContentLoaded completada');
});

// Monitoreo global de eventos de clic
function setupGlobalClickMonitoring() {
    console.log('[MONITOR] Configurando monitoreo global de clics');
    
    // Usar captura para interceptar eventos antes que los manejadores normales
    document.addEventListener('click', function(event) {
        const isSaveButton = event.target.id === 'saveInteletGroupProspectBtn' || 
                            (event.target.closest && event.target.closest('#saveInteletGroupProspectBtn'));
        
        if (isSaveButton) {
            console.log('=== [MONITOR-GLOBAL] CLIC DETECTADO EN BOTÓN DE GUARDAR ===');
            console.log('[MONITOR-GLOBAL] Timestamp:', new Date().toISOString());
            console.log('[MONITOR-GLOBAL] isSubmitting:', isSubmitting);
            console.log('[MONITOR-GLOBAL] formSubmissionInProgress:', formSubmissionInProgress);
            
            // Si ya está en proceso, bloquear este clic
            if (isSubmitting || formSubmissionInProgress) {
                console.log('[MONITOR-GLOBAL] ❌ BLOQUEANDO CLIC - Formulario ya en proceso de envío');
                event.preventDefault();
                event.stopPropagation();
                event.stopImmediatePropagation();
                
                // Reforzar deshabilitación del botón
                const btn = event.target.closest('#saveInteletGroupProspectBtn');
                if (btn) {
                    console.log('[MONITOR-GLOBAL] Reforzando deshabilitación del botón');
                    btn.disabled = true;
                    btn.style.pointerEvents = 'none';
                }
                
                return false;
            } else {
                console.log('[MONITOR-GLOBAL] ✓ Clic permitido - No hay envío en progreso');
                // NO establecer isSubmitting aquí, dejar que el botón lo maneje
                console.log('[MONITOR-GLOBAL] Permitiendo que el botón maneje el evento');
            }
        }
    }, true); // true para fase de captura
}

// Función principal de inicialización
function initializeInteletGroupProspectForm() {
    console.log('====== INICIALIZACIÓN DEL FORMULARIO INTELETGROUP ======');
    console.log('[MONITOR] Verificando estado inicial de variables de control:');
    console.log('[MONITOR] isSubmitting inicial:', isSubmitting);
    console.log('[MONITOR] formSubmissionInProgress inicial:', formSubmissionInProgress);
    console.log('[MONITOR] lastSubmissionTime inicial:', lastSubmissionTime);

    // Resetear explícitamente las banderas de control al inicio
    isSubmitting = false;
    formSubmissionInProgress = false;
    lastSubmissionTime = 0;
    lastFormData = null;
    submissionCount = 0;
    
    // Resetear mutex también
    if (window.prospectMutex) {
        window.prospectMutex.unlock();
        console.log('[MONITOR] Mutex reseteado');
    }
    
    console.log('[MONITOR] Variables de control reseteadas explícitamente');
    
    // Obtener información del usuario actual
    if (typeof window.currentUserName !== 'undefined') {
        currentUserName = window.currentUserName;
    }
    if (typeof window.currentUserId !== 'undefined') {
        currentUserId = window.currentUserId;
    }

    // Log de inicialización
    console.log('=== INICIALIZACIÓN INTELETGROUP ===');
    console.log('currentUserName inicializado:', currentUserName);
    console.log('currentUserId inicializado:', currentUserId);
    console.log('window.currentUserName:', window.currentUserName);
    console.log('window.currentUserId:', window.currentUserId);

    // Configurar eventos
    console.log('[MONITOR] Configurando eventos del formulario...');
    setupFormEvents();
    console.log('[MONITOR] Configurando validación...');
    setupValidation();
    console.log('[MONITOR] Configurando subida de archivos...');
    setupFileUpload();

    // Configurar evento del tipo de persona con delegación de eventos
    console.log('Llamando a setupTipoPersonaEvent...');
    setupTipoPersonaEvent();
    console.log('setupTipoPersonaEvent llamada completada');
    
    // Agregar monitoreo de eventos de clic a nivel de documento
    console.log('[MONITOR] Configurando monitor de eventos a nivel de documento');
    document.addEventListener('click', function(event) {
        // Monitorear clics en o cerca del botón de guardar
        if (event.target && (event.target.id === 'saveInteletGroupProspectBtn' || 
                            event.target.closest('#saveInteletGroupProspectBtn'))) {
            console.log('[MONITOR-GLOBAL] CLIC DETECTADO EN O DENTRO DEL BOTÓN DE GUARDAR');
            console.log('[MONITOR-GLOBAL] Target exacto:', event.target.tagName, event.target.id || 'sin ID');
            console.log('[MONITOR-GLOBAL] Estado actual - isSubmitting:', isSubmitting);
            console.log('[MONITOR-GLOBAL] Estado actual - formSubmissionInProgress:', formSubmissionInProgress);
            console.log('[MONITOR-GLOBAL] Timestamp:', new Date().toISOString());
        }
    }, true); // Usar fase de captura para detectar antes de otros handlers
    
    console.log('====== INICIALIZACIÓN COMPLETADA ======');
}

// Configurar eventos del formulario
function setupFormEvents() {
    // Botón de guardar
    const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
    console.log('[MONITOR] Iniciando configuración de eventos del botón de guardar');
    console.log('[MONITOR] Estado inicial isSubmitting:', isSubmitting);
    console.log('[MONITOR] Estado inicial formSubmissionInProgress:', formSubmissionInProgress);
    
    if (saveBtn) {
        console.log('[MONITOR] Botón encontrado, configurando eventos...');
        // Remover cualquier listener anterior
        console.log('[MONITOR] Reemplazando botón para eliminar listeners antiguos');
        saveBtn.replaceWith(saveBtn.cloneNode(true));
        const newSaveBtn = document.getElementById('saveInteletGroupProspectBtn');
        console.log('[MONITOR] Botón clonado obtenido:', newSaveBtn ? 'SI' : 'NO');
        
        // Agregar un único event listener con mucho logging
        newSaveBtn.addEventListener('click', function(event) {
            console.log('===== CLICK EN BOTÓN GUARDAR DETECTADO =====');
            console.log('[MONITOR] Detalles del evento:', event.type, 'Target:', event.target.id);
            console.log('[MONITOR] Estado de flags - isSubmitting:', isSubmitting);
            console.log('[MONITOR] Estado de flags - formSubmissionInProgress:', formSubmissionInProgress);
            console.log('[MONITOR] Estado del botón - disabled:', newSaveBtn.disabled);
            console.log('[MONITOR] timeStamp del evento:', event.timeStamp);
            console.log('[MONITOR] Estado de propagación - bubbles:', event.bubbles, 'cancelable:', event.cancelable);
            
            // Registrar stack trace para ver desde dónde se llamó
            console.log('[MONITOR] Stack trace:', new Error().stack);
            
            event.preventDefault();
            console.log('[MONITOR] event.preventDefault() llamado');
            event.stopPropagation();
            console.log('[MONITOR] event.stopPropagation() llamado');

            // Deshabilitar temporalmente el botón para prevenir múltiples clicks
            if (newSaveBtn.disabled) {
                console.log('[MONITOR] BLOQUEO: Botón ya deshabilitado físicamente');
                return false;
            }
            
            if (isSubmitting) {
                console.log('[MONITOR] BLOQUEO: isSubmitting ya está en true');
                return false;
            }
            
            if (formSubmissionInProgress) {
                console.log('[MONITOR] BLOQUEO: formSubmissionInProgress ya está en true');
                return false;
            }
            
            // Protección contra doble clic basada en timestamp
            const lastClick = parseInt(newSaveBtn.getAttribute('data-lastclick') || '0');
            const now = Date.now();
            if (now - lastClick < 1000) { // 1 segundo de protección
                console.log('[MONITOR] BLOQUEO: Detectado posible doble clic');
                return false;
            }
            newSaveBtn.setAttribute('data-lastclick', now.toString());
            
            console.log('[MONITOR] Todas las comprobaciones pasadas, procediendo con handleSaveProspect()');
            
            // Deshabilitar botón inmediatamente ANTES de establecer banderas
            newSaveBtn.disabled = true;
            newSaveBtn.style.pointerEvents = 'none';
            console.log('[MONITOR] Botón deshabilitado');
            
            // Ahora sí, llamar a handleSaveProspect directamente
            // NO establecemos las banderas aquí, dejar que handleSaveProspect las maneje
            console.log('[MONITOR] Llamando a handleSaveProspect()...');
            handleSaveProspect();
            
            return false;
        });
    }

    // Prevenir envío por Enter en el formulario
    const form = document.getElementById('inteletGroupProspectForm');
    if (form) {
        console.log('[MONITOR] Configurando prevención de envío del formulario por Enter');
        form.removeEventListener('submit', preventFormSubmit);
        form.addEventListener('submit', preventFormSubmit);
        
        // Agregar captura de todos los eventos del formulario para depuración
        console.log('[MONITOR] Configurando monitoreo de eventos del formulario');
        ['submit', 'keypress', 'keydown'].forEach(eventType => {
            form.addEventListener(eventType, function(event) {
                if (event.type === 'keydown' || event.type === 'keypress') {
                    if (event.key === 'Enter') {
                        console.log(`[MONITOR-FORM] Tecla Enter detectada en evento ${event.type}`);
                        console.log('[MONITOR-FORM] Target del evento:', event.target.tagName, event.target.id || 'sin ID');
                        console.log('[MONITOR-FORM] isSubmitting:', isSubmitting);
                        console.log('[MONITOR-FORM] formSubmissionInProgress:', formSubmissionInProgress);
                    }
                } else {
                    console.log(`[MONITOR-FORM] Evento ${event.type} detectado en el formulario`);
                    console.log('[MONITOR-FORM] Target del evento:', event.target.tagName, event.target.id || 'sin ID');
                    console.log('[MONITOR-FORM] isSubmitting:', isSubmitting);
                    console.log('[MONITOR-FORM] formSubmissionInProgress:', formSubmissionInProgress);
                }
            });
        });
    }

    // Botón de llenar datos de prueba
    const fillTestBtn = document.getElementById('fillTestDataBtn');
    if (fillTestBtn) {
        fillTestBtn.removeEventListener('click', fillTestData);
        fillTestBtn.addEventListener('click', fillTestData);
    }

    // Evento cuando se abre el modal - usar once para evitar duplicados
    const modal = document.getElementById('inteletGroupProspectModal');
    console.log('Modal encontrado:', modal);
    if (modal) {
        console.log('Registrando evento show.bs.modal');
        // Usar eventos nativos de Bootstrap en lugar de jQuery
        modal.removeEventListener('show.bs.modal', handleModalShow);
        modal.addEventListener('show.bs.modal', handleModalShow);
    } else {
        console.log('Modal inteletGroupProspectModal no encontrado');
    }
}

// Función auxiliar para prevenir el envío del formulario
function preventFormSubmit(event) {
    console.log('[MONITOR-PREVENT] Intento de envío de formulario detectado!');
    console.log('[MONITOR-PREVENT] Tipo de evento:', event.type);
    console.log('[MONITOR-PREVENT] Target:', event.target.tagName, event.target.id || 'sin ID');
    console.log('[MONITOR-PREVENT] Estado isSubmitting:', isSubmitting);
    console.log('[MONITOR-PREVENT] Estado formSubmissionInProgress:', formSubmissionInProgress);
    console.log('[MONITOR-PREVENT] Key (si aplica):', event.key || 'N/A');
    
    // Establecer banderas de control inmediatamente
    if (!isSubmitting) {
        console.log('[MONITOR-PREVENT] Estableciendo isSubmitting = true preventivamente');
        isSubmitting = true;
    }
    
    event.preventDefault();
    console.log('[MONITOR-PREVENT] event.preventDefault() ejecutado');
    
    event.stopPropagation();
    console.log('[MONITOR-PREVENT] event.stopPropagation() ejecutado');
    
    event.stopImmediatePropagation();
    console.log('[MONITOR-PREVENT] event.stopImmediatePropagation() ejecutado');
    
    // Desactivar el botón de guardar durante un breve período
    const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
    if (saveBtn) {
        console.log('[MONITOR-PREVENT] Deshabilitando botón de guardar temporalmente');
        saveBtn.disabled = true;
        saveBtn.style.pointerEvents = 'none';
        
        setTimeout(() => {
            console.log('[MONITOR-PREVENT] Rehabilitando botón después de 500ms');
            saveBtn.disabled = false;
            saveBtn.style.pointerEvents = 'auto';
            isSubmitting = false;
            console.log('[MONITOR-PREVENT] isSubmitting restablecido a false');
        }, 500);
    }
    
    return false;
}

// Función para manejar cuando se muestra el modal
function handleModalShow() {
    console.log('====== APERTURA DEL MODAL ======');
    console.log('[MONITOR] Modal show.bs.modal disparado');
    console.log('[MONITOR] Estado actual - isSubmitting:', isSubmitting);
    console.log('[MONITOR] Estado actual - formSubmissionInProgress:', formSubmissionInProgress);
    
    // Asegurar que las banderas de control estén reseteadas al abrir el modal
    isSubmitting = false;
    formSubmissionInProgress = false;
    lastSubmissionTime = 0;
    lastFormData = null;
    console.log('[MONITOR] Banderas reseteadas explícitamente al abrir modal');
    
    resetForm();
    populateExecutiveName();

    // Registrar evento para cambio de tipo de persona
    const tipoPersonaField = document.getElementById('tipo_persona');
    if (tipoPersonaField) {
        console.log('[MONITOR] Campo tipo_persona encontrado, registrando evento change');
        // Remover listener anterior si existe
        tipoPersonaField.removeEventListener('change', handleTipoPersonaChange);
        // Agregar nuevo listener
        tipoPersonaField.addEventListener('change', handleTipoPersonaChange);
    } else {
        console.log('[MONITOR] Campo tipo_persona NO encontrado');
    }
    
    // Verificar estado del botón después de abrir el modal
    const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
    if (saveBtn) {
        console.log('[MONITOR] Botón de guardar en modal - Estado:', saveBtn.disabled ? 'deshabilitado' : 'habilitado');
        console.log('[MONITOR] Botón de guardar - HTML:', saveBtn.outerHTML);
        
        // Asegurar que el botón esté habilitado inicialmente
        saveBtn.disabled = false;
        saveBtn.style.pointerEvents = 'auto';
        console.log('[MONITOR] Botón de guardar habilitado explícitamente');
    }
}

// Función para manejar el cambio de tipo de persona
function handleTipoPersonaChange() {
    console.log('Tipo de persona cambiado a:', this.value);
    loadDocumentChecklist(this.value);
}

// Configurar evento del tipo de persona usando delegación de eventos
function setupTipoPersonaEvent() {
    console.log('=== setupTipoPersonaEvent ===');

    // Usar delegación de eventos en el documento para capturar cambios en el select
    document.addEventListener('change', function(event) {
        if (event.target && event.target.id === 'tipo_persona') {
            console.log('Evento change detectado en tipo_persona:', event.target.value);
            loadDocumentChecklist(event.target.value);
        }
    });

    console.log('Evento de delegación registrado para tipo_persona');
}

// Configurar validación en tiempo real
function setupValidation() {
    const form = document.getElementById('inteletGroupProspectForm');
    if (!form) return;

    // RUT validation and formatting
    const rutField = document.getElementById('rut_cliente');
    if (rutField) {
        rutField.addEventListener('blur', function() {
            formatRUT(this);
            validateRUT(this);
            checkAndEnableSubmitButton();
        });
        rutField.addEventListener('input', function() {
            clearFieldError(this);
            checkAndEnableSubmitButton();
        });
    }

    // Razón Social validation and uppercase
    const razonSocialField = document.getElementById('razon_social');
    if (razonSocialField) {
        razonSocialField.addEventListener('blur', function() {
            this.value = this.value.toUpperCase();
            validateRazonSocial(this);
            checkAndEnableSubmitButton();
        });
        razonSocialField.addEventListener('input', function() {
            clearFieldError(this);
            checkAndEnableSubmitButton();
        });
    }

    // Campos de texto que deben ser mayúsculas
    const uppercaseFields = ['razon_social', 'rubro', 'direccion_comercial', 'dias_atencion'];
    uppercaseFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            // Transformar a mayúsculas mientras se escribe
            field.addEventListener('input', function() {
                const cursorPos = this.selectionStart;
                this.value = this.value.toUpperCase();
                this.setSelectionRange(cursorPos, cursorPos);
            });
            
            // También en blur por si acaso
            field.addEventListener('blur', function() {
                this.value = this.value.toUpperCase();
            });
        }
    });

    // Teléfono validation
    const telefonoField = document.getElementById('telefono_celular');
    if (telefonoField) {
        telefonoField.addEventListener('input', function() {
            validateTelefono(this);
            checkAndEnableSubmitButton();
        });
        telefonoField.addEventListener('blur', function() {
            checkAndEnableSubmitButton();
        });
    }

    // Email validation
    const emailField = document.getElementById('email');
    if (emailField) {
        emailField.addEventListener('input', function() {
            validateEmail(this);
            checkAndEnableSubmitButton();
        });
        emailField.addEventListener('blur', function() {
            checkAndEnableSubmitButton();
        });
    }

    // Validación general para campos requeridos
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        field.addEventListener('blur', function() {
            validateRequired(this);
            checkAndEnableSubmitButton();
        });

        field.addEventListener('input', function() {
            // Limpiar errores mientras se escribe
            clearFieldError(this);
            checkAndEnableSubmitButton();
        });
    });
}

// Función para limpiar errores de un campo específico
function clearFieldError(field) {
    if (!field) return;

    field.classList.remove('is-invalid');
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    if (feedback) {
        feedback.style.display = 'none';
    }
}

// Función para verificar si el botón de envío debe estar habilitado
function checkAndEnableSubmitButton() {
    // Solo verificar si no hay un envío en progreso
    if (isSubmitting || formSubmissionInProgress) {
        return;
    }

    const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
    if (!saveBtn) return;

    // Si el botón está deshabilitado por un error de validación previo, verificar si ahora es válido
    if (saveBtn.disabled) {
        const form = document.getElementById('inteletGroupProspectForm');
        if (form) {
            // Hacer una validación rápida sin mostrar errores
            const isValid = validateFormQuietly();
            if (isValid) {
                console.log('[MONITOR] Formulario ahora es válido, re-habilitando botón');
                saveBtn.disabled = false;
                saveBtn.style.pointerEvents = 'auto';
                setLoadingState(saveBtn, false);
            }
        }
    }
}

// Función para validar el formulario sin mostrar errores (para verificación silenciosa)
function validateFormQuietly() {
    const form = document.getElementById('inteletGroupProspectForm');
    if (!form) return false;

    const requiredFields = form.querySelectorAll('[required]');

    for (let field of requiredFields) {
        if (!field.value.trim()) {
            return false;
        }
    }

    // Validaciones específicas sin mostrar errores
    const rutField = document.getElementById('rut_cliente');
    if (rutField && rutField.value && !isValidRUT(rutField.value)) {
        return false;
    }

    const telefonoField = document.getElementById('telefono_celular');
    if (telefonoField && telefonoField.value && !isValidTelefono(telefonoField.value)) {
        return false;
    }

    const emailField = document.getElementById('email');
    if (emailField && emailField.value && !isValidEmail(emailField.value)) {
        return false;
    }

    return true;
}

// Funciones auxiliares para validación silenciosa (solo validan el valor, no muestran errores)
function isValidRUT(value) {
    if (!value) return true; // Campo vacío es válido para validación silenciosa
    const rutPattern = /^\d{7,8}-[\dkK]$/;
    if (!rutPattern.test(value)) return false;

    // Validar dígito verificador
    const parts = value.split('-');
    const rut = parts[0];
    const dv = parts[1].toUpperCase();

    let suma = 0;
    let multiplicador = 2;

    for (let i = rut.length - 1; i >= 0; i--) {
        suma += parseInt(rut.charAt(i)) * multiplicador;
        multiplicador = multiplicador === 7 ? 2 : multiplicador + 1;
    }

    const resto = suma % 11;
    const dvCalculado = resto === 0 ? '0' : resto === 1 ? 'K' : (11 - resto).toString();

    return dv === dvCalculado;
}

function isValidTelefono(value) {
    if (!value) return true; // Campo vacío es válido para validación silenciosa
    const pattern = /^\d{9,15}$/;
    return pattern.test(value);
}

function isValidEmail(value) {
    if (!value) return true; // Campo vacío es válido para validación silenciosa
    const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return pattern.test(value);
}

// Configurar subida de archivos
function setupFileUpload() {
    const fileInput = document.getElementById('documentos');
    if (!fileInput) return;

    fileInput.addEventListener('change', function() {
        validateFiles(this);
    });
}

// Formatear RUT automáticamente
function formatRUT(field) {
    // Guardar posición del cursor
    const cursorPos = field.selectionStart;
    let value = field.value;
    
    // Eliminar todo excepto números y K
    value = value.replace(/[^0-9kK]/g, '');
    
    // Convertir k minúscula a K mayúscula
    value = value.replace(/k/g, 'K');
    
    if (value.length >= 2) {
        // Si tiene más de 8 caracteres antes del guión, tomar solo los primeros 9
        if (value.length > 9) {
            value = value.slice(0, 9);
        }
        
        // Insertar el guión antes del último carácter
        if (value.length >= 8) {
            const numero = value.slice(0, -1);
            const dv = value.slice(-1);
            value = numero + '-' + dv;
        }
    }
    
    field.value = value;
    
    // Restaurar posición del cursor ajustada
    if (cursorPos <= value.length) {
        field.setSelectionRange(cursorPos, cursorPos);
    }
}

// Validar RUT
function validateRUT(field) {
    const value = field.value.trim();
    const rutPattern = /^\d{7,8}-[\dkK]$/;
    
    if (value === '') {
        setFieldState(field, 'neutral');
        return true;
    }
    
    if (!rutPattern.test(value)) {
        setFieldState(field, 'invalid', 'Formato inválido. Use: ********-9');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar Razón Social
function validateRazonSocial(field) {
    const value = field.value.trim();
    // Permitir letras mayúsculas, números, espacios y algunos caracteres especiales comunes en nombres de empresas
    const pattern = /^[A-Z0-9\s\.\-\&]+$/;
    
    if (value === '') {
        setFieldState(field, 'neutral');
        return true;
    }
    
    if (!pattern.test(value)) {
        setFieldState(field, 'invalid', 'Solo letras mayúsculas, números, espacios y caracteres: . - &');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar teléfono
function validateTelefono(field) {
    const value = field.value.trim();
    const pattern = /^\d{9,15}$/;
    
    if (value === '') {
        setFieldState(field, 'neutral');
        return true;
    }
    
    if (!pattern.test(value)) {
        setFieldState(field, 'invalid', 'Solo números, mínimo 9 dígitos');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar email
function validateEmail(field) {
    const value = field.value.trim();
    const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (value === '') {
        setFieldState(field, 'neutral');
        return true;
    }
    
    if (!pattern.test(value)) {
        setFieldState(field, 'invalid', 'Formato de email inválido');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar campos requeridos
function validateRequired(field) {
    const value = field.value.trim();
    
    if (value === '') {
        setFieldState(field, 'invalid', 'Este campo es requerido');
        return false;
    }
    
    setFieldState(field, 'valid');
    return true;
}

// Validar archivos
function validateFiles(fileInput) {
    const files = fileInput.files;
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    
    let isValid = true;
    let errorMessage = '';
    
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        if (file.size > maxSize) {
            isValid = false;
            errorMessage = `El archivo ${file.name} excede el tamaño máximo de 5MB`;
            break;
        }
        
        if (!allowedTypes.includes(file.type)) {
            isValid = false;
            errorMessage = `El archivo ${file.name} no tiene un formato permitido`;
            break;
        }
    }
    
    if (!isValid) {
        setFieldState(fileInput, 'invalid', errorMessage);
        fileInput.value = '';
    } else {
        setFieldState(fileInput, 'valid');
    }
    
    return isValid;
}

// Establecer estado del campo
function setFieldState(field, state, message = '') {
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    
    // Limpiar clases anteriores
    field.classList.remove('is-valid', 'is-invalid');
    
    switch (state) {
        case 'valid':
            field.classList.add('is-valid');
            if (feedback) feedback.textContent = '';
            break;
        case 'invalid':
            field.classList.add('is-invalid');
            if (feedback) feedback.textContent = message;
            break;
        case 'neutral':
            if (feedback) feedback.textContent = '';
            break;
    }
}

// Poblar nombre del ejecutivo
function populateExecutiveName() {
    console.log('=== DEBUG populateExecutiveName ===');
    console.log('currentUserName:', currentUserName);
    console.log('window.currentUserName:', window.currentUserName);

    const nameField = document.getElementById('nombre_ejecutivo');
    console.log('nameField encontrado:', nameField);

    if (nameField) {
        // Intentar múltiples fuentes para el nombre del usuario
        let userName = currentUserName ||
                      window.currentUserName ||
                      (window.currentUserId ? 'JOHANNA LISSETE RIGO ESPINOZA' : '') ||
                      'Usuario Ejecutivo';

        console.log('userName a usar:', userName);

        // Forzar el llenado del campo
        nameField.value = userName;
        nameField.setAttribute('value', userName);

        // Disparar evento para asegurar que se registre el cambio
        nameField.dispatchEvent(new Event('input', { bubbles: true }));
        nameField.dispatchEvent(new Event('change', { bubbles: true }));

        console.log('Campo nombre_ejecutivo llenado con:', userName);
        console.log('Valor actual del campo:', nameField.value);
    } else {
        console.log('Campo nombre_ejecutivo no encontrado');
    }
}

// Función para generar un RUT único para pruebas
function generateUniqueTestRut() {
    // Generar un número aleatorio entre ******** y 99999999
    const randomNumber = Math.floor(Math.random() * (99999999 - ******** + 1)) + ********;

    // Calcular dígito verificador
    let sum = 0;
    let multiplier = 2;
    const rutString = randomNumber.toString();

    for (let i = rutString.length - 1; i >= 0; i--) {
        sum += parseInt(rutString[i]) * multiplier;
        multiplier = multiplier === 7 ? 2 : multiplier + 1;
    }

    const remainder = sum % 11;
    const dv = remainder < 2 ? remainder.toString() : (11 - remainder === 10 ? 'K' : (11 - remainder).toString());

    return `${randomNumber}-${dv}`;
}

// Llenar datos de prueba
function fillTestData() {
    const uniqueRut = generateUniqueTestRut();
    const testData = {
        tipo_persona: 'Natural',
        rut_cliente: uniqueRut,
        razon_social: 'EMPRESA EJEMPLO LTDA',
        rubro: 'COMERCIO AL POR MENOR',
        direccion_comercial: 'AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO',
        telefono_celular: '*********',
        email: '<EMAIL>',
        numero_pos: 'POS123456',
        tipo_cuenta: 'Cuenta Corriente',
        numero_cuenta_bancaria: '********',
        dias_atencion: 'LUNES A VIERNES',
        horario_atencion: '09:00 - 18:00',
        contrata_boleta: 'Si',
        competencia_actual: 'Transbank'
    };
    
    Object.keys(testData).forEach(key => {
        const field = document.getElementById(key);
        if (field) {
            field.value = testData[key];
            // Trigger validation and formatting
            field.dispatchEvent(new Event('input'));
            field.dispatchEvent(new Event('blur'));
        }
    });
    
    showMessage('info', 'Datos de prueba cargados correctamente');
}

// Validar todo el formulario
function validateForm() {
    const form = document.getElementById('inteletGroupProspectForm');
    if (!form) return false;
    
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!validateRequired(field)) {
            isValid = false;
        }
    });
    
    // Validaciones específicas
    const rutField = document.getElementById('rut_cliente');
    if (rutField && !validateRUT(rutField)) {
        isValid = false;
    }
    
    const razonSocialField = document.getElementById('razon_social');
    if (razonSocialField && !validateRazonSocial(razonSocialField)) {
        isValid = false;
    }
    
    const telefonoField = document.getElementById('telefono_celular');
    if (telefonoField && !validateTelefono(telefonoField)) {
        isValid = false;
    }
    
    const emailField = document.getElementById('email');
    if (emailField && !validateEmail(emailField)) {
        isValid = false;
    }
    
    return isValid;
}

// Variables globales para controlar envíos múltiples y prevenir duplicados
// isSubmitting ya está definido como variable global al inicio del archivo
let lastFormData = null;
let lastSubmissionTime = 0;
const MINIMUM_SUBMISSION_INTERVAL = 2000; // 2 segundos mínimo entre envíos
let submissionAbortController = null;
let submissionCount = 0; // Contador de envíos para debugging
let formSubmissionInProgress = false; // Flag adicional para prevenir múltiples envíos

// Manejar guardado del prospecto
async function handleSaveProspect() {
    console.log('====== INICIO FUNCIÓN handleSaveProspect ======');
    console.log('[MONITOR] Estado actual isSubmitting:', isSubmitting);
    console.log('[MONITOR] Estado actual formSubmissionInProgress:', formSubmissionInProgress);
    console.log('[MONITOR] lastSubmissionTime:', lastSubmissionTime);
    console.log('[MONITOR] MINIMUM_SUBMISSION_INTERVAL:', MINIMUM_SUBMISSION_INTERVAL);
    
    // Declarar saveBtn una sola vez al inicio de la función
    const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
    console.log('[MONITOR] Botón de guardar encontrado:', saveBtn ? 'SI' : 'NO');
    
    // 1. Verificar si ya hay un envío en progreso (CRÍTICO para prevenir doble clic)
    // Usamos una variable estática interna para mayor seguridad
    if (!handleSaveProspect.isFirstRun) {
        handleSaveProspect.isFirstRun = true;
        console.log('[MONITOR] ✔️ Primera ejecución de handleSaveProspect()');
    } else {
        console.log('[MONITOR] ❌ ALERTA: Posible ejecución múltiple de handleSaveProspect()');
    }
    
    // MUTEX: Verificación de bloqueo a nivel de ventana
    // Esta es nuestra primera y más importante barrera de seguridad
    if (window.prospectMutex && window.prospectMutex.isLocked()) {
        submissionCount++;
        console.log(`[MUTEX] ❌ Envío #${submissionCount} BLOQUEADO - Mutex activo`);
        console.log(`[MUTEX] Tiempo desde bloqueo: ${Date.now() - window.prospectMutex.timestamp}ms`);
        
        // Deshabilitar el botón para mayor seguridad
        if (saveBtn) {
            console.log('[MUTEX] Reforzando deshabilitación del botón');
            saveBtn.disabled = true;
            saveBtn.style.pointerEvents = 'none';
        }
        
        return;
    }
    
    // Las banderas se establecen AQUI, no en el botón
    // Primero verificamos si ya están establecidas para prevenir doble envío
    if (isSubmitting || formSubmissionInProgress) {
        submissionCount++;
        console.log(`[MONITOR] ❌ Envío #${submissionCount} BLOQUEADO - Ya hay un envío en progreso`);
        console.log('[MONITOR] isSubmitting:', isSubmitting, 'formSubmissionInProgress:', formSubmissionInProgress);
        return;
    }
    
    // NOTA: Eliminamos la verificación del botón deshabilitado porque
    // el botón se deshabilita ANTES de llamar a esta función como medida de seguridad
    console.log('[MONITOR] Botón deshabilitado:', saveBtn ? saveBtn.disabled : 'No encontrado');
    
    // ACTIVAR MUTEX: Si hemos llegado aquí, es seguro proceder
    if (window.prospectMutex) {
        window.prospectMutex.lock();
        console.log('[MUTEX] Mutex activado para este proceso de guardado');
    }

    // 2. Verificar intervalo mínimo entre envíos
    const currentTime = Date.now();
    console.log('[MONITOR] Tiempo actual:', currentTime);
    console.log('[MONITOR] Tiempo desde último envío:', currentTime - lastSubmissionTime, 'ms');
    
    if (currentTime - lastSubmissionTime < MINIMUM_SUBMISSION_INTERVAL) {
        const remainingTime = Math.ceil((MINIMUM_SUBMISSION_INTERVAL - (currentTime - lastSubmissionTime)) / 1000);
        submissionCount++;
        console.log(`[MONITOR] ❌ Envío #${submissionCount} BLOQUEADO - debe esperar ${remainingTime} segundo(s)`);
        showMessage('error', `Por favor, espere ${remainingTime} segundo(s) antes de enviar nuevamente.`);
        return;
    }

    console.log('[MONITOR] ✓ Verificación de tiempo entre envíos: PASADA');

    // 3. ESTABLECER BANDERAS DE CONTROL INMEDIATAMENTE para prevenir doble envío
    console.log('[MONITOR] ⚠️ PUNTO CRÍTICO: ESTABLECIENDO FLAGS DE CONTROL ANTES DE VALIDACIÓN');
    try {
        console.log('[MONITOR] Estableciendo banderas de control...');
        isSubmitting = true;
        formSubmissionInProgress = true;
        console.log('[MONITOR] isSubmitting establecido a:', isSubmitting);
        console.log('[MONITOR] formSubmissionInProgress establecido a:', formSubmissionInProgress);

        // NO actualizar lastSubmissionTime aquí - se hará después de la verificación de duplicados
        console.log('[MONITOR] lastSubmissionTime se actualizará después de verificar duplicados');
    } catch (e) {
        console.error('[MONITOR] ⚠️ ERROR al configurar flags:', e);
    }

    // 4. Validar formulario en el cliente
    console.log('[MONITOR] Iniciando validación del formulario...');
    const isFormValid = validateForm();
    console.log('[MONITOR] Resultado de validación del formulario:', isFormValid ? 'VÁLIDO' : 'INVÁLIDO');

    if (!isFormValid) {
        // Limpiar cualquier mensaje previo
        console.log('[MONITOR] ❌ Formulario inválido, mostrando mensaje de error');
        clearAllMessages();
        // Mostrar solo en el modal, no como notificación flotante
        showMessage('error', 'Por favor, corrija los errores marcados en el formulario.');

        // IMPORTANTE: Re-habilitar el botón cuando hay errores de validación
        console.log('[MONITOR] Re-habilitando botón debido a error de validación');
        if (saveBtn) {
            saveBtn.disabled = false;
            saveBtn.style.pointerEvents = 'auto';
            setLoadingState(saveBtn, false);
        }

        // Resetear banderas de control
        isSubmitting = false;
        formSubmissionInProgress = false;

        // Liberar mutex si está activo
        if (window.prospectMutex && window.prospectMutex.isLocked()) {
            window.prospectMutex.unlock();
            console.log('[MONITOR] Mutex liberado debido a error de validación');
        }

        console.log('[MONITOR] Estado después de error de validación - isSubmitting:', isSubmitting, 'formSubmissionInProgress:', formSubmissionInProgress);
        return;
    }

    console.log('[MONITOR] ✓ Validación de formulario: PASADA');

    // 5. Configurar estado de "cargando" y prevención de duplicados
    const form = document.getElementById('inteletGroupProspectForm');
    console.log('[MONITOR] Formulario encontrado:', form ? 'SI' : 'NO');

    console.log('[MONITOR] Deshabilitando botón visualmente...');
    if (saveBtn) {
        console.log('[MONITOR] Estado del botón ANTES de setLoadingState - disabled:', saveBtn.disabled);
        setLoadingState(saveBtn, true);
        console.log('[MONITOR] Estado del botón DESPUÉS de setLoadingState - disabled:', saveBtn.disabled);
        
        // Verificación adicional de que el botón esté realmente deshabilitado
        if (!saveBtn.disabled) {
            console.error('[MONITOR] ⚠️ ALERTA: El botón NO fue deshabilitado correctamente');
            saveBtn.disabled = true;
            saveBtn.style.pointerEvents = 'none';
            console.log('[MONITOR] Forzando deshabilitación del botón - nuevo estado:', saveBtn.disabled);
        }
    }

    // Cancelar cualquier envío anterior pendiente
    if (submissionAbortController) {
        console.log('[MONITOR] Abortando controlador de envío anterior');
        submissionAbortController.abort();
    }
    console.log('[MONITOR] Creando nuevo AbortController');
    submissionAbortController = new AbortController();

    // Limpiar mensajes anteriores antes de mostrar el nuevo
    clearAllMessages();
    showMessage('info', 'Guardando prospecto...');

    // 5. Preparar y validar datos del formulario
    try {
        const formData = new FormData(form);
        formData.append('usuario_id', window.currentUserId);
        
        // 6. Verificar si estos EXACTOS datos ya fueron enviados recientemente (PREVENCIÓN MEJORADA)
        const currentFormDataString = JSON.stringify({
            rut_cliente: formData.get('rut_cliente'),
            razon_social: formData.get('razon_social'),
            email: formData.get('email'),
            telefono_celular: formData.get('telefono_celular')
        });

        // Verificar duplicados usando el tiempo ANTERIOR al actual (antes de actualizar lastSubmissionTime)
        const previousSubmissionTime = lastSubmissionTime;
        console.log('[MONITOR] Verificando duplicados - currentTime:', currentTime, 'previousSubmissionTime:', previousSubmissionTime);
        console.log('[MONITOR] Diferencia de tiempo:', currentTime - previousSubmissionTime, 'ms');
        console.log('[MONITOR] lastFormData:', lastFormData);
        console.log('[MONITOR] currentFormDataString:', currentFormDataString);

        // Verificación más estricta: bloquear si los datos son idénticos Y el tiempo es muy corto
        if (lastFormData === currentFormDataString && previousSubmissionTime > 0 && (currentTime - previousSubmissionTime < 5000)) {
            submissionCount++;
            console.log(`[MONITOR] ❌ Envío #${submissionCount} BLOQUEADO - datos idénticos detectados en ventana de 5 segundos`);
            showMessage('error', 'Se detectó un posible doble envío. Por favor, espere un momento antes de intentar nuevamente.');
            // Restaurar estado del botón y flags
            setLoadingState(saveBtn, false);
            isSubmitting = false;
            formSubmissionInProgress = false;
            if (window.prospectMutex) {
                window.prospectMutex.unlock();
            }
            return;
        }

        // Verificación adicional: si el mutex está bloqueado por otro proceso
        if (window.prospectMutex && window.prospectMutex.isLocked() && window.prospectMutex.timestamp !== currentTime) {
            console.log(`[MONITOR] ❌ Envío BLOQUEADO - Mutex ya está activo desde:`, window.prospectMutex.timestamp);
            showMessage('error', 'Ya hay un envío en progreso. Por favor, espere.');
            // Restaurar estado del botón y flags
            setLoadingState(saveBtn, false);
            isSubmitting = false;
            formSubmissionInProgress = false;
            if (window.prospectMutex) {
                window.prospectMutex.unlock();
            }
            return;
        }

        lastFormData = currentFormDataString;
        submissionCount++;
        console.log(`[MONITOR] ✓ Verificación de duplicados PASADA - Iniciando envío #${submissionCount}`);

        // Ahora sí actualizar lastSubmissionTime después de verificar duplicados
        lastSubmissionTime = currentTime;
        console.log('[MONITOR] lastSubmissionTime actualizado a:', lastSubmissionTime);

        console.log('[MONITOR] ==================== INICIANDO VALIDACIÓN ====================');
        console.log('[MONITOR] Estado antes de validación:');
        console.log('[MONITOR] - isSubmitting:', isSubmitting);
        console.log('[MONITOR] - formSubmissionInProgress:', formSubmissionInProgress);
        console.log('[MONITOR] - submissionMutex.isLocked:', window.prospectMutex ? window.prospectMutex.isLocked() : 'undefined');

        // Agregar archivos del checklist
        Object.keys(uploadedFiles).forEach(docType => {
            const files = uploadedFiles[docType];
            files.forEach((file, index) => {
                formData.append(`documento_${docType}[]`, file);
            });
        });
        
        // Agregar información del checklist
        formData.append('checklist_types', JSON.stringify(Object.keys(uploadedFiles)));

        // Debug: Verificar archivos adicionales
        const additionalFiles = document.getElementById('documentos_adicionales');
        if (additionalFiles && additionalFiles.files.length > 0) {
            console.log('Archivos adicionales detectados:', additionalFiles.files.length);
        }

        // Debug: Mostrar contenido de FormData
        console.log('Contenido de FormData:');
        for (let pair of formData.entries()) {
            if (pair[1] instanceof File) {
                console.log(pair[0], '(File):', pair[1].name, pair[1].size, 'bytes');
            } else {
                console.log(pair[0], ':', pair[1]);
            }
        }

        // 7. Enviar datos con fetch y timeout
        console.log('[MONITOR] ==================== ENVIANDO SOLICITUD ====================');
        console.log('[MONITOR] URL destino: guardar_inteletgroup_prospecto.php');
        console.log('[MONITOR] Método: POST');
        console.log('[MONITOR] Timestamp del envío:', currentTime);
        console.log('[MONITOR] Estado antes del fetch:');
        console.log('[MONITOR] - isSubmitting:', isSubmitting);
        console.log('[MONITOR] - formSubmissionInProgress:', formSubmissionInProgress);
        console.log('[MONITOR] - submissionMutex.isLocked:', window.prospectMutex ? window.prospectMutex.isLocked() : 'undefined');

        const response = await fetch('guardar_inteletgroup_prospecto.php', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            signal: submissionAbortController.signal
        });

        console.log('[MONITOR] ==================== RESPUESTA RECIBIDA ====================');
        console.log('[MONITOR] Status:', response.status);
        console.log('[MONITOR] StatusText:', response.statusText);
        console.log('[MONITOR] OK:', response.ok);

        const result = await response.json();

        console.log('[MONITOR] ==================== PROCESANDO RESPUESTA ====================');
        console.log('[MONITOR] Respuesta JSON recibida:', result);
        console.log('[MONITOR] result.success:', result.success);
        console.log('[MONITOR] result.message:', result.message);
        console.log('[MONITOR] response.ok:', response.ok);

        if (!response.ok || !result.success) {
            // Si la respuesta no es 2xx o success es false, tratarla como un error
            console.log('[MONITOR] ❌ RESPUESTA DE ERROR - Llamando handleServerError');
            console.log('[MONITOR] Status code:', response.status);
            handleServerError(result, response.status);
        } else {
            // Éxito real (respuesta 2xx Y success true)
            console.log('[MONITOR] ✅ RESPUESTA EXITOSA - Llamando handleSuccess');
            handleSuccess(result);
        }

    } catch (error) {
        console.error('[MONITOR] Error en la solicitud fetch:', error);

        if (error.name === 'AbortError') {
            console.log('[MONITOR] Solicitud cancelada');
            showMessage('info', 'Solicitud cancelada');
        } else {
            showMessage('error', 'Error de conexión. No se pudo contactar al servidor.');
        }

        // IMPORTANTE: Resetear flags en caso de error
        console.log('[MONITOR] Error capturado - reseteando banderas');
        isSubmitting = false;
        formSubmissionInProgress = false;
        
        // Desbloquear mutex
        if (window.prospectMutex) {
            window.prospectMutex.unlock();
        }
        
        // Rehabilitar botón
        const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
        if (saveBtn) {
            setLoadingState(saveBtn, false);
            saveBtn.removeAttribute('data-lastclick');
        }
    } finally {
        // 8. Restaurar estado del botón y limpiar variables de control
        console.log('[MONITOR] FINALIZANDO PROCESO - Restableciendo estado');
        if (saveBtn) {
            console.log('[MONITOR] Restaurando estado del botón a normal');
            setLoadingState(saveBtn, false);
            console.log('[MONITOR] Estado del botón después de restaurar - disabled:', saveBtn.disabled);
        } else {
            console.log('[MONITOR] ⚠️ No se encontró botón para restaurar estado');
        }
        
        // DESBLOQUEAR MUTEX primero
        if (window.prospectMutex) {
            console.log('[MUTEX] Desbloqueando mutex al finalizar proceso');
            window.prospectMutex.unlock();
        }
        
        console.log('[MONITOR] Restableciendo isSubmitting = false');
        isSubmitting = false;
        window.isSubmitting = false; // Asegurar que la variable global también se actualice
        console.log('[MONITOR] Nuevo valor isSubmitting:', isSubmitting);
        
        console.log('[MONITOR] Restableciendo formSubmissionInProgress = false');
        formSubmissionInProgress = false;
        console.log('[MONITOR] Nuevo valor formSubmissionInProgress:', formSubmissionInProgress);
        
        console.log('[MONITOR] Limpiando submissionAbortController');
        submissionAbortController = null;

        // Limpiar variables de control más rápidamente después de éxito
        console.log('[MONITOR] Programando limpieza final de caché');
        
        // Resetear handleSaveProspect.isFirstRun
        handleSaveProspect.isFirstRun = false;
        console.log('[MONITOR] handleSaveProspect.isFirstRun reseteado');
        
        setTimeout(() => {
            console.log('[MONITOR] Ejecutando limpieza final de caché');
            lastFormData = null;
            lastSubmissionTime = 0;
            console.log('[MONITOR] Cache de formulario limpiado - permitiendo nuevos envíos');
            console.log('[MONITOR] Estado final isSubmitting:', isSubmitting);
            console.log('[MONITOR] Estado final formSubmissionInProgress:', formSubmissionInProgress);
            console.log('====== FIN COMPLETO DEL PROCESO DE GUARDADO ======');
            
            // Forzar comprobación adicional después de todo el proceso
            setTimeout(() => {
                console.log('[MONITOR] VERIFICACIÓN FINAL DE SEGURIDAD:');
                console.log('[MONITOR] isSubmitting:', isSubmitting);
                console.log('[MONITOR] formSubmissionInProgress:', formSubmissionInProgress);
                console.log('[MONITOR] lastSubmissionTime:', lastSubmissionTime);
                
                const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
                if (saveBtn) {
                    console.log('[MONITOR] Estado final del botón - disabled:', saveBtn.disabled);
                    console.log('[MONITOR] Estado final del botón - pointerEvents:', saveBtn.style.pointerEvents);
                    
                    // Limpiar atributo de último clic
                    saveBtn.removeAttribute('data-lastclick');
                }
            }, 500);
        }, 1500); // 1.5 segundos para limpiar más rápido
    }
}

// Maneja una respuesta exitosa del servidor
function handleSuccess(result) {
    console.log('[MONITOR] ==================== HANDLE SUCCESS ====================');
    console.log('[MONITOR] Función handleSuccess llamada con:', result);
    console.log('[MONITOR] Estado actual:');
    console.log('[MONITOR] - isSubmitting:', isSubmitting);
    console.log('[MONITOR] - formSubmissionInProgress:', formSubmissionInProgress);

    // Limpiar el mensaje de "Guardando prospecto..." primero
    clearAllMessages();
    console.log('[MONITOR] Mensajes limpiados');

    // Mostrar mensaje de éxito dentro del modal
    showMessage('success', 'Prospecto registrado exitosamente. Se ha guardado toda la información.');
    console.log('[MONITOR] Mensaje de éxito mostrado');

    // Cerrar modal después de mostrar el mensaje por un momento
    setTimeout(() => {
        if (modalInstance) {
            modalInstance.hide();
        }

        // Mostrar notificación de éxito en la página principal
        crearNotificacionPrincipal('success', '¡Éxito!', 'El prospecto ha sido registrado correctamente en el sistema.');

        // Resetear formulario para el próximo uso
        resetForm();
    }, 2000); // 2 segundos para leer el mensaje en el modal

    // Limpiar variables de control
    isSubmitting = false;
    lastFormData = null;
}

// Maneja una respuesta de error del servidor
function handleServerError(result, status) {
    console.log('[MONITOR] ==================== HANDLE SERVER ERROR ====================');
    console.log('[MONITOR] Función handleServerError llamada');
    console.log('[MONITOR] result:', result);
    console.log('[MONITOR] status:', status);
    console.log('[MONITOR] Estado actual:');
    console.log('[MONITOR] - isSubmitting:', isSubmitting);
    console.log('[MONITOR] - formSubmissionInProgress:', formSubmissionInProgress);

    let errorMessage = result.message || 'Ocurrió un error desconocido.';
    console.log('[MONITOR] Mensaje de error a mostrar:', errorMessage);

    // Limpiar errores previos en los campos
    clearFieldErrors();
    console.log('[MONITOR] Errores de campos limpiados');

    // Limpiar mensaje de "Guardando prospecto..."
    clearAllMessages();

    if (status === 400 && result.errors) {
        // Errores de validación específicos
        errorMessage = 'Se encontraron errores en el formulario:';
        Object.keys(result.errors).forEach(fieldName => {
            const field = document.getElementById(fieldName);
            const errorText = result.errors[fieldName];
            if (field) {
                setFieldState(field, 'invalid', errorText);
            }
            errorMessage += `\n- ${errorText}`;
        });
        // Enfocar el primer campo con error
        const firstErrorField = document.querySelector('.is-invalid');
        if (firstErrorField) firstErrorField.focus();

    } else if (status === 409 && result.errors && result.errors.rut_cliente) {
        // Error específico de RUT duplicado - DESHABILITADO
        // Se permite registrar el mismo RUT múltiples veces
        errorMessage = result.message;
    }

    showMessage('error', errorMessage);
}


// Limpia los errores de todos los campos del formulario
function clearFieldErrors() {
    const form = document.getElementById('inteletGroupProspectForm');
    if (!form) return;
    
    const fields = form.querySelectorAll('.is-invalid');
    fields.forEach(field => {
        setFieldState(field, 'neutral');
    });
}


// Establece el estado de carga de un botón
function setLoadingState(button, isLoading) {
    console.log('[MONITOR] setLoadingState llamada con isLoading =', isLoading);
    if (!button) {
        console.error('[MONITOR] ⚠️ ERROR: setLoadingState llamado con botón nulo o indefinido');
        return;
    }
    
    console.log('[MONITOR] Botón antes de cambios - ID:', button.id, 'disabled:', button.disabled, 'pointerEvents:', button.style.pointerEvents);
    const btnText = button.querySelector('.btn-text');
    const btnLoading = button.querySelector('.btn-loading');
    console.log('[MONITOR] Elementos encontrados - btnText:', btnText ? 'SI' : 'NO', 'btnLoading:', btnLoading ? 'SI' : 'NO');

    try {
        if (isLoading) {
            console.log('[MONITOR] Deshabilitando botón...');
            
            // Múltiples métodos para asegurar que el botón quede deshabilitado
            button.disabled = true;
            button.setAttribute('disabled', 'disabled');
            button.style.pointerEvents = 'none';
            button.style.cursor = 'not-allowed';
            button.classList.add('disabled');
            
            // Agregar atributos data para verificación
            button.dataset.processingState = 'disabled';
            
            if (btnText) btnText.style.display = 'none';
            if (btnLoading) btnLoading.style.display = 'inline-block';
        } else {
            console.log('[MONITOR] Habilitando botón...');
            
            button.disabled = false;
            button.removeAttribute('disabled');
            button.style.pointerEvents = 'auto';
            button.style.cursor = 'pointer';
            button.classList.remove('disabled');
            
            // Limpiar atributos data
            delete button.dataset.processingState;
            
            if (btnText) btnText.style.display = 'inline-block';
            if (btnLoading) btnLoading.style.display = 'none';
        }
    } catch (e) {
        console.error('[MONITOR] ⚠️ ERROR al modificar el botón:', e);
    }
    
    console.log('[MONITOR] Botón después de cambios - disabled:', button.disabled, 'pointerEvents:', button.style.pointerEvents);
}



// Limpiar todos los mensajes
function clearAllMessages() {
    const container = document.getElementById('inteletgroup-message-container');
    const successMsg = document.getElementById('inteletgroup-success-message');
    const errorMsg = document.getElementById('inteletgroup-error-message');
    const loadingMsg = document.getElementById('inteletgroup-loading-message');

    if (container) container.style.display = 'none';
    if (successMsg) successMsg.style.display = 'none';
    if (errorMsg) errorMsg.style.display = 'none';
    if (loadingMsg) loadingMsg.style.display = 'none';

    // Limpiar también notificaciones principales
    const notifContainer = document.getElementById('inteletgroup-notifications-container');
    if (notifContainer) {
        notifContainer.innerHTML = '';
    }
}

// Mostrar mensajes en el formulario modal
function showMessage(type, message, inModalOnly = false) {
    const container = document.getElementById('inteletgroup-message-container');
    const successMsg = document.getElementById('inteletgroup-success-message');
    const errorMsg = document.getElementById('inteletgroup-error-message');
    const loadingMsg = document.getElementById('inteletgroup-loading-message');

    if (!container || !successMsg || !errorMsg || !loadingMsg) {
        console.error('Elementos de mensaje no encontrados');
        return;
    }

    // Ocultar todos los mensajes
    successMsg.style.display = 'none';
    errorMsg.style.display = 'none';
    loadingMsg.style.display = 'none';
    
    // Mostrar el mensaje apropiado
    let targetMsg;
    switch (type) {
        case 'success':
            targetMsg = successMsg;
            break;
        case 'error':
            targetMsg = errorMsg;
            break;
        case 'info':
            targetMsg = loadingMsg;
            break;
    }
    
    if (targetMsg) {
        const messageText = targetMsg.querySelector('.message-text');
        if (messageText) {
            messageText.textContent = message;
        }
        targetMsg.style.display = 'block';
        container.style.display = 'block';
        
        // Hacer scroll al mensaje para asegurarnos que sea visible
        container.scrollIntoView({ behavior: 'smooth', block: 'start' });
        
        // Auto-hide después de 4 segundos para mensajes de error
        if (type === 'error') {
            setTimeout(() => {
                container.style.display = 'none';
            }, 4000);
        }
    }
}

// Crear notificación en la página principal (fuera del modal)
function crearNotificacionPrincipal(type, title, message) {
    // No crear notificaciones flotantes si el modal está visible y hay un mensaje de error
    const modal = document.getElementById('inteletGroupProspectModal');
    const modalInstance = modal ? bootstrap.Modal.getInstance(modal) : null;
    if (modalInstance && modalInstance._isShown && type === 'error') {
        // Si el modal está abierto y es un error, no crear notificación flotante
        return;
    }
    
    // Buscar si ya existe el contenedor de notificaciones o crearlo
    let notifContainer = document.getElementById('inteletgroup-notifications-container');
    
    if (!notifContainer) {
        notifContainer = document.createElement('div');
        notifContainer.id = 'inteletgroup-notifications-container';
        notifContainer.style.position = 'fixed';
        notifContainer.style.top = '80px';
        notifContainer.style.right = '20px';
        notifContainer.style.zIndex = '9999';
        notifContainer.style.maxWidth = '350px';
        document.body.appendChild(notifContainer);
    }
    
    // Crear la notificación
    const notif = document.createElement('div');
    notif.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} shadow-sm`;
    notif.style.marginBottom = '10px';
    notif.style.position = 'relative';
    notif.style.animation = 'fadeInRight 0.5s';
    
    // Agregar estilos de animación si no existen
    if (!document.getElementById('notif-animations')) {
        const style = document.createElement('style');
        style.id = 'notif-animations';
        style.textContent = `
            @keyframes fadeInRight {
                from { opacity: 0; transform: translateX(50px); }
                to { opacity: 1; transform: translateX(0); }
            }
            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }
    
    // Contenido de la notificación
    notif.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="me-3">
                <i class="bi ${type === 'success' ? 'bi-check-circle-fill' : 
                          type === 'error' ? 'bi-exclamation-triangle-fill' : 
                          'bi-info-circle-fill'}" style="font-size: 1.5rem;"></i>
            </div>
            <div>
                <h6 class="alert-heading mb-1">${title}</h6>
                <p class="mb-0 small">${message}</p>
            </div>
        </div>
        <button type="button" class="btn-close btn-sm position-absolute" 
                style="top: 10px; right: 10px;" aria-label="Close"></button>
    `;
    
    // Agregar la notificación al contenedor
    notifContainer.appendChild(notif);
    
    // Agregar evento para cerrar la notificación
    const closeBtn = notif.querySelector('.btn-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            notif.style.animation = 'fadeOut 0.3s';
            setTimeout(() => {
                notifContainer.removeChild(notif);
                // Si no quedan notificaciones, remover el contenedor
                if (notifContainer.children.length === 0) {
                    document.body.removeChild(notifContainer);
                }
            }, 300);
        });
    }
    
    // Auto-ocultar después de 4 segundos para mensajes de éxito, 8 para otros
    const hideDelay = type === 'success' ? 4000 : 8000;
    setTimeout(() => {
        if (notif.parentNode) {
            notif.style.animation = 'fadeOut 0.3s';
            setTimeout(() => {
                if (notif.parentNode) {
                    notifContainer.removeChild(notif);
                    // Si no quedan notificaciones, remover el contenedor
                    if (notifContainer.children.length === 0 && notifContainer.parentNode) {
                        document.body.removeChild(notifContainer);
                    }
                }
            }, 300);
        }
    }, hideDelay);
    
    return notif;
}

// Resetear formulario
function resetForm() {
    const form = document.getElementById('inteletGroupProspectForm');
    if (form) {
        form.reset();

        // Limpiar estados de validación
        const fields = form.querySelectorAll('.form-control, .form-select');
        fields.forEach(field => {
            field.classList.remove('is-valid', 'is-invalid');
        });

        // Limpiar mensajes
        const feedbacks = form.querySelectorAll('.invalid-feedback');
        feedbacks.forEach(feedback => {
            feedback.textContent = '';
        });

        // Resetear el botón de guardar
        const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
        if (saveBtn) {
            saveBtn.disabled = false;
            saveBtn.classList.remove('loading');

            const btnText = saveBtn.querySelector('.btn-text');
            const btnLoading = saveBtn.querySelector('.btn-loading');
            if (btnText) btnText.style.display = 'inline-block';
            if (btnLoading) btnLoading.style.display = 'none';
        }

        // Repoblar el nombre del ejecutivo
        populateExecutiveName();
    }

    // Limpiar checklist de documentos
    uploadedFiles = {};
    const checklistContainer = document.getElementById('document-checklist-container');
    if (checklistContainer) {
        checklistContainer.innerHTML =
            '<p class="text-muted">Seleccione un tipo de persona para ver los documentos requeridos.</p>';
    }

    // Ocultar mensajes
    const container = document.getElementById('inteletgroup-message-container');
    if (container) {
        container.style.display = 'none';
    }

    // Limpiar variables de control
    isSubmitting = false;
    formSubmissionInProgress = false;
    lastFormData = null;
}

// Función para abrir el modal (llamada desde el botón)
// Exportamos la función a nivel global para que sea accesible desde el HTML
window.abrirModalInteletGroupProspecto = function abrirModalInteletGroupProspecto() {
    console.log('====== APERTURA DE MODAL SOLICITADA ======');
    console.log('[MONITOR] Estado actual - isSubmitting:', isSubmitting);
    console.log('[MONITOR] Estado actual - formSubmissionInProgress:', formSubmissionInProgress);
    
    const modalElement = document.getElementById('inteletGroupProspectModal');
    console.log('[MONITOR] Elemento modal encontrado:', modalElement ? 'SI' : 'NO');
    
    // Crear o reutilizar la instancia única del modal
    if (!modalInstance) {
        console.log('[MONITOR] Creando nueva instancia de modal');
        modalInstance = new bootstrap.Modal(modalElement);
    } else {
        console.log('[MONITOR] Usando instancia existente del modal');
    }
    
    // Resetear explícitamente las banderas de control antes de mostrar el modal
    isSubmitting = false;
    formSubmissionInProgress = false;
    lastSubmissionTime = 0;
    lastFormData = null;
    submissionCount = 0;
    console.log('[MONITOR] Banderas de control reseteadas antes de mostrar modal');
    
    console.log('[MONITOR] Mostrando modal...');
    modalInstance.show();

    // Asegurar que el nombre del ejecutivo se llene después de abrir el modal
    setTimeout(() => {
        console.log('[MONITOR] Timeout ejecutado para poblar nombre ejecutivo');
        populateExecutiveName();
        
        // Verificar estado del botón después del timeout
        const saveBtn = document.getElementById('saveInteletGroupProspectBtn');
        if (saveBtn) {
            console.log('[MONITOR] Botón de guardar después de timeout - Estado:', saveBtn.disabled ? 'deshabilitado' : 'habilitado');
            
            // Agregar atributos anti-doble-clic y reforzar el event listener
            console.log('[MONITOR] Reforzando protección anti-doble-clic del botón');
            saveBtn.setAttribute('data-double-click-protection', 'enabled');
            saveBtn.setAttribute('autocomplete', 'off');
            
            // Agregar clase visual para indicar que el botón está protegido
            if (!saveBtn.classList.contains('protected-btn')) {
                saveBtn.classList.add('protected-btn');
            }
            
            // Eliminamos la solución del overlay que está causando problemas
            // En su lugar, reforzamos el evento del botón directamente
            console.log('[MONITOR] Reforzando directamente el evento click del botón');
            
            // Solo añadir atributos anti-doble-clic, NO reemplazar el botón
            console.log('[MONITOR] Añadiendo atributos de protección al botón existente');
            saveBtn.setAttribute('data-double-click-protection', 'enabled');
            saveBtn.setAttribute('autocomplete', 'off');
            saveBtn.setAttribute('data-lastclick', '0');
            
            // Verificar que el botón mantenga su event listener
            console.log('[MONITOR] Botón configurado con protección adicional');
        }
    }, 100);
}

// Función para generar RUT aleatorio válido
// Exportamos también esta función a nivel global si se necesita
window.generarRutAleatorio = function generarRutAleatorio() {
    // Generar número base aleatorio entre 10.000.000 y 99.999.999
    const numeroBase = Math.floor(Math.random() * ********) + ********;

    // Calcular dígito verificador
    const digitoVerificador = calcularDigitoVerificador(numeroBase);

    // Formatear RUT
    const rutGenerado = numeroBase + '-' + digitoVerificador;

    // Asignar al campo
    const campoRut = document.getElementById('rut_cliente');
    if (campoRut) {
        campoRut.value = rutGenerado;
        campoRut.classList.remove('is-invalid');

        // Mostrar notificación temporal
        const button = event.target.closest('button');
        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="bi bi-check"></i>';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary');

        setTimeout(() => {
            button.innerHTML = originalContent;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 1000);
    }

    console.log('RUT aleatorio generado:', rutGenerado);
}

// Función para calcular dígito verificador de RUT
function calcularDigitoVerificador(rut) {
    let suma = 0;
    let multiplicador = 2;

    // Convertir a string y procesar de derecha a izquierda
    const rutStr = rut.toString();
    for (let i = rutStr.length - 1; i >= 0; i--) {
        suma += parseInt(rutStr[i]) * multiplicador;
        multiplicador = multiplicador === 7 ? 2 : multiplicador + 1;
    }

    const resto = suma % 11;
    const dv = 11 - resto;

    if (dv === 11) return '0';
    if (dv === 10) return 'K';
    return dv.toString();
}

// Función para llenar datos de prueba
// Exportamos también esta función a nivel global si se necesita
window.llenarDatosPrueba = function llenarDatosPrueba() {
    // Generar RUT aleatorio primero
    generarRutAleatorio();

    // Datos de prueba para InteletGroup (todo en mayúsculas donde corresponde)
    const datosPrueba = {
        nombre_ejecutivo: window.currentUserName || 'EJECUTIVO TEST',
        razon_social: 'EMPRESA EJEMPLO LTDA',
        rubro: 'COMERCIO AL POR MENOR',
        direccion_comercial: 'AV. PROVIDENCIA 1234, SANTIAGO',
        telefono_celular: '9' + Math.floor(Math.random() * ******** + ********),
        email: '<EMAIL>',
        numero_pos: 'POS' + Math.floor(Math.random() * 900000 + 100000),
        tipo_cuenta: 'Cuenta Corriente',
        numero_cuenta_bancaria: Math.floor(Math.random() * ********** + **********).toString(),
        dias_atencion: 'LUNES A VIERNES',
        horario_atencion: '09:00 - 18:00',
        contrata_boleta: 'Si',
        competencia_actual: 'Transbank'
    };

    // Llenar los campos del formulario
    Object.keys(datosPrueba).forEach(campo => {
        const elemento = document.getElementById('inteletgroup_' + campo) || document.getElementById(campo);
        if (elemento) {
            elemento.value = datosPrueba[campo];
            // Trigger eventos para aplicar formato
            elemento.dispatchEvent(new Event('input'));
            elemento.dispatchEvent(new Event('blur'));
        }
    });

    console.log('Datos de prueba cargados para InteletGroup');
}

// Función para prevenir notificaciones duplicadas
function setupNotificationPrevention() {
    // Interceptar console.error para evitar que otros scripts creen notificaciones
    const originalConsoleError = console.error;
    console.error = function() {
        // Si el modal de InteletGroup está abierto, no permitir notificaciones externas
        const modal = document.getElementById('inteletGroupProspectModal');
        const modalInstance = modal ? bootstrap.Modal.getInstance(modal) : null;
        if (modalInstance && modalInstance._isShown) {
            // Solo log en consola, no crear notificaciones
            originalConsoleError.apply(console, arguments);
            return;
        }
        // Si el modal no está abierto, comportamiento normal
        originalConsoleError.apply(console, arguments);
    };
}

// =============================
// FUNCIONES PARA CHECKLIST DE DOCUMENTOS
// =============================

// Definición de documentos requeridos por tipo de persona
const DOCUMENT_TYPES = {
    Natural: [
        {
            id: 'PN_CEDULA_FRONTAL',
            name: 'Cédula de identidad frontal',
            description: 'Fotografía o escaneo del frente de la cédula de identidad',
            required: true,
            accept: 'image/*,.pdf'
        },
        {
            id: 'PN_CEDULA_TRASERA',
            name: 'Cédula de identidad trasera',
            description: 'Fotografía o escaneo del reverso de la cédula de identidad',
            required: true,
            accept: 'image/*,.pdf'
        },
        {
            id: 'PN_BOLETA_PATENTE',
            name: 'Boleta y/o Patente Comercial',
            description: 'Documento que acredite la actividad comercial',
            required: true,
            accept: '.pdf,.doc,.docx,image/*'
        },
        {
            id: 'PN_FOTO_INTERIOR',
            name: 'Fotografía interior del establecimiento',
            description: 'Fotografía del interior del establecimiento que muestre la actividad económica',
            required: true,
            accept: 'image/*'
        },
        {
            id: 'PN_FOTO_EXTERIOR',
            name: 'Fotografía exterior del establecimiento',
            description: 'Fotografía del exterior del establecimiento comercial, que muestre fachada y dirección',
            required: true,
            accept: 'image/*'
        },
        {
            id: 'PN_RESPALDO_CUENTA',
            name: 'Respaldo de cuenta de Abono',
            description: 'Documento que indique Titular, número de cuenta y logo del banco (Cta Cte o Cta Vista)',
            required: true,
            accept: '.pdf,.doc,.docx,image/*'
        },
        {
            id: 'AMBOS_CARPETA_TRIBUTARIA',
            name: 'Carpeta Tributaria (FAPRO)',
            description: 'Carpeta tributaria del SII',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_MANDATO_PAC',
            name: 'Mandato PAC',
            description: 'Para clientes con cuenta de abono distinta a BCI donde el titular es el establecimiento',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_PODER_NOTARIAL',
            name: 'Poder Notarial',
            description: 'Para clientes con cuenta de abono distinta a BCI, donde el titular es distinto al establecimiento',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_PODER_NOTARIAL_SIMPLE',
            name: 'Poder Notarial Simple',
            description: 'Poder simple para representación legal',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_CARTA_ACEPTACION',
            name: 'Carta de Aceptación',
            description: 'Excepción: Clientes con facturación inferior a 100UF, Carta de Aceptación a la comisión garantizada',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        // Documentos complementarios (aplican para ambos tipos de persona)
        {
            id: 'AMBOS_CUENTAS_TERCEROS',
            name: 'Cuentas de abono de terceros',
            description: 'Rut titular distinto al del establecimiento - Poder Simple: Cuenta de tercero de único Socio (EIRL)',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_SOCIEDAD_HECHO',
            name: 'Sociedad de Hecho - Declaración Jurada Notarial',
            description: 'Declaración Jurada Notarial que indique los representantes legales vigentes a la fecha de firma con contrato BCIPagos. (No tienen escritura pública, ni estatutos)',
            required: false,
            accept: '.pdf,.doc,.docx'
        }
    ],
    Juridica: [
        {
            id: 'PJ_CEDULA_REPRESENTANTES',
            name: 'Cédula de identidad representantes legales',
            description: 'Cédula de identidad por ambos lados, de él o los representantes legales',
            required: true,
            accept: 'image/*,.pdf'
        },
        {
            id: 'PJ_CONSTITUCION_PODERES',
            name: 'Constitución o Poderes Vigentes',
            description: 'Estatutos y Vigencia (FAPRO)',
            required: true,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'PJ_BOLETA_PATENTE',
            name: 'Boleta y/o Patente Comercial',
            description: 'Documento que acredite la actividad comercial',
            required: true,
            accept: '.pdf,.doc,.docx,image/*'
        },
        {
            id: 'PJ_FOTO_INTERIOR',
            name: 'Fotografía interior del establecimiento',
            description: 'Fotografía del interior del establecimiento que muestre la actividad económica',
            required: true,
            accept: 'image/*'
        },
        {
            id: 'PJ_FOTO_EXTERIOR',
            name: 'Fotografía exterior del establecimiento',
            description: 'Fotografía del exterior del establecimiento comercial, que muestre fachada y dirección',
            required: true,
            accept: 'image/*'
        },
        {
            id: 'PJ_RESPALDO_CUENTA',
            name: 'Respaldo de cuenta de Abono',
            description: 'Documento que indique Titular, número de cuenta y logo del banco (Cta Cte o Cta Vista)',
            required: true,
            accept: '.pdf,.doc,.docx,image/*'
        },
        {
            id: 'PJ_CARPETA_TRIBUTARIA',
            name: 'Carpeta Tributaria (FAPRO)',
            description: 'Carpeta tributaria del SII',
            required: true,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'PJ_ERUT',
            name: 'E-Rut (FAPRO)',
            description: 'E-Rut que indique nombre del representante que firma',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        // Documentos complementarios (aplican para ambos tipos de persona)
        {
            id: 'AMBOS_CARPETA_TRIBUTARIA',
            name: 'Carpeta Tributaria (FAPRO)',
            description: 'Carpeta tributaria del SII',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_MANDATO_PAC',
            name: 'Mandato PAC',
            description: 'Para clientes con cuenta de abono distinta a BCI donde el titular es el establecimiento',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_PODER_NOTARIAL',
            name: 'Poder Notarial',
            description: 'Para clientes con cuenta de abono distinta a BCI, donde el titular es distinto al establecimiento',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_PODER_NOTARIAL_SIMPLE',
            name: 'Poder Notarial Simple',
            description: 'Poder simple para representación legal',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_CARTA_ACEPTACION',
            name: 'Carta de Aceptación',
            description: 'Excepción: Clientes con facturación inferior a 100UF, Carta de Aceptación a la comisión garantizada',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_CUENTAS_TERCEROS',
            name: 'Cuentas de abono de terceros',
            description: 'Rut titular distinto al del establecimiento - Poder Simple: Cuenta de tercero de único Socio (EIRL)',
            required: false,
            accept: '.pdf,.doc,.docx'
        },
        {
            id: 'AMBOS_SOCIEDAD_HECHO',
            name: 'Sociedad de Hecho - Declaración Jurada Notarial',
            description: 'Declaración Jurada Notarial que indique los representantes legales vigentes a la fecha de firma con contrato BCIPagos. (No tienen escritura pública, ni estatutos)',
            required: false,
            accept: '.pdf,.doc,.docx'
        }
    ]
};

// Cargar el checklist de documentos según el tipo de persona
function loadDocumentChecklist(tipoPersona) {
    console.log('=== loadDocumentChecklist llamada con:', tipoPersona);
    const container = document.getElementById('document-checklist-container');
    
    if (!container) {
        console.error('Container document-checklist-container no encontrado');
        return;
    }
    
    if (!tipoPersona) {
        console.log('No se proporcionó tipo de persona');
        container.innerHTML = '<p class="text-muted">Seleccione un tipo de persona para ver los documentos requeridos.</p>';
        return;
    }

    // Limpiar contenedor y archivos previos
    container.innerHTML = '';
    uploadedFiles = {};

    // Mostrar loading
    container.innerHTML = '<div class="text-center p-3"><i class="bi bi-hourglass-split me-2"></i>Cargando documentos desde base de datos...</div>';

    // Cargar documentos desde la base de datos
    fetch('endpoints/obtener_tipos_documento_v2.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tipo_persona: tipoPersona })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.documentos) {
            console.log('Documentos cargados desde BD:', data.documentos);
            renderDocumentChecklistFromDB(data.documentos);
        } else {
            console.error('Error cargando documentos desde BD:', data.error);
            // Fallback al array hardcodeado si falla la consulta
            const documents = DOCUMENT_TYPES[tipoPersona] || [];
            renderDocumentChecklistFromArray(documents);
        }
    })
    .catch(error => {
        console.error('Error de conexión:', error);
        // Fallback al array hardcodeado si falla la conexión
        const documents = DOCUMENT_TYPES[tipoPersona] || [];
        renderDocumentChecklistFromArray(documents);
    });
}

// Función para renderizar el checklist desde datos de la BD
function renderDocumentChecklistFromDB(documentos) {
    const container = document.getElementById('document-checklist-container');
    if (!container) return;

    if (documentos.length === 0) {
        container.innerHTML = '<p class="text-muted">No hay documentos configurados para este tipo de persona.</p>';
        return;
    }

    // Convertir documentos de BD a formato esperado por el frontend
    const documents = documentos.map(doc => ({
        id: doc.codigo,
        name: doc.nombre,
        description: doc.descripcion,
        required: doc.es_obligatorio === 1,
        accept: '.pdf,.doc,.docx,.jpg,.jpeg,.png'
    }));

    renderDocumentChecklist(documents);
}

// Función fallback para renderizar desde array hardcodeado
function renderDocumentChecklistFromArray(documents) {
    if (documents.length === 0) {
        const container = document.getElementById('document-checklist-container');
        container.innerHTML = '<p class="text-muted">Seleccione un tipo de persona para ver los documentos requeridos.</p>';
        return;
    }

    renderDocumentChecklist(documents);
}

// Función común para renderizar el checklist
function renderDocumentChecklist(documents) {
    const container = document.getElementById('document-checklist-container');
    if (!container) return;

    // Crear el HTML del checklist
    let html = '<div class="document-checklist">';

    // Barra de progreso
    html += `
        <div class="checklist-progress mb-3">
            <div class="progress-info">
                <span>Progreso de documentación</span>
                <span id="checklist-progress-text">0 de ${documents.filter(d => d.required).length} obligatorios</span>
            </div>
            <div class="progress">
                <div class="progress-bar bg-success" id="checklist-progress-bar"
                     role="progressbar" style="width: 0%"></div>
            </div>
        </div>
    `;

    // Documentos obligatorios
    const requiredDocs = documents.filter(d => d.required);
    const optionalDocs = documents.filter(d => !d.required);
    
    if (requiredDocs.length > 0) {
        html += '<h6 class="document-category-title"><i class="bi bi-exclamation-circle me-2"></i>Documentos Obligatorios</h6>';
        requiredDocs.forEach(doc => {
            html += createDocumentChecklistItem(doc);
        });
    }
    
    if (optionalDocs.length > 0) {
        html += '<h6 class="document-category-title mt-4"><i class="bi bi-info-circle me-2"></i>Documentos Opcionales</h6>';
        optionalDocs.forEach(doc => {
            html += createDocumentChecklistItem(doc);
        });
    }
    
    html += '</div>';
    container.innerHTML = html;

    // Configurar eventos de upload
    setupChecklistEvents();
}

// Crear HTML para un item del checklist
function createDocumentChecklistItem(doc) {
    const requiredBadge = doc.required ? 
        '<span class="required-badge">Obligatorio</span>' : 
        '<span class="optional-badge">Opcional</span>';
    
    return `
        <div class="checklist-item" data-doc-id="${doc.id}">
            <div class="checklist-item-header">
                <h6 class="checklist-item-title">
                    <i class="bi bi-file-earmark me-2"></i>
                    ${doc.name}
                    ${requiredBadge}
                </h6>
                <div class="checklist-status status-pending">
                    <i class="bi bi-clock-fill" style="font-size: 1.2rem;"></i>
                    <span>Pendiente</span>
                </div>
            </div>
            <div class="checklist-item-description">${doc.description}</div>
            <div class="checklist-upload-area" onclick="triggerFileUpload('${doc.id}')">
                <i class="bi bi-cloud-upload fs-3 text-muted"></i>
                <p class="mb-0 mt-2">Haga clic para seleccionar archivos o arrástrelos aquí</p>
                <small class="text-muted">Formatos permitidos: ${getAcceptFormats(doc.accept)}</small>
            </div>
            <input type="file" 
                   class="checklist-file-input" 
                   id="file-${doc.id}" 
                   data-doc-id="${doc.id}"
                   accept="${doc.accept}"
                   multiple>
            <div class="uploaded-files-list" id="files-list-${doc.id}"></div>
        </div>
    `;
}

// Obtener formatos aceptados en formato legible
function getAcceptFormats(accept) {
    const formats = {
        'image/*': 'Imágenes (JPG, PNG, etc.)',
        '.pdf': 'PDF',
        '.doc,.docx': 'Word',
        '.xlsx,.xls': 'Excel'
    };
    
    return accept.split(',').map(f => formats[f] || f).join(', ');
}

// Configurar eventos del checklist
function setupChecklistEvents() {
    // Eventos para cada input de archivo
    document.querySelectorAll('.checklist-file-input').forEach(input => {
        input.addEventListener('change', handleFileSelection);
    });
    
    // Drag and drop
    document.querySelectorAll('.checklist-upload-area').forEach(area => {
        area.addEventListener('dragover', handleDragOver);
        area.addEventListener('dragleave', handleDragLeave);
        area.addEventListener('drop', handleDrop);
    });
}

// Trigger para abrir el diálogo de archivos
function triggerFileUpload(docId) {
    document.getElementById(`file-${docId}`).click();
}

// Manejar selección de archivos
function handleFileSelection(event) {
    const input = event.target;
    const docId = input.dataset.docId;
    const files = Array.from(input.files);
    
    if (files.length === 0) return;
    
    // Inicializar array si no existe
    if (!uploadedFiles[docId]) {
        uploadedFiles[docId] = [];
    }
    
    // Validar y agregar archivos
    files.forEach(file => {
        if (validateFile(file)) {
            uploadedFiles[docId].push(file);
        }
    });
    
    // Actualizar UI
    updateChecklistItem(docId);
    updateProgress();
    
    // Limpiar input para permitir reselección
    input.value = '';
}

// Validar archivo
function validateFile(file) {
    const maxSize = 5 * 1024 * 1024; // 5MB
    
    if (file.size > maxSize) {
        showMessage('error', `El archivo ${file.name} excede el tamaño máximo de 5MB`);
        return false;
    }
    
    return true;
}

// Actualizar item del checklist
function updateChecklistItem(docId) {
    const item = document.querySelector(`[data-doc-id="${docId}"]`);
    const filesList = document.getElementById(`files-list-${docId}`);
    const status = item.querySelector('.checklist-status');
    const files = uploadedFiles[docId] || [];
    
    if (files.length > 0) {
        // Actualizar estado
        status.className = 'checklist-status status-uploaded';
        status.innerHTML = '<i class="bi bi-check-circle-fill" style="font-size: 1.2rem;"></i><span>Subido</span>';
        item.classList.add('is-valid');
        
        // Mostrar lista de archivos
        let filesHtml = '';
        files.forEach((file, index) => {
            filesHtml += `
                <div class="uploaded-file-item">
                    <span class="uploaded-file-name">
                        <i class="bi bi-file-earmark-fill"></i>
                        ${file.name}
                        <small class="text-muted">(${formatFileSize(file.size)})</small>
                    </span>
                    <button type="button" class="btn-remove-file" 
                            onclick="removeFile('${docId}', ${index})">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            `;
        });
        filesList.innerHTML = filesHtml;
    } else {
        // Sin archivos
        status.className = 'checklist-status status-pending';
        status.innerHTML = '<i class="bi bi-clock-fill" style="font-size: 1.2rem;"></i><span>Pendiente</span>';
        item.classList.remove('is-valid');
        filesList.innerHTML = '';
    }
}

// Formatear tamaño de archivo
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Eliminar archivo
function removeFile(docId, index) {
    if (uploadedFiles[docId]) {
        uploadedFiles[docId].splice(index, 1);
        updateChecklistItem(docId);
        updateProgress();
    }
}

// Actualizar barra de progreso
function updateProgress() {
    const tipoPersona = document.getElementById('tipo_persona').value;
    if (!tipoPersona) return;
    
    const documents = DOCUMENT_TYPES[tipoPersona] || [];
    const requiredDocs = documents.filter(d => d.required);
    const uploadedRequired = requiredDocs.filter(d => 
        uploadedFiles[d.id] && uploadedFiles[d.id].length > 0
    ).length;
    
    const percentage = requiredDocs.length > 0 ? 
        (uploadedRequired / requiredDocs.length) * 100 : 0;
    
    document.getElementById('checklist-progress-bar').style.width = percentage + '%';
    document.getElementById('checklist-progress-text').textContent = 
        `${uploadedRequired} de ${requiredDocs.length} obligatorios`;
}

// Drag and drop handlers
function handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('drag-over');
}

function handleDragLeave(e) {
    e.currentTarget.classList.remove('drag-over');
}

function handleDrop(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('drag-over');
    
    const docId = e.currentTarget.parentElement.dataset.docId;
    const input = document.getElementById(`file-${docId}`);
    
    // Asignar archivos al input y disparar evento
    input.files = e.dataTransfer.files;
    input.dispatchEvent(new Event('change', { bubbles: true }));
}

// Verificación final de que las funciones críticas estén disponibles globalmente
console.log('[MONITOR] Verificación de funciones globales:');
console.log('[MONITOR] window.abrirModalInteletGroupProspecto:', typeof window.abrirModalInteletGroupProspecto);
console.log('[MONITOR] window.generarRutAleatorio:', typeof window.generarRutAleatorio);
console.log('[MONITOR] window.llenarDatosPrueba:', typeof window.llenarDatosPrueba);
console.log('[MONITOR] JavaScript de InteletGroup cargado completamente');
