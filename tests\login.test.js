const { test, expect } = require('@playwright/test');

// Configurar el tiempo de espera para las pruebas
const TIMEOUT = 30000; // 30 segundos

test('Inicio de sesión exitoso con credenciales válidas', async ({ page }) => {
  test.setTimeout(TIMEOUT);
  
  try {
    // Navegar a la página de inicio de sesión
    await page.goto('http://localhost/intranet/dist/login.php', { waitUntil: 'networkidle' });
    
    // Tomar captura de pantalla de la página de login
    await page.screenshot({ path: 'screenshots/login-page.png' });
    
    // Rellenar el formulario de inicio de sesión
    await page.fill('input[name="rut"]', '<EMAIL>');
    await page.fill('input[name="clave"]', 'tu_contraseña_aquí'); // Reemplaza con la contraseña correcta
    
    // Tomar captura después de llenar el formulario
    await page.screenshot({ path: 'screenshots/filled-form.png' });
    
    // Hacer clic en el botón de inicio de sesión
    const [response] = await Promise.all([
      page.waitForResponse(response => response.url().includes('ControllerGestar.php')),
      page.click('button[type="submit"]')
    ]);
    
    // Esperar a que la redirección se complete
    await page.waitForLoadState('networkidle');
    
    // Tomar captura después del login
    await page.screenshot({ path: 'screenshots/after-login.png' });
    
    // Verificar que la respuesta del servidor es exitosa
    expect(response.status()).toBe(200);
    
    // Verificar que el usuario es redirigido al dashboard después del inicio de sesión exitoso
    await expect(page).toHaveURL(/inteletgroup_admin_dashboard\.php$/);
    
    // Verificar que el nombre de usuario esté visible en la página
    await expect(page.locator('body')).toContainText('Oriana');
    
  } catch (error) {
    // Tomar captura en caso de error
    await page.screenshot({ path: 'screenshots/error.png' });
    throw error;
  }
});

test('Mostrar error con credenciales incorrectas', async ({ page }) => {
  test.setTimeout(TIMEOUT);
  
  try {
    // Navegar a la página de inicio de sesión
    await page.goto('http://localhost/intranet/dist/login.php', { waitUntil: 'networkidle' });
    
    // Rellenar el formulario con credenciales incorrectas
    await page.fill('input[name="rut"]', '<EMAIL>');
    await page.fill('input[name="clave"]', 'contraseña_incorrecta');
    
    // Interceptar la respuesta del servidor
    const [response] = await Promise.all([
      page.waitForResponse(response => response.url().includes('ControllerGestar.php')),
      page.click('button[type="submit"]')
    ]);
    
    // Esperar a que se complete la solicitud
    const responseData = await response.json();
    
    // Verificar que la respuesta indica error de credenciales
    expect(responseData.success).toBe(false);
    expect(responseData.message).toContain('incorrecta');
    
  } catch (error) {
    // Tomar captura en caso de error
    await page.screenshot({ path: 'screenshots/login-error.png' });
    throw error;
  }
});
