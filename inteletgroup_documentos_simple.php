<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'cache_utils.php';
session_start();

// Aplicar headers anti-caché
no_cache_headers();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    $errorMsg = urlencode("Usuario no autenticado o sin permisos para InteletGroup.");
    header('Location: ' . version_url('login.php?error=' . $errorMsg));
    exit;
}

// Incluir conexión a la base de datos
require_once 'con_db.php';

// Obtener información del usuario logueado
$usuario_id = $_SESSION['usuario_id'];
$nombre_usuario = $_SESSION['nombre_usuario'] ?? 'Usuario';
$proyecto = $_SESSION['proyecto'] ?? 'inteletGroup';

// Verificar conexión
if (!isset($mysqli) || $mysqli->connect_error) {
    error_log("Error crítico: No se pudo establecer conexión a la base de datos");
    $db_status = "❌ Error de conexión a la base de datos";
} else {
    $db_status = "✅ Conexión a la base de datos establecida";
}

// Obtener todos los prospectos del usuario (versión simplificada)
$prospectos_usuario = [];
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>InteletGroup - Gestión de Documentos</title>
    <?php echo no_cache_meta(); ?>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    
    <style>
        :root {
            --primary-dark: #1e3a8a;
            --primary-medium: #3b82f6;
            --header-gradient: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #2563eb 100%);
        }
        
        .simple-header {
            background: var(--header-gradient) !important;
            color: white !important;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
            margin-bottom: 2rem !important;
            position: sticky !important;
            top: 0 !important;
            z-index: 1000 !important;
            padding: 0 !important;
        }
        
        .header-container {
            display: flex !important;
            align-items: center !important;
            justify-content: space-between !important;
            padding: 1rem 0 !important;
        }
        
        .brand-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .site-title {
            font-size: 1.5rem !important;
            font-weight: 700 !important;
            margin: 0 !important;
            color: white !important;
        }
        
        .site-subtitle {
            font-size: 0.875rem !important;
            color: rgba(255, 255, 255, 0.9) !important;
            margin: 0 !important;
        }
        
        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.6rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            margin-left: 0.5rem;
        }
        
        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="simple-header">
        <div class="container">
            <div class="header-container">
                <div class="brand-section">
                    <div>
                        <h1 class="site-title">InteletGroup</h1>
                        <span class="site-subtitle">Gestión de Documentos</span>
                    </div>
                </div>
                
                <div>
                    <a href="form_inteletgroup.php" class="nav-btn">
                        <i class="bi bi-arrow-left"></i> Volver
                    </a>
                    <a href="logout.php" class="nav-btn">
                        <i class="bi bi-box-arrow-right"></i> Salir
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="alert alert-info">
            <h4><i class="bi bi-info-circle me-2"></i>Página Simplificada</h4>
            <p>Esta es una versión simplificada para probar el diseño del header.</p>
            <p><strong>Usuario:</strong> <?php echo htmlspecialchars($nombre_usuario); ?></p>
            <p><strong>Proyecto:</strong> <?php echo htmlspecialchars($proyecto); ?></p>
            <p><strong>Estado DB:</strong> <?php echo $db_status; ?></p>
        </div>
        
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="bi bi-check-circle-fill text-success me-2"></i>
                    Página Cargada Correctamente
                </h5>
                <p class="card-text">
                    Si puedes ver esta página, significa que el archivo PHP funciona correctamente.
                </p>
                <a href="inteletgroup_documentos.php" class="btn btn-primary">
                    Ir a Versión Completa
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>