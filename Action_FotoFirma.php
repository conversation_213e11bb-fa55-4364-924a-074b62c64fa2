<?php
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    // Conexión a la base de datos
    include("con_db.php");
    session_start();

    $ejecutivo = $_SESSION['usuario'];

    // Obtener valores del formulario
    $fecha_venta = $_POST['fecha_venta'];
    $cliente_firma = $_POST['cliente_firma'];
    $rut_comercio = $_POST['rut_comercio'];




    // Procesar la subida de la foto
    $extension = pathinfo($_FILES['userfile']['name'], PATHINFO_EXTENSION);
    $archivo_destino = "Fotos/Form_EntregaInmediata/" . $rut_comercio . "." . $extension;
    
    if (move_uploaded_file($_FILES['userfile']['tmp_name'], $archivo_destino)) {
        // Redireccionar a otra página o realizar otras acciones

        
        $sql = "INSERT INTO TB_VENTA_GETNET_INMEDIATA (rut_ejecutivo, ejecutivo, fecha_venta, cliente_firma, archivo) 
        VALUES ('$rut_ejecutivo', '$ejecutivo', NOW(), '$cliente_firma','$archivo_destino')";

        
        echo "<script>alert('Foto firma subida con exito');window.location.href='Form_EntregaInmediata2.php';</script>";
        exit();
    } else {
        echo "<script>alert('Error al subir el archivo.');window.location.href='Form_EntregaInmediata.php';</script>";
        // echo "Error al subir el archivo. " . $_FILES['userfile']['error'];
    }
}
?>
