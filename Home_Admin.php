<?php 

header('Content-Type: text/html; charset=UTF-8');
//Iniciar una nueva sesión o reanudar la existente.
session_start();

$inc = include("con_db.php");



$resultado = $conex->query(
    "   
    select t.* , w.nombre 
    , w.area , w.supervisor 
    , w.iden_user , w.rut 
    FROM  
    (
      SELECT  
        a.usuario , a.pass_new, a.fecha_registro 
      , count(*) total
      FROM  
        TB_CLAVES_USUARIOS a
      LEFT JOIN  
        TB_CLAVES_USUARIOS b
      ON a.usuario = b.usuario 
      AND a.fecha_registro <= b.fecha_registro
      GROUP BY 
        a.usuario , a.pass_new , a.fecha_registro
    ) t
    LEFT JOIN 
      tb_user_tqw w 
    ON t.usuario = w.email 
    LEFT JOIN 
    	(
			SELECT DISTINCT RUT  AS RUT
			FROM tb_turnos_py
			WHERE DATE_FORMAT(STR_TO_DATE(FECHA, '%d/%m/%Y'), '%Y%m') = (SELECT MAX(DATE_FORMAT(STR_TO_DATE(FECHA, '%d/%m/%Y'), '%Y%m')) FROM tb_turnos_py)
) turnos
ON turnos.RUT  = w.rut  
    WHERE  t.total = 1 
    AND turnos.RUT  is not null 
    
    
      "  
      
      );
  

 $variaa =  $_SESSION['usuario'];

?>



<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Affan - PWA Mobile HTML Template">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>APP TQW</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/logo_con.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">

      <!-- # Header Five Layout -->
      <!-- Header Content -->
      <div class="header-content header-style-five position-relative d-flex align-items-center justify-content-between">
        <!-- Logo Wrapper -->
        <div class="logo-wrapper">
          <a href="Suoer_Excep_Calid30.php">
            <img src="img/core-img/Definitivo.png" alt="">
          </a>
        </div>

        <!-- Navbar Toggler -->
        <div class="navbar--toggler" id="affanNavbarToggler" data-bs-toggle="offcanvas" data-bs-target="#affanOffcanvas"
          aria-controls="affanOffcanvas">
          <span class="d-block"></span>
          <span class="d-block"></span>
          <span class="d-block"></span>
        </div>
      </div>
      <!-- # Header Five Layout End -->

    </div>
  </div>

  <!-- Offcanvas Start -->
  <div class="offcanvas offcanvas-start" id="affanOffcanvas" data-bs-scroll="true" tabindex="-1"
    aria-labelledby="affanOffcanvsLabel">

    <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
      aria-label="Close"></button>

    <div class="offcanvas-body p-0">
      <div class="sidenav-wrapper">
        <!-- Sidenav Profile -->
        <div class="sidenav-profile bg-gradient">
          <div class="sidenav-style1"></div>

          <!-- User Thumbnail -->
          <div class="user-profile">
            <img src="img/bg-img/2.jpg" alt="">
          </div>

          <!-- User Info -->
          <div class="user-info">
            <h6 class="user-name mb-0">
              <?php echo $_SESSION['usuario'] ?>
            </h6>

          </div>
        </div>

        <!-- Sidenav Nav -->
        <ul class="sidenav-nav ps-0">
          <li>
            <a href="home.php"><i class="bi bi-house-door"></i>HOME</a>
          </li>
          <li>
            <a href="Form_UserNuevo.php"><i class="bi bi-house-door"></i>INGRESAR USUARIO NUEVO</a>
          </li>
          <li>
            <a href="#"><i class="bi bi-globe"></i></i>REPORTES WEB</a>
            <ul>
              <li>
              <a href="https://app.powerbi.com/view?r=eyJrIjoiMDI5MjIxOGItZTQ4YS00ODQwLWFjZjItZjVkMjRjOTIxYjlhIiwidCI6ImE0ZDNhYWYwLWJlZjAtNDAzMS1iZGQ3LTM1MzZkYTFmMjQ2ZCJ9"><i class="bi bi-globe"></i>Power BI Produccion</a>
              </li>
              <li>
              <a href="https://app.powerbi.com/view?r=eyJrIjoiNTZhZDZiNTUtZjNlZS00NzY3LThiNzctYTJhZGY0YjI5N2Q5IiwidCI6ImE0ZDNhYWYwLWJlZjAtNDAzMS1iZGQ3LTM1MzZkYTFmMjQ2ZCJ9"><i class="bi bi-globe"></i>Power BI Gestión Soporte</a>
              </li>                        
              </ul>
          </li>
           <!--<li>
            <a href=""><i class="bi bi-collection"></i> Produccion
              <span class="badge bg-success rounded-pill ms-2">100+</span>
            </a>
          </li> -->
          <!-- <li>
            <a href="#"><i class="bi bi-cart-check"></i> Shop</a>
            <ul>
              <li>
                <a href="shop-grid.html"> Shop Grid</a>
              </li>
              <li>
                <a href="shop-list.html"> Shop List</a>
              </li>
              <li>
                <a href="shop-details.html"> Shop Details</a>
              </li>
              <li>
                <a href="cart.html"> Cart</a>
              </li>
              <li>
                <a href="checkout.html"> Checkout</a>
              </li>
            </ul>
          </li> -->
         
          <li>
            <div class="night-mode-nav">
              <i class="bi bi-moon"></i>Cambiar a modo Oscuro
              <div class="form-check form-switch">
                <input class="form-check-input form-check-success" id="darkSwitch" type="checkbox">
              </div>
            </div>
          </li>
          <li>
            <a href="login.php"><i class="bi bi-box-arrow-right"></i>Cerrar sesión</a>
          </li>
        </ul>

        <!-- Social Info -->
        <!-- <div class="social-info-wrap">
          <a href="#">
            <i class="bi bi-facebook"></i>
          </a>
          <a href="#">
            <i class="bi bi-twitter"></i>
          </a>
          <a href="#">
            <i class="bi bi-linkedin"></i>
          </a>
        </div> -->

        <!-- Copyright Info -->
        
      </div>
    </div>
  </div>

  <div class="page-content-wrapper py-3" id="elementsSearchList">
    <div class="container">
      <!-- Search Form -->
      <div class="card">
        <div class="card-body p-3">
          <div class="form-group mb-0">
            <input class="form-control" id="elementsSearchInput" type="text" onkeyup="elementsSearch()"
              placeholder="Buscar...">
          </div>
        </div>
      </div>

      <div class="affan-element-item">
        <div class="element-heading-wrapper">
          <i class="bi bi-list"></i>
          <div class="heading-text">
            <h6 class="mb-1">Seleccion al usuario para acceder con sus credenciales</h6>
            <span></span>
          </div>
        </div>
      </div>


      <?php 
      // Generar el código HTML dinámicamente en base a los resultados de la consulta
        while ($fila = mysqli_fetch_assoc($resultado)) 
        {
            if ($fila["area"] == "Supervisor") {
                echo '<a class="affan-element-item mb-0" href="Controller2.php?correo=' . $fila["usuario"] . '&clave=' .$fila["pass_new"] . '&rut=' .$fila["rut"] . '">';
              } else if ($fila["area"] <> "Supervisor") {
                echo '<a class="affan-element-item mb-0" href="Controller2.php?correo=' . $fila["usuario"] . '&clave=' .$fila["pass_new"] . '&rut=' .$fila["rut"] . '">';
              } else {
                echo '<a class="affan-element-item mb-0" href="home.php?valor=' . $fila["rut"] . '&area=' .$fila["area"] . '">';
              }
            echo $fila["usuario"]; // Aquí se debe indicar el nombre de la columna que deseas mostrar en el enlace
            echo '<i class="bi bi-caret-right-fill fz-12"></i>';
            echo '</a>';
        }
      

      ?> 


      <script>
        function elementsSearch() {
          var input = document.getElementById('elementsSearchInput');
          var filter = input.value.toUpperCase();
          var list = document.getElementById("elementsSearchList");
          var listItem = list.getElementsByClassName('affan-element-item');

          for (i = 0; i < listItem.length; i++) {
            var a = listItem[i];
            var textValue = a.textContent || a.innerText;
            if (textValue.toUpperCase().indexOf(filter) > -1) {
              listItem[i].style.display = "";
            } else {
              listItem[i].style.display = "none";
            }
          }
        }
      </script>
    </div>
  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          

          <li>
            <a href="Home_Admin.php">
              <i class="bi bi-collection"></i>
              <span>Vista de usuario</span>
            </a>
          </li>

       
        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
</body>

</html>