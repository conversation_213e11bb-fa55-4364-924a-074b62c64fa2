<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Aumentar límites para evitar timeouts
ini_set('max_execution_time', 300);
ini_set('memory_limit', '256M');

// Log para debugging
error_log("=== INTELETGROUP_EXPORT_XLS_NATIVE.PHP INICIADO ===");

// Iniciar sesión
session_start();

// Verificar autenticación y permisos de administrador
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    die("Error: Usuario no autenticado o sin permisos para InteletGroup.");
}

// Prevenir caché del navegador
header("Cache-Control: no-cache, no-store, must-revalidate");
header("Pragma: no-cache");
header("Expires: 0");

// Incluir archivos necesarios
require_once 'con_db.php';

// Verificar conexión a base de datos
if (!isset($mysqli) || $mysqli->connect_error) {
    die("Error de conexión a la base de datos");
}

// Obtener parámetros de filtros (desde POST o GET)
$filtro_ejecutivo = $_POST['ejecutivo'] ?? $_GET['ejecutivo'] ?? 'todos';
$filtro_periodo = $_POST['periodo'] ?? $_GET['periodo'] ?? 'año';
$filtro_fecha_inicio = $_POST['fecha_inicio'] ?? $_GET['fecha_inicio'] ?? date('Y-01-01');
$filtro_fecha_fin = $_POST['fecha_fin'] ?? $_GET['fecha_fin'] ?? date('Y-12-31');

// Calcular fechas según el periodo seleccionado
switch($filtro_periodo) {
    case 'hoy':
        $filtro_fecha_inicio = date('Y-m-d');
        $filtro_fecha_fin = date('Y-m-d');
        break;
    case 'semana':
        $filtro_fecha_inicio = date('Y-m-d', strtotime('monday this week'));
        $filtro_fecha_fin = date('Y-m-d', strtotime('sunday this week'));
        break;
    case 'mes_actual':
        $filtro_fecha_inicio = date('Y-m-01');
        $filtro_fecha_fin = date('Y-m-t');
        break;
    case 'trimestre':
        $trimestre = ceil(date('n') / 3);
        $filtro_fecha_inicio = date('Y-') . sprintf('%02d', ($trimestre - 1) * 3 + 1) . '-01';
        $filtro_fecha_fin = date('Y-m-t', strtotime($filtro_fecha_inicio . ' +2 months'));
        break;
    case 'año':
        $filtro_fecha_inicio = date('Y-01-01');
        $filtro_fecha_fin = date('Y-12-31');
        break;
}

// Construir condición WHERE para filtros
$where_conditions = ["1=1"];
$params = [];
$types = "";

if ($filtro_ejecutivo !== 'todos') {
    $where_conditions[] = "p.usuario_id = ?";
    $params[] = $filtro_ejecutivo;
    $types .= "i";
}

$where_conditions[] = "DATE(p.fecha_registro) BETWEEN ? AND ?";
$params[] = $filtro_fecha_inicio;
$params[] = $filtro_fecha_fin;
$types .= "ss";

$where_clause = implode(" AND ", $where_conditions);

// Consulta para obtener los datos
$query = "
    SELECT
        p.id, 
        p.razon_social, 
        p.rut_cliente, 
        p.tipo_persona,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        p.fecha_registro
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE (u.proyecto = 'inteletGroup' OR u.proyecto IS NULL) AND " . $where_clause . "
    ORDER BY p.fecha_registro DESC";

$stmt = $mysqli->prepare($query);
if (!$stmt) {
    die("Error preparando consulta: " . $mysqli->error);
}

if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}

if (!$stmt->execute()) {
    die("Error ejecutando consulta: " . $stmt->error);
}

// Obtener resultados usando bind_result para compatibilidad con PHP 7.3
$id = $razon_social = $rut_cliente = $tipo_persona = $ejecutivo_nombre_usuario = $fecha_registro = null;
$stmt->bind_result($id, $razon_social, $rut_cliente, $tipo_persona, $ejecutivo_nombre_usuario, $fecha_registro);

$prospectos = [];
while ($stmt->fetch()) {
    // Obtener datos de documentos para cada prospecto
    $doc_query = "SELECT 
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p2
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p2.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p2.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE p2.id = ?";
    
    $doc_stmt = $mysqli->prepare($doc_query);
    if ($doc_stmt) {
        $doc_stmt->bind_param("i", $id);
        $doc_stmt->execute();
        $doc_stmt->bind_result($total_documentos, $obligatorios_completados, $total_obligatorios);
        $doc_stmt->fetch();
        $doc_stmt->close();
    } else {
        $total_documentos = $obligatorios_completados = $total_obligatorios = 0;
    }
    
    $porcentaje_completado = $total_obligatorios > 0 ? round(($obligatorios_completados / $total_obligatorios) * 100) : 0;
    
    $prospectos[] = [
        'ID' => $id,
        'Razón Social' => $razon_social,
        'RUT' => $rut_cliente,
        'Tipo' => $tipo_persona,
        'Ejecutivo' => $ejecutivo_nombre_usuario,
        'Documentos' => $obligatorios_completados . '/' . $total_obligatorios . ' (Total: ' . $total_documentos . ')',
        'Completitud' => $porcentaje_completado . '%',
        'Fecha Registro' => date('d/m/Y', strtotime($fecha_registro))
    ];
}

$stmt->close();

// Verificar si hay datos para exportar
if (empty($prospectos)) {
    die("No hay datos para exportar con los filtros seleccionados");
}

// Generar nombre de archivo descriptivo
$fecha_actual = date('Y-m-d_H-i-s');
$periodo_texto = $filtro_periodo;
$ejecutivo_texto = $filtro_ejecutivo === 'todos' ? 'todos' : 'ejecutivo_' . $filtro_ejecutivo;
$nombre_archivo = "prospectos_inteletgroup_{$periodo_texto}_{$ejecutivo_texto}_{$fecha_actual}.xls";

// Función para generar XLS nativo usando formato BIFF
function generarXLSNativo($datos, $nombre_archivo) {
    // Headers para descarga XLS
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $nombre_archivo . '"');
    header('Cache-Control: max-age=0');
    
    // Crear contenido XLS usando formato BIFF simplificado
    // Este es un formato que Excel puede leer sin problemas
    
    echo pack("ssssss", 0x809, 0x8, 0x0, 0x10, 0x0, 0x0); // BOF (Beginning of File)
    
    if (!empty($datos)) {
        $row = 0;
        
        // Escribir encabezados
        $headers = array_keys($datos[0]);
        $col = 0;
        foreach ($headers as $header) {
            escribirCeldaTexto($row, $col, $header);
            $col++;
        }
        $row++;
        
        // Escribir datos
        foreach ($datos as $fila) {
            $col = 0;
            foreach ($fila as $valor) {
                escribirCeldaTexto($row, $col, $valor);
                $col++;
            }
            $row++;
        }
    }
    
    echo pack("ss", 0x0A, 0x00); // EOF (End of File)
}

// Función para escribir una celda de texto en formato BIFF
function escribirCeldaTexto($row, $col, $valor) {
    $valor = (string)$valor;
    $length = strlen($valor);
    
    // Limitar longitud para evitar problemas
    if ($length > 255) {
        $valor = substr($valor, 0, 255);
        $length = 255;
    }
    
    // LABEL record (0x204)
    echo pack("ssssss", 0x204, 8 + $length, $row, $col, 0x0, $length);
    echo $valor;
}

try {
    // Generar y enviar archivo XLS
    generarXLSNativo($prospectos, $nombre_archivo);
    error_log("=== EXPORTACIÓN XLS NATIVA COMPLETADA EXITOSAMENTE ===");
    exit;
    
} catch (Exception $e) {
    error_log("ERROR FATAL en la generación del XLS: " . $e->getMessage());
    die("Error al generar el archivo XLS: " . $e->getMessage());
}
?>
