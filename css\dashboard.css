/**
 * Estilos para el dashboard administrativo
 */

:root {
    --primary-color: #3b82f6;
    --primary-dark: #1e40af;
    --primary-medium: #2563eb;
    --primary-light: #60a5fa;
    --primary-blue: #2699fb;
    
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    
    --text-dark: #1f2937;
    --text-medium: #4b5563;
    --text-light: #9ca3af;
    
    --background: #f3f4f6;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

body {
    background-color: var(--background);
    color: var(--text-dark);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Header estilizado */
.page-header {
    background-color: white;
    padding: 1.5rem 0;
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

.page-header h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-dark);
    margin-bottom: 0.25rem;
}

.user-section {
    display: flex;
    align-items: center;
}

.user-info-container {
    text-align: right;
}

.user-name {
    font-weight: 600;
    color: var(--text-dark);
}

.user-role {
    font-size: 0.8rem;
    color: var(--text-medium);
}

.logout-btn {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    background-color: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-medium);
    transition: all 0.2s ease;
}

.logout-btn:hover {
    background-color: #e5e7eb;
    color: var(--danger-color);
}

/* Tarjetas del dashboard */
.dashboard-card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
    background: white;
    padding: 1.5rem;
    height: 100%;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.kpi-card {
    text-align: center;
    padding: 1.2rem 0.8rem;
}

.kpi-value {
    font-size: 2.4rem;
    font-weight: 700;
    color: var(--primary-dark);
    line-height: 1;
    margin-bottom: 0.3rem;
}

.kpi-label {
    color: var(--secondary-color);
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.4px;
    margin-bottom: 0.2rem;
}

.kpi-change {
    font-size: 0.8rem;
    font-weight: 600;
    margin-top: 0.3rem;
}

.kpi-change.positive {
    color: var(--success-color);
}

.kpi-change.negative {
    color: var(--danger-color);
}

/* Contenedores para gráficos */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Sección de filtros */
.filter-section {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

/* Contenedor de tablas */
.table-container {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* Estilos para las tabs */
.nav-tabs {
    border-bottom: 2px solid #e5e7eb;
}

.nav-tabs .nav-link {
    color: var(--secondary-color);
    border: none;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    color: var(--primary-medium);
}

.nav-tabs .nav-link.active {
    color: var(--primary-dark);
    background: none;
    border: none;
    border-bottom: 3px solid var(--primary-medium);
}

/* Badges para los tipos de persona */
.badge-natural {
    background-color: #3b82f6;
    color: white;
}

.badge-juridica {
    background-color: #10b981;
    color: white;
}

/* Tablas responsivas */
.table-responsive {
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* Iconos en estadísticas */
.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.6rem;
    font-size: 1.2rem;
    color: white;
}

.stat-icon.blue {
    background: rgba(38, 153, 251, 0.1);
    color: var(--primary-blue);
}

.stat-icon.green {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.stat-icon.yellow {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.stat-icon.purple {
    background: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
}

/* Barras de progreso personalizadas */
.progress-custom {
    width: 100%;
    background-color: #e5e7eb;
    border-radius: 9999px;
    overflow: hidden;
}

.progress-bar-custom {
    height: 100%;
    border-radius: 9999px;
}

.progress-bar-custom.green {
    background-color: var(--success-color);
}

.progress-bar-custom.yellow {
    background-color: var(--warning-color);
}

.progress-bar-custom.red {
    background-color: var(--danger-color);
}

/* Estilos para tablas de datos */
table.dataTable thead th {
    background-color: #f9fafb;
    color: var(--text-medium);
    font-weight: 600;
    font-size: 0.875rem;
}

table.dataTable tbody tr:hover {
    background-color: #f3f4f6;
}

/* Botones de acción */
.btn-outline-primary {
    color: var(--primary-medium);
    border-color: var(--primary-light);
}

.btn-outline-primary:hover {
    background-color: var(--primary-medium);
    border-color: var(--primary-medium);
}

/* Responsive */
@media (max-width: 768px) {
    .page-header h1 {
        font-size: 1.5rem;
    }
    
    .kpi-value {
        font-size: 2rem;
    }
    
    .kpi-label {
        font-size: 0.7rem;
    }
    
    .dashboard-card {
        padding: 1rem;
    }
}