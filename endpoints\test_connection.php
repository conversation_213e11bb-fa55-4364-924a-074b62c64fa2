<?php
/**
 * Test simple para verificar la conexión a la base de datos
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Incluir conexión a la base de datos
    require_once dirname(__DIR__) . '/con_db.php';
    
    // Verificar que las variables estén disponibles
    if (!isset($conn)) {
        throw new Exception('Variable $conn no está disponible');
    }
    
    // Hacer una consulta simple
    $sql = "SELECT COUNT(*) as total FROM tb_inteletgroup_tipos_documento";
    $result = $conn->query($sql);
    
    if (!$result) {
        throw new Exception('Error en consulta: ' . $conn->error);
    }
    
    $row = $result->fetch_assoc();
    
    echo json_encode([
        'success' => true,
        'message' => 'Conexión exitosa',
        'total_documentos' => $row['total'],
        'variables_disponibles' => [
            'conn' => isset($conn) ? 'Sí' : 'No',
            'mysqli' => isset($mysqli) ? 'Sí' : 'No'
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'variables_disponibles' => [
            'conn' => isset($conn) ? 'Sí' : 'No',
            'mysqli' => isset($mysqli) ? 'Sí' : 'No'
        ]
    ]);
}
?>
