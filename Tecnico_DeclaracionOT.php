<?php

header('Content-Type: text/html; charset=UTF-8');
//Iniciar una nueva sesión o reanudar la existente.
session_start();

$inc = include("con_db.php");



$ot_dec_fue_sla = $conex->query("

      SELECT  
      '17171717' Orden , '' `<PERSON><PERSON> fin#` , '' FechaDeclaracion 
       ,10 Ptos_referencial, '123532532' TP_DECLARADA ,'SDHGDSHD'  SLA_DECLARACION 
       UNION ALL
      SELECT  
        Orden , `Fecha fin#` , FechaDeclaracion 
        , SUM(Ptos_referencial) Ptos_referencial , TP_DECLARADA , SLA_DECLARACION 
      FROM  
          tb_paso_pyNdc tppn  
      WHERE 
          DATE_FORMAT(MES_CONTABLE, '%Y%m')  = '202305'
      AND 
        TP_DECLARADA='Si' AND SLA_DECLARACION = 'Mayor 72 horas'
      AND
        RutTecnicoOrig  =  '" . $_SESSION['RUT'] . "' 
      GROUP BY
          ORDEN , `Fecha fin#` , Fe<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> , TP_DECLARADA , SLA_DECLARACION 

        ");

$rowtkpi = mysqli_fetch_array($ot_dec_fue_sla);





$ot_pen_fue_sla = $conex->query("

      
    
      SELECT  
        Orden , `Fecha fin#` , FechaDeclaracion 
        , SUM(Ptos_referencial) Ptos_referencial , TP_DECLARADA , SLA_DECLARACION 
      FROM  
          tb_paso_pyNdc tppn  
      WHERE 
          DATE_FORMAT(MES_CONTABLE, '%Y%m')  = '202305'
       AND    SLA_DECLARACION ='PEND_OUTSLA' 
      AND
        RutTecnicoOrig  =  '" . $_SESSION['RUT'] . "' 
      GROUP BY
          ORDEN , `Fecha fin#` , FechaDeclaracion , TP_DECLARADA , SLA_DECLARACION 

        ");




$ot_pen_IN_sla = $conex->query("

      
        SELECT  
          Orden , `Fecha fin#` , FechaDeclaracion 
          , SUM(Ptos_referencial) Ptos_referencial , TP_DECLARADA , SLA_DECLARACION 
          , DIFDIAS
        FROM  
            tb_paso_pyNdc tppn  
        WHERE 
            DATE_FORMAT(MES_CONTABLE, '%Y%m')  = '202305'
         AND    TP_DECLARADA='No'  
        AND
          RutTecnicoOrig  =  '" . $_SESSION['RUT'] . "' 
        GROUP BY
            ORDEN , `Fecha fin#` , FechaDeclaracion , TP_DECLARADA , SLA_DECLARACION  , DIFDIAS
  
          ");




$declara = $conex->query("
          SELECT  
              SUM(IF(TP_DECLARADA='No',Q_ordenes,0)) PEND 
              ,SUM(IF(TP_DECLARADA='No' AND SLA_DECLARACION = 'PEND_OUTSLA' ,Q_ordenes,0)) PEND_OUT
              ,SUM(IF(TP_DECLARADA='No' AND SLA_DECLARACION = 'PEND_SLA' ,Q_ordenes,0)) PEND_SLA
              , SUM(IF(TP_DECLARADA='Si',Q_ordenes,0)) DECLARADA 
              , SUM(IF(TP_DECLARADA='Si' AND SLA_DECLARACION = 'Mayor 72 horas',Q_ordenes,0)) DECLAR_OUTSLA 
              , SUM(IF(TP_DECLARADA='Si' AND SLA_DECLARACION = 'Mayor 72 horas',Ptos,0)) PTOS_DESC 
              , SUM(IF(TP_DECLARADA='Si' AND SLA_DECLARACION = 'SLA OK',Q_ordenes,0)) DECLAR_SLA 
            FROM 
        (
            SELECT 
              TP_DECLARADA  , SLA_DECLARACION 
              , RutTecnicoOrig  
              , SUM(Ptos_referencial) Ptos 
              , COUNT(distinct Orden ) Q_ordenes
            FROM 
                tb_paso_pyNdc tppn   
            WHERE
                RutTecnicoOrig  =  '" . $_SESSION['RUT'] . "'
            GROUP BY
                  TP_DECLARADA  , SLA_DECLARACION , RutTecnicoOrig         
        ) A   ");

$rowtdeclara = mysqli_fetch_array($declara);






$carga = $conex->query("
      SELECT COUNT(1) total
      FROM tb_paso_pyNdc tppn  
      WHERE DATE_FORMAT(`Fecha fin#`, '%d/%m/%Y') =    DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 DAY), '%d/%m/%Y')
      ");

$cargarow = mysqli_fetch_array($carga);





?>



<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="APP TQW">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- T\chathe above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>TQW APP - INDICADORES</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/logo_con.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>
      </div>
    </div>
  </div>
 -->
  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">



      <!-- Header Content -->
      <div class="header-content position-relative d-flex align-items-center justify-content-between">



        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0">INDICADORES</h6>
        </div>

        <div class="navbar--toggler" id="affanNavbarToggler" data-bs-toggle="offcanvas" data-bs-target="#affanOffcanvas"
          aria-controls="affanOffcanvas">
          <span class="d-block"></span>
          <span class="d-block"></span>
          <span class="d-block"></span>
        </div>

        <!-- Settings -->
        <!-- <div class="setting-wrapper">
          <div class="setting-trigger-btn" id="settingTriggerBtn">
            <i class="bi bi-gear"></i>
            <span></span>
          </div>
        </div> -->
      </div>
    </div>
  </div>

  <div class="offcanvas offcanvas-start" id="affanOffcanvas" data-bs-scroll="true" tabindex="-1"
    aria-labelledby="affanOffcanvsLabel">

    <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
      aria-label="Close"></button>

    <div class="offcanvas-body p-0">
      <div class="sidenav-wrapper">
        <!-- Sidenav Profile -->
        <div class="sidenav-profile bg-gradient">
          <div class="sidenav-style1"></div>

          <!-- User Thumbnail -->
          <div class="user-profile">
            <img src="img/bg-img/2.jpg" alt="">
          </div>

          <!-- User Info -->
          <div class="user-info">
            <h6 class="user-name mb-0">
              <?php echo $_SESSION['usuario'] ?>
            </h6>

          </div>
        </div>

        <!-- Sidenav Nav -->
        <ul class="sidenav-nav ps-0">
          <li>
            <a href="home.php"><i class="bi bi-house-door"></i>HOME</a>
          </li>
          <li>
            <a href=""><i class="bi bi-folder2-open"></i>PRODUCCION NDC
              <!-- <span class="badge bg-danger rounded-pill ms-2">220+</span> -->
            </a>
          </li>
          <li>
            <a href="Tecnico_calidad.php"><i class="bi bi-collection"></i>CALIDAD REACTIVA
              <span class="badge bg-success rounded-pill ms-2"></span>
            </a>
          </li>

          <li>
            <a href=""><i class="bi bi-collection"></i>DECLARACION DE ORDENES
              <span class="badge bg-success rounded-pill ms-2"></span>
            </a>
          </li>



          <li>
            <a href="#" class="nav-url"><i class="bi bi-globe"></i>ACCESOS DIRECTOS<span
                class="dropdown-icon"></span></a>
            <ul>
              <li>
                <a href="https://entrenamientovirtual.cl/course/"><i class="bi bi-globe"></i>Desafio Tecnico
                  <span class="badge bg-success rounded-pill ms-2"></span>
                </a>
              </li>
              <li>
                <a href="http://172.17.113.6/eps/index.do"><i class="bi bi-globe"></i>NDC Declaracion Material
                  <span class="badge bg-success rounded-pill ms-2"></span>
                </a>
              </li>
              <li>
                <a href="https://forms.gle/3m3ZUDby4ie5Y5Us7"><i class="bi bi-globe"></i>Registro Serie instaladas
                  <span class="badge bg-success rounded-pill ms-2"></span>
                </a>
              </li>
              <li>
                <a href="https://lla.cloudcheck.net/t1gui/login-page"><i class="bi bi-globe"></i>Cloudcheck
                  <span class="badge bg-success rounded-pill ms-2"></span>
                </a>
              </li>
            </ul>
          </li>
          <li>
            <a href=""><i class="bi bi-calendar-check-fill"></i>TURNOS
              <span class="badge bg-success rounded-pill ms-2"></span>
            </a>
          </li>
          <li>
            <div class="night-mode-nav">
              <i class="bi bi-moon"></i>CAMBIAR MODO OSCURO
              <div class="form-check form-switch">
                <input class="form-check-input form-check-success" id="darkSwitch" type="checkbox">
              </div>
            </div>
          </li>
          <li>
            <a href="login.php"><i class="bi bi-box-arrow-right"></i>CERRAR SESIÓN</a>
          </li>
        </ul>

        <!-- Social Info -->



      </div>
    </div>
  </div>


  <div class="page-content-wrapper py-3">

  <div class="container">
      <div class="card-body direction-rtl">





        <?php
        if ($cargarow['total'] == 0) {
          // Si es mayor a 0, muestras el elemento web
          echo '
            <div class="toast toast-autohide custom-toast-1 toast-warning mb-2" role="alert" aria-live="assertive"
            aria-atomic="true" data-bs-delay="8000" data-bs-autohide="true">
            <div class="toast-body">
              <i class="bi bi-shield-fill-exclamation h1 mb-0"></i>
              <div class="toast-text ms-3 me-2">
                <p class="mb-0">INFORMACION PARCIAL A LA ESPERA DE LA INFORMACIÓN DEL DÍA AYER DESDE NDC</p>
                 </div>
            </div>

            <button class="btn btn-close btn-close-white position-absolute p-1" type="button" data-bs-dismiss="toast"
            aria-label="Close"></button>
          </div>




            ';
        } else {
          // Si es mayor a 0, muestras el elemento web
          echo '
            <div class="toast toast-autohide custom-toast-1 toast-success mb-2" role="alert" aria-live="assertive"
            aria-atomic="true" data-bs-delay="8000" data-bs-autohide="true">
            <div class="toast-body">
            <i class="bi bi-shield-check text-white h1 mb-0"></i>
            <div class="toast-text ms-3 me-2">
                <p class="mb-0">RESUMEN ACTUALIZADO AL DÍA DE AYER DESDE NDC</p>
             
              </div>
            </div>

            <button class="btn btn-close position-absolute p-1" type="button" data-bs-dismiss="toast"
              aria-label="Close"></button>
          </div>


            ';
        }
        ?>



      </div>

    </div>


    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading">
        <h6>DECLARACION DE ORDENES</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body direction-rtl">
          <div class="row">
            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart mb-2 text-warning"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo $rowtdeclara['PEND']; ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">OT PENDIENTES</p>
              </div>
            </div>

            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line mb-2 text-danger"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo $rowtdeclara['PEND_OUT']; ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">PENDIENTE FUERA DE PLAZO</p>
              </div>
            </div>

            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart mb-2 text-warning"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo $rowtdeclara['PEND_SLA']; ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">PENDIENTE DENTRO DE PLAZO</p>
              </div>
            </div>



            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart mb-2 text-warning"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo $rowtdeclara['DECLARADA']; ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">OT DECLARADAS</p>
              </div>
            </div>
            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line mb-2 text-danger"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo $rowtdeclara['DECLAR_OUTSLA']; ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">OT FUERAS DE PLAZO</p>
              </div>
            </div>
            <div class="col-4">
              <!-- Single Counter -->
              <div class="single-counter-wrap text-center">
                <i class="bi-bar-chart-line mb-2 text-danger"></i>
                <h4 class="mb-0">
                  <span class="counter">
                    <?php echo round($rowtdeclara['PTOS_DESC'], 2); ?>
                  </span>
                </h4>
                <span class="solid-line"></span>
                <p class="mb-0 fz-12">PUNTOS A DESCONTAR</p>
              </div>
            </div>



          </div>
        </div>
      </div>
    </div>


    <div class="page-content-wrapper py-3">
      <!-- Element Heading -->
      <div class="container">
        <div class="element-heading">
          <h6>DETALLE DE OT SEGUN ESTEN DECLARADAS O PENDIENTES POR DECLARAR </h6>
        </div>
      </div>

      <div class="container">
        <div class="card">
          <div class="card-body">
            <div class="standard-tab">
              <ul class="nav rounded-lg mb-2 p-2 shadow-sm" id="affanTabs1" role="tablist">
                <li class="nav-item" role="presentation">
                  <button class="btn active" id="bootstrap-tab" data-bs-toggle="tab" data-bs-target="#bootstrap"
                    type="button" role="tab" aria-controls="bootstrap" aria-selected="true">OT DECLARADAS FUERA DE
                    PLAZO</button>
                </li>

                <li class="nav-item" role="presentation">
                  <button class="btn" id="dark-tab" data-bs-toggle="tab" data-bs-target="#dark" type="button" role="tab"
                    aria-controls="dark" aria-selected="false">OT PENDIENTES</button>
                </li>
              </ul>

              <div class="tab-content rounded-lg p-3 shadow-sm" id="affanTabs1Content"> 
                <div class="tab-pane fade show active" id="bootstrap" role="tabpanel" aria-labelledby="bootstrap-tab">

                  <a class="btn m-1 btn-danger" href="#" onclick="exportTableToXLSX(); return false;">
                    <i class="bi bi-arrow-down"></i> Descargar
                  </a>

                  <table class="table mb-0 table-striped" id="dataTable4">
                    <thead>
                      <thead>
                        <tr>
                          <th>OT</th>
                          <th>FECHA FIN</th>
                          <th>FECHA DECLARA</th>
                          <th>PTOS</th>
                        </tr>
                      </thead>
                    <tbody>
                      <?php
                      while ($row2 = mysqli_fetch_array($ot_dec_fue_sla)) { ?>
                        <tr>
                          <td>
                            <?php echo $row2['Orden']; ?>
                          </td>
                          <td>
                            <?php echo $row2['Fecha fin#']; ?>
                          </td>
                          <td>
                            <?php echo $row2['FechaDeclaracion']; ?>
                          </td>
                          <td>
                            <?php echo ROUND($row2['Ptos_referencial'], 2); ?>
                          </td>
                        </tr>

                        <?php
                      }
                      ?>

                    </tbody>
                  </table>



                </div>



                <div class="tab-pane fade" id="dark" role="tabpanel" aria-labelledby="dark-tab">
                  <a class="btn m-1 btn-danger" href="#" onclick="exportTableToXLSX2(); return false;">
                    <i class="bi bi-arrow-down"></i> Descargar
                  </a>

                  <table class="table mb-0 table-striped" id="dataTable5">
                    <thead>
                      <thead>
                        <tr>
                          <th>OT</th>
                          <th>FECHA FIN</th>
                      
                          <th>PTOS</th>
                          <th>DIF DIAS</th>
                        </tr>
                      </thead>
                    <tbody>
                      <?php
                      while ($row3 = mysqli_fetch_array($ot_pen_IN_sla)) { ?>
                        <tr>
                          <td>
                            <?php echo $row3['Orden']; ?>
                          </td>
                          <td>
                            <?php echo $row3['Fecha fin#']; ?>
                          </td>
                         
                          <td>
                            <?php echo ROUND($row3['Ptos_referencial'], 2); ?>
                          </td>
                          <td>
                            <?php echo ROUND($row3['DIFDIAS'], 2); ?>
                          </td>
                        </tr>

                        <?php
                      }
                      ?>

                    </tbody>
                  </table>
                </div>
               </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer Nav -->
    <div class="footer-nav-area" id="footerNav">
      <div class="container px-0">
        <!-- Footer Content -->
        <div class="footer-nav position-relative">
          <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
            <li class="active">
              <a href="home.php">
                <i class="bi bi-house"></i>
                <span>Home</span>
              </a>
            </li>








          </ul>
        </div>
      </div>
    </div>



    <script>

      function exportTableToXLSX() {
        const filename = 'tabla.xlsx';
        const table = document.getElementById('dataTable4');



        // Crea un objeto Workbook de SheetJS
        const wb = XLSX.utils.table_to_book(table);


        // Descarga el archivo XLSX
        XLSX.writeFile(wb, filename);
      }  
    </script>


    <script>

      function exportTableToXLSX2() {
        const filename = 'tabla.xlsx';
        const table = document.getElementById('dataTable5');



        // Crea un objeto Workbook de SheetJS
        const wb = XLSX.utils.table_to_book(table);


        // Descarga el archivo XLSX
        XLSX.writeFile(wb, filename);
      }  
    </script>



    <script src="https://cdn.jsdelivr.net/npm/xlsx/dist/xlsx.full.min.js"></script>

    <!-- All JavaScript Files -->
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/slideToggle.min.js"></script>
    <script src="js/internet-status.js"></script>
    <script src="js/tiny-slider.js"></script>
    <script src="js/venobox.min.js"></script>
    <script src="js/countdown.js"></script>
    <script src="js/rangeslider.min.js"></script>
    <script src="js/vanilla-dataTables.min.js"></script>
    <script src="js/index.js"></script>
    <script src="js/imagesloaded.pkgd.min.js"></script>
    <script src="js/isotope.pkgd.min.js"></script>
    <script src="js/dark-rtl.js"></script>
    <script src="js/active.js"></script>
    <script src="js/pwa.js"></script>
    <script src="js/chart-active.js"></script>
    <script src="js/apexcharts.min.js"></script>
</body>

</html>