<?php
// Configuración de errores
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Iniciar sesión si no está iniciada
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Incluir utilidades
require_once 'cache_utils.php';
no_cache_headers();

// Registrar cierre de sesión
if (isset($_SESSION['usuario'])) {
    $username = $_SESSION['usuario'];
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    error_log("[" . date('Y-m-d H:i:s') . "] Logout - Usuario: {$username} - IP: {$ip}");
}

// Limpiar todas las variables de sesión
$_SESSION = array();

// Eliminar la cookie de sesión si existe
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destruir la sesión
session_destroy();

// Siempre redirigir a login.php
header("Location: login.php");
exit;
