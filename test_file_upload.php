<?php
// Archivo de prueba para verificar la funcionalidad de carga de archivos
session_start();

// Simular datos de sesión para pruebas
if (!isset($_SESSION['usuario_id'])) {
    $_SESSION['usuario_id'] = 1;
    $_SESSION['nombre_usuario'] = 'Test User';
    $_SESSION['proyecto'] = 'inteletGroup';
}

echo "<h2>Test de Carga de Archivos - InteletGroup</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>Datos POST recibidos:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    echo "<h3>Archivos recibidos:</h3>";
    echo "<pre>";
    print_r($_FILES);
    echo "</pre>";
    
    if (isset($_FILES['documentos'])) {
        echo "<h3>Procesando archivos:</h3>";
        $files = $_FILES['documentos'];
        $file_count = count($files['name']);
        
        for ($i = 0; $i < $file_count; $i++) {
            echo "<p>Archivo $i:</p>";
            echo "<ul>";
            echo "<li>Nombre: " . htmlspecialchars($files['name'][$i]) . "</li>";
            echo "<li>Tipo: " . htmlspecialchars($files['type'][$i]) . "</li>";
            echo "<li>Tamaño: " . $files['size'][$i] . " bytes</li>";
            echo "<li>Error: " . $files['error'][$i] . "</li>";
            echo "<li>Temporal: " . htmlspecialchars($files['tmp_name'][$i]) . "</li>";
            echo "</ul>";
        }
    }
} else {
?>
<form method="POST" enctype="multipart/form-data">
    <h3>Formulario de Prueba</h3>
    <p>
        <label>RUT Cliente:</label><br>
        <input type="text" name="rut_cliente" value="12345678-9" required>
    </p>
    <p>
        <label>Nombre Ejecutivo:</label><br>
        <input type="text" name="nombre_ejecutivo" value="Test User" required>
    </p>
    <p>
        <label>Documentos:</label><br>
        <input type="file" name="documentos[]" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.xlsx,.xls">
    </p>
    <p>
        <button type="submit">Enviar Prueba</button>
    </p>
</form>
<?php
}
?>
