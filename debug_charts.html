<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Chart.js</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .chart-container {
            width: 500px;
            height: 400px;
            margin: 20px 0;
            border: 2px solid #333;
            padding: 20px;
            background-color: #f0f0f0;
        }
        #debugChart {
            width: 100% !important;
            height: 300px !important;
            border: 2px solid blue;
            background-color: white;
        }
        .debug-info {
            background-color: #e0e0e0;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
        }
    </style>
</head>
<body>
    <h1>Debug Chart.js - Versión 2</h1>
    
    <div class="debug-info">
        <h3>Información de Debug:</h3>
        <p id="chartStatus">Cargando...</p>
        <p id="canvasStatus">Verificando canvas...</p>
        <p id="contextStatus">Verificando contexto...</p>
    </div>
    
    <div class="chart-container">
        <h3>Gráfico de Debug</h3>
        <canvas id="debugChart" width="400" height="300"></canvas>
    </div>

    <!-- Chart.js desde CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        console.log('=== DEBUG CHART.JS INICIADO ===');
        
        function updateStatus(elementId, message) {
            document.getElementById(elementId).textContent = message;
            console.log(message);
        }
        
        // Verificar Chart.js
        if (typeof Chart !== 'undefined') {
            updateStatus('chartStatus', '✓ Chart.js está disponible - Versión: ' + Chart.version);
        } else {
            updateStatus('chartStatus', '✗ Chart.js NO está disponible');
        }
        
        // Esperar a que el DOM esté listo
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM completamente cargado');
            
            // Verificar canvas
            const canvas = document.getElementById('debugChart');
            if (canvas) {
                updateStatus('canvasStatus', '✓ Canvas encontrado - Dimensiones: ' + canvas.width + 'x' + canvas.height);
                
                // Verificar contexto 2D
                try {
                    const ctx = canvas.getContext('2d');
                    if (ctx) {
                        updateStatus('contextStatus', '✓ Contexto 2D obtenido correctamente');
                        
                        // Dibujar algo simple primero
                        ctx.fillStyle = 'red';
                        ctx.fillRect(10, 10, 50, 50);
                        ctx.fillStyle = 'blue';
                        ctx.fillText('Test Canvas', 70, 30);
                        
                        console.log('Dibujo simple completado');
                        
                        // Ahora intentar crear el gráfico
                        if (typeof Chart !== 'undefined') {
                            setTimeout(() => {
                                try {
                                    console.log('Creando gráfico Chart.js...');
                                    
                                    const chartData = {
                                        labels: ['A', 'B', 'C', 'D'],
                                        datasets: [{
                                            label: 'Test Data',
                                            data: [10, 20, 15, 25],
                                            backgroundColor: 'rgba(75, 192, 192, 0.6)',
                                            borderColor: 'rgba(75, 192, 192, 1)',
                                            borderWidth: 2
                                        }]
                                    };

                                    const chart = new Chart(ctx, {
                                        type: 'bar',
                                        data: chartData,
                                        options: {
                                            responsive: false,
                                            maintainAspectRatio: false,
                                            animation: {
                                                duration: 2000
                                            },
                                            scales: {
                                                y: {
                                                    beginAtZero: true
                                                }
                                            }
                                        }
                                    });
                                    
                                    console.log('✓ Gráfico Chart.js creado exitosamente:', chart);
                                    updateStatus('contextStatus', '✓ Gráfico Chart.js creado exitosamente');
                                    
                                } catch (error) {
                                    console.error('✗ Error creando gráfico Chart.js:', error);
                                    updateStatus('contextStatus', '✗ Error creando gráfico: ' + error.message);
                                }
                            }, 1000);
                        }
                        
                    } else {
                        updateStatus('contextStatus', '✗ No se pudo obtener el contexto 2D');
                    }
                } catch (error) {
                    updateStatus('contextStatus', '✗ Error obteniendo contexto: ' + error.message);
                }
            } else {
                updateStatus('canvasStatus', '✗ Canvas NO encontrado');
            }
        });
        
        // También intentar después de que todo esté cargado
        window.addEventListener('load', function() {
            console.log('Window load event disparado');
        });
    </script>
</body>
</html>
