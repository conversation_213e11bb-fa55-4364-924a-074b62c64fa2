/**
 * JavaScript para el formulario de registro de prospectos
 * Incluye validación del lado del cliente, manejo de modal y envío de formulario
 */

// Variables globales
let currentUserName = '';

// Inicialización cuando el DOM está listo
document.addEventListener('DOMContentLoaded', function() {
    initializeProspectForm();
});

// Función de inicialización
function initializeProspectForm() {
    // Obtener nombre del usuario logueado
    getCurrentUserName();
    
    // Configurar event listeners
    setupEventListeners();
    
    // Configurar validación en tiempo real
    setupRealTimeValidation();
}

// Obtener nombre del usuario actual
function getCurrentUserName() {
    // Intentar obtener desde variable global o hacer petición AJAX
    if (typeof window.currentUserName !== 'undefined') {
        currentUserName = window.currentUserName;
    } else {
        // Hacer petición AJAX para obtener el nombre del usuario
        fetch('endpoints/get_user_info.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentUserName = data.nombre_usuario;
                }
            })
            .catch(error => {
                console.error('Error obteniendo información del usuario:', error);
                currentUserName = 'Usuario Actual';
            });
    }
}

// Configurar event listeners
function setupEventListeners() {
    const form = document.getElementById('prospectForm');
    if (form) {
        form.addEventListener('submit', handleFormSubmit);
    }
    
    // Cerrar modal con Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            cerrarModalProspecto();
        }
    });
    
    // Formateo automático de campos
    const rutField = document.getElementById('rut_ejecutivo');
    if (rutField) {
        rutField.addEventListener('input', formatRUT);
    }
    
    const telefonoField = document.getElementById('telefono');
    if (telefonoField) {
        telefonoField.addEventListener('input', formatTelefono);
    }
    
    const razonSocialField = document.getElementById('razon_social');
    if (razonSocialField) {
        razonSocialField.addEventListener('input', formatRazonSocial);
    }
}

// Configurar validación en tiempo real
function setupRealTimeValidation() {
    const requiredFields = ['rut_ejecutivo', 'razon_social', 'telefono'];
    
    requiredFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('blur', () => validateField(fieldId));
            field.addEventListener('input', () => clearFieldError(fieldId));
        }
    });
    
    // Validación específica para email
    const emailField = document.getElementById('email');
    if (emailField) {
        emailField.addEventListener('blur', () => validateEmail());
    }
}

// Abrir modal de prospecto
function abrirModalProspecto() {
    const modal = document.getElementById('prospectModal');
    if (modal) {
        modal.style.display = 'block';
        
        // Auto-poblar nombre del ejecutivo
        const nombreEjecutivoField = document.getElementById('nombre_ejecutivo');
        if (nombreEjecutivoField) {
            nombreEjecutivoField.value = currentUserName;
        }
        
        // Limpiar formulario
        limpiarFormulario();
        
        // Focus en primer campo editable
        const rutField = document.getElementById('rut_ejecutivo');
        if (rutField) {
            setTimeout(() => rutField.focus(), 100);
        }
    }
}

// Cerrar modal de prospecto
function cerrarModalProspecto() {
    const modal = document.getElementById('prospectModal');
    if (modal) {
        modal.style.display = 'none';
        limpiarFormulario();
        ocultarErrores();
    }
}

// Limpiar formulario
function limpiarFormulario() {
    const form = document.getElementById('prospectForm');
    if (form) {
        form.reset();
        
        // Restaurar nombre del ejecutivo
        const nombreEjecutivoField = document.getElementById('nombre_ejecutivo');
        if (nombreEjecutivoField) {
            nombreEjecutivoField.value = currentUserName;
        }
        
        // Limpiar clases de error/éxito
        const formControls = form.querySelectorAll('.form-control');
        formControls.forEach(control => {
            control.classList.remove('error', 'success');
        });
    }
}

// Formatear RUT
function formatRUT(e) {
    let value = e.target.value.replace(/[^0-9kK]/g, '');
    e.target.value = value;
}

// Formatear teléfono
function formatTelefono(e) {
    let value = e.target.value.replace(/[^0-9]/g, '');
    e.target.value = value;
}

// Formatear razón social
function formatRazonSocial(e) {
    let value = e.target.value.toUpperCase();
    // Remover acentos y caracteres especiales
    value = value.normalize("NFD").replace(/[\u0300-\u036f]/g, "");
    // Permitir solo letras, números, espacios y algunos caracteres básicos
    value = value.replace(/[^A-Z0-9\s\.\-&]/g, '');
    e.target.value = value;
}

// Validar campo individual
function validateField(fieldId) {
    const field = document.getElementById(fieldId);
    if (!field) return true;
    
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';
    
    switch (fieldId) {
        case 'rut_ejecutivo':
            if (!value) {
                isValid = false;
                errorMessage = 'El RUT es requerido';
            } else if (!validarRUT(value)) {
                isValid = false;
                errorMessage = 'El RUT no es válido';
            }
            break;
            
        case 'razon_social':
            if (!value) {
                isValid = false;
                errorMessage = 'La razón social es requerida';
            } else if (!/^[A-Z0-9\s\.\-&]+$/.test(value)) {
                isValid = false;
                errorMessage = 'Solo mayúsculas, sin acentos ni caracteres especiales';
            }
            break;
            
        case 'telefono':
            if (!value) {
                isValid = false;
                errorMessage = 'El teléfono es requerido';
            } else if (value.length < 9) {
                isValid = false;
                errorMessage = 'El teléfono debe tener al menos 9 dígitos';
            }
            break;
    }
    
    mostrarErrorCampo(fieldId, isValid, errorMessage);
    return isValid;
}

// Validar email
function validateEmail() {
    const emailField = document.getElementById('email');
    if (!emailField) return true;
    
    const value = emailField.value.trim();
    if (!value) return true; // Email es opcional
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = emailRegex.test(value);
    
    mostrarErrorCampo('email', isValid, isValid ? '' : 'Formato de email inválido');
    return isValid;
}

// Validar RUT chileno
function validarRUT(rut) {
    rut = rut.replace(/[^0-9kK]/g, '');
    
    if (rut.length < 8 || rut.length > 9) {
        return false;
    }
    
    const dv = rut.slice(-1).toUpperCase();
    const numero = rut.slice(0, -1);
    
    let suma = 0;
    let multiplicador = 2;
    
    for (let i = numero.length - 1; i >= 0; i--) {
        suma += parseInt(numero[i]) * multiplicador;
        multiplicador = multiplicador === 7 ? 2 : multiplicador + 1;
    }
    
    const resto = suma % 11;
    let dvCalculado = 11 - resto;
    
    if (dvCalculado === 11) dvCalculado = '0';
    if (dvCalculado === 10) dvCalculado = 'K';
    
    return dv === dvCalculado.toString();
}

// Mostrar error en campo específico
function mostrarErrorCampo(fieldId, isValid, errorMessage) {
    const field = document.getElementById(fieldId);
    const errorDiv = field.parentNode.querySelector('.error-message');
    
    if (isValid) {
        field.classList.remove('error');
        field.classList.add('success');
        if (errorDiv) {
            errorDiv.textContent = '';
            errorDiv.classList.remove('show');
        }
    } else {
        field.classList.remove('success');
        field.classList.add('error');
        if (errorDiv) {
            errorDiv.textContent = errorMessage;
            errorDiv.classList.add('show');
        }
    }
}

// Limpiar error de campo
function clearFieldError(fieldId) {
    const field = document.getElementById(fieldId);
    const errorDiv = field.parentNode.querySelector('.error-message');
    
    field.classList.remove('error');
    if (errorDiv) {
        errorDiv.classList.remove('show');
    }
}

// Validar formulario completo
function validarFormulario() {
    const errores = [];
    
    // Validar campos requeridos
    if (!validateField('rut_ejecutivo')) {
        errores.push('RUT del cliente inválido');
    }
    
    if (!validateField('razon_social')) {
        errores.push('Razón social inválida');
    }
    
    if (!validateField('telefono')) {
        errores.push('Teléfono inválido');
    }
    
    // Validar email si está presente
    if (!validateEmail()) {
        errores.push('Email inválido');
    }
    
    // Validar archivo si está presente
    const archivoField = document.getElementById('archivo_documentacion');
    if (archivoField && archivoField.files.length > 0) {
        const archivo = archivoField.files[0];
        const maxSize = 10 * 1024 * 1024; // 10MB
        const allowedTypes = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'zip', 'rar'];
        const extension = archivo.name.split('.').pop().toLowerCase();
        
        if (archivo.size > maxSize) {
            errores.push('El archivo no debe superar los 10MB');
        }
        
        if (!allowedTypes.includes(extension)) {
            errores.push('Tipo de archivo no permitido');
        }
    }
    
    return errores;
}

// Mostrar resumen de errores
function mostrarResumenErrores(errores) {
    const errorSummary = document.getElementById('errorSummary');
    const errorList = document.getElementById('errorList');
    
    if (errores.length > 0) {
        errorList.innerHTML = '';
        errores.forEach(error => {
            const li = document.createElement('li');
            li.textContent = error;
            errorList.appendChild(li);
        });
        errorSummary.style.display = 'block';
        
        // Scroll al resumen de errores
        errorSummary.scrollIntoView({ behavior: 'smooth', block: 'center' });
    } else {
        errorSummary.style.display = 'none';
    }
}

// Ocultar errores
function ocultarErrores() {
    const errorSummary = document.getElementById('errorSummary');
    if (errorSummary) {
        errorSummary.style.display = 'none';
    }
    
    // Limpiar errores de campos individuales
    const errorMessages = document.querySelectorAll('.error-message');
    errorMessages.forEach(error => {
        error.classList.remove('show');
    });
    
    const errorFields = document.querySelectorAll('.form-control.error');
    errorFields.forEach(field => {
        field.classList.remove('error');
    });
}

// Mostrar indicador de carga
function mostrarCarga() {
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'flex';
    }
}

// Ocultar indicador de carga
function ocultarCarga() {
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'none';
    }
}

// Manejar envío del formulario
function handleFormSubmit(e) {
    e.preventDefault();

    // Ocultar errores previos
    ocultarErrores();

    // Validar formulario
    const errores = validarFormulario();

    if (errores.length > 0) {
        mostrarResumenErrores(errores);
        return false;
    }

    // Confirmar envío
    if (!confirm('¿Está seguro de registrar este prospecto?')) {
        return false;
    }

    // Mostrar indicador de carga
    mostrarCarga();

    // Preparar datos del formulario
    const form = document.getElementById('prospectForm');
    const formData = new FormData(form);

    // Enviar formulario via AJAX
    fetch('guardar_prospecto_nuevo.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        ocultarCarga();

        if (data.success) {
            alert('Prospecto registrado exitosamente');
            cerrarModalProspecto();

            // Actualizar tabla de prospectos si existe
            if (typeof actualizarTablaProspectos === 'function') {
                // Recargar datos de prospectos
                fetch('endpoints/obtener_prospectos.php')
                    .then(response => response.json())
                    .then(tableData => {
                        if (tableData.success) {
                            actualizarTablaProspectos(tableData.data);
                        }
                    })
                    .catch(error => {
                        console.error('Error actualizando tabla:', error);
                    });
            }
        } else {
            if (data.errors && data.errors.length > 0) {
                mostrarResumenErrores(data.errors);
            } else {
                alert('Error: ' + (data.message || 'Error desconocido'));
            }
        }
    })
    .catch(error => {
        ocultarCarga();
        console.error('Error:', error);
        alert('Error de conexión. Por favor intente nuevamente.');
    });

    return false;
}

// Llenar formulario con datos de prueba
function llenarFormularioTest() {
    const testData = {
        rut_ejecutivo: '********K',
        razon_social: 'EMPRESA EJEMPLO LTDA',
        rubro: 'Retail',
        direccion_comercial: 'Av. Providencia 1234, Providencia',
        telefono: '*********',
        email: '<EMAIL>',
        num_pos: '2',
        tipo_cuenta: 'Cuenta Corriente',
        num_cuenta_bancaria: '********',
        dias_atencion: 'Lunes a Viernes',
        horario_atencion: '09:00 - 18:00',
        contrata_boleta: 'Sí',
        competencia_actual: 'Transbank',
        observaciones: 'Cliente potencial con buen volumen de transacciones'
    };

    // Llenar campos
    Object.keys(testData).forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.value = testData[fieldId];

            // Disparar evento de input para validación
            const event = new Event('input', { bubbles: true });
            field.dispatchEvent(event);
        }
    });

    console.log('Formulario llenado con datos de prueba');
}

// Función para obtener información del usuario actual
function obtenerInfoUsuario() {
    return fetch('endpoints/get_user_info.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentUserName = data.nombre_usuario;

                // Actualizar campo si el modal está abierto
                const nombreEjecutivoField = document.getElementById('nombre_ejecutivo');
                if (nombreEjecutivoField) {
                    nombreEjecutivoField.value = currentUserName;
                }
            }
            return data;
        })
        .catch(error => {
            console.error('Error obteniendo información del usuario:', error);
            return { success: false, error: error.message };
        });
}

// Función de utilidad para mostrar notificaciones
function mostrarNotificacion(mensaje, tipo = 'info') {
    // Crear elemento de notificación
    const notification = document.createElement('div');
    notification.className = `notification notification-${tipo}`;
    notification.textContent = mensaje;

    // Estilos inline para la notificación
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 6px;
        color: white;
        font-weight: 600;
        z-index: 10000;
        animation: slideInRight 0.3s ease-out;
        max-width: 300px;
        word-wrap: break-word;
    `;

    // Colores según tipo
    switch (tipo) {
        case 'success':
            notification.style.backgroundColor = '#28a745';
            break;
        case 'error':
            notification.style.backgroundColor = '#dc3545';
            break;
        case 'warning':
            notification.style.backgroundColor = '#ffc107';
            notification.style.color = '#212529';
            break;
        default:
            notification.style.backgroundColor = '#17a2b8';
    }

    // Agregar al DOM
    document.body.appendChild(notification);

    // Remover después de 5 segundos
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

// Agregar estilos de animación para notificaciones
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
