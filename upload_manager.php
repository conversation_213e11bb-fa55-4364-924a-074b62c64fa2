<?php
/**
 * Gestor de archivos para el sistema de prospectos
 * Maneja subida, validación y organización de archivos
 */

class UploadManager {
    
    private $upload_base_dir = 'uploads/prospectos/';
    private $max_file_size = 10485760; // 10MB en bytes
    private $allowed_extensions = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'zip', 'rar'];
    private $allowed_mime_types = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg',
        'image/png',
        'application/zip',
        'application/x-rar-compressed'
    ];
    
    public function __construct($custom_upload_dir = null) {
        if ($custom_upload_dir) {
            $this->upload_base_dir = $custom_upload_dir;
        }
        
        // Crear directorio base si no existe
        $this->createDirectory($this->upload_base_dir);
    }
    
    /**
     * Subir archivo de prospecto
     */
    public function uploadProspectFile($file, $rut_ejecutivo, $prospecto_id) {
        try {
            // Validar archivo
            $validation = $this->validateFile($file);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'message' => $validation['message']
                ];
            }
            
            // Crear directorio específico para el prospecto
            $prospect_dir = $this->upload_base_dir . date('Y') . '/' . date('m') . '/';
            $this->createDirectory($prospect_dir);
            
            // Generar nombre único para el archivo
            $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $filename = $this->generateUniqueFilename($rut_ejecutivo, $prospecto_id, $file_extension);
            $filepath = $prospect_dir . $filename;
            
            // Mover archivo
            if (move_uploaded_file($file['tmp_name'], $filepath)) {
                // Establecer permisos seguros
                chmod($filepath, 0644);
                
                return [
                    'success' => true,
                    'filepath' => $filepath,
                    'filename' => $filename,
                    'size' => $file['size'],
                    'type' => $file['type']
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Error al mover el archivo al directorio de destino'
                ];
            }
            
        } catch (Exception $e) {
            error_log("Error en uploadProspectFile: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error interno al procesar el archivo'
            ];
        }
    }
    
    /**
     * Validar archivo subido
     */
    private function validateFile($file) {
        // Verificar errores de subida
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return [
                'valid' => false,
                'message' => $this->getUploadErrorMessage($file['error'])
            ];
        }
        
        // Verificar tamaño
        if ($file['size'] > $this->max_file_size) {
            return [
                'valid' => false,
                'message' => 'El archivo excede el tamaño máximo permitido (10MB)'
            ];
        }
        
        // Verificar extensión
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($file_extension, $this->allowed_extensions)) {
            return [
                'valid' => false,
                'message' => 'Tipo de archivo no permitido. Extensiones permitidas: ' . implode(', ', $this->allowed_extensions)
            ];
        }
        
        // Verificar MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime_type = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!in_array($mime_type, $this->allowed_mime_types)) {
            return [
                'valid' => false,
                'message' => 'Tipo de archivo no válido según su contenido'
            ];
        }
        
        // Verificar que el archivo no esté vacío
        if ($file['size'] === 0) {
            return [
                'valid' => false,
                'message' => 'El archivo está vacío'
            ];
        }
        
        return ['valid' => true];
    }
    
    /**
     * Generar nombre único para archivo
     */
    private function generateUniqueFilename($rut_ejecutivo, $prospecto_id, $extension) {
        $timestamp = date('YmdHis');
        $random = substr(md5(uniqid(rand(), true)), 0, 8);
        return "prospecto_{$rut_ejecutivo}_{$prospecto_id}_{$timestamp}_{$random}.{$extension}";
    }
    
    /**
     * Crear directorio si no existe
     */
    private function createDirectory($path) {
        if (!is_dir($path)) {
            if (!mkdir($path, 0755, true)) {
                throw new Exception("No se pudo crear el directorio: $path");
            }
        }
    }
    
    /**
     * Obtener mensaje de error de subida
     */
    private function getUploadErrorMessage($error_code) {
        switch ($error_code) {
            case UPLOAD_ERR_INI_SIZE:
                return 'El archivo excede el tamaño máximo permitido por el servidor';
            case UPLOAD_ERR_FORM_SIZE:
                return 'El archivo excede el tamaño máximo permitido por el formulario';
            case UPLOAD_ERR_PARTIAL:
                return 'El archivo se subió parcialmente';
            case UPLOAD_ERR_NO_FILE:
                return 'No se subió ningún archivo';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Falta el directorio temporal';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Error al escribir el archivo en disco';
            case UPLOAD_ERR_EXTENSION:
                return 'Subida de archivo detenida por extensión';
            default:
                return 'Error desconocido al subir el archivo';
        }
    }
    
    /**
     * Eliminar archivo
     */
    public function deleteFile($filepath) {
        if (file_exists($filepath)) {
            return unlink($filepath);
        }
        return false;
    }
    
    /**
     * Obtener información de archivo
     */
    public function getFileInfo($filepath) {
        if (!file_exists($filepath)) {
            return null;
        }
        
        return [
            'exists' => true,
            'size' => filesize($filepath),
            'modified' => filemtime($filepath),
            'readable' => is_readable($filepath),
            'extension' => pathinfo($filepath, PATHINFO_EXTENSION),
            'basename' => basename($filepath)
        ];
    }
    
    /**
     * Limpiar archivos antiguos (para mantenimiento)
     */
    public function cleanupOldFiles($days_old = 365) {
        $cutoff_time = time() - ($days_old * 24 * 60 * 60);
        $deleted_count = 0;
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($this->upload_base_dir)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getMTime() < $cutoff_time) {
                if (unlink($file->getPathname())) {
                    $deleted_count++;
                }
            }
        }
        
        return $deleted_count;
    }
    
    /**
     * Obtener estadísticas de uso de archivos
     */
    public function getStorageStats() {
        $total_size = 0;
        $file_count = 0;
        
        if (is_dir($this->upload_base_dir)) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($this->upload_base_dir)
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $total_size += $file->getSize();
                    $file_count++;
                }
            }
        }
        
        return [
            'total_files' => $file_count,
            'total_size_bytes' => $total_size,
            'total_size_mb' => round($total_size / 1048576, 2),
            'upload_dir' => $this->upload_base_dir
        ];
    }
}

/**
 * Función helper para usar el gestor de archivos
 */
function uploadProspectDocument($file, $rut_ejecutivo, $prospecto_id) {
    $upload_manager = new UploadManager();
    return $upload_manager->uploadProspectFile($file, $rut_ejecutivo, $prospecto_id);
}

/**
 * Función para crear directorio de uploads si no existe
 */
function ensureUploadDirectoryExists() {
    $upload_dir = 'uploads/prospectos/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
        
        // Crear archivo .htaccess para seguridad
        $htaccess_content = "Options -Indexes\n";
        $htaccess_content .= "Options -ExecCGI\n";
        $htaccess_content .= "AddHandler cgi-script .php .pl .py .jsp .asp .sh .cgi\n";
        
        file_put_contents($upload_dir . '.htaccess', $htaccess_content);
    }
}

// Asegurar que el directorio existe al incluir este archivo
ensureUploadDirectoryExists();
?>
