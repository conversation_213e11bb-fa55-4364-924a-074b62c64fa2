<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Crear archivo de log específico para esta exportación
$log_file = __DIR__ . '/../logs/export_' . date('Y-m-d_H-i-s') . '.log';
if (!file_exists(__DIR__ . '/../logs/')) {
    mkdir(__DIR__ . '/../logs/', 0777, true);
}

// Función para registrar logs
function export_log($message, $type = 'INFO') {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[$timestamp] [$type] $message" . PHP_EOL;
    file_put_contents($log_file, $log_message, FILE_APPEND);
    
    if ($type == 'ERROR') {
        error_log($message);
    }
}

export_log("Iniciando proceso de exportación");

// Iniciar sesión
session_start();
export_log("Sesión iniciada: " . (isset($_SESSION['usuario_id']) ? "Usuario ID: " . $_SESSION['usuario_id'] : "No hay sesión de usuario"));

// Verificar autenticación
if (!isset($_SESSION['usuario_id'])) {
    export_log("Usuario no autenticado", "ERROR");
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Usuario no autenticado']);
    exit;
}

if (!isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    export_log("Usuario sin permisos para InteletGroup", "ERROR");
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Sin permisos para acceder a InteletGroup']);
    exit;
}

// Incluir conexión a base de datos
require_once __DIR__ . '/../con_db.php';

// Verificar conexión
if (!isset($mysqli) || $mysqli->connect_error) {
    $error_msg = "Error de conexión a la base de datos: " . ($mysqli->connect_error ?? "Variable de conexión no disponible");
    export_log($error_msg, "ERROR");
    header('Content-Type: application/json');
    echo json_encode(['error' => $error_msg]);
    exit;
}

export_log("Conexión a base de datos establecida");

// Obtener parámetros de filtros
$filtro_ejecutivo = $_GET['ejecutivo'] ?? 'todos';
$filtro_periodo = $_GET['periodo'] ?? 'año';
$filtro_fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-01-01');
$filtro_fecha_fin = $_GET['fecha_fin'] ?? date('Y-12-31');

export_log("Filtros recibidos: ejecutivo=$filtro_ejecutivo, periodo=$filtro_periodo, fecha_inicio=$filtro_fecha_inicio, fecha_fin=$filtro_fecha_fin");

// Calcular fechas según el periodo seleccionado
switch($filtro_periodo) {
    case 'hoy':
        $filtro_fecha_inicio = date('Y-m-d');
        $filtro_fecha_fin = date('Y-m-d');
        break;
    case 'semana':
        $filtro_fecha_inicio = date('Y-m-d', strtotime('monday this week'));
        $filtro_fecha_fin = date('Y-m-d', strtotime('sunday this week'));
        break;
    case 'mes':
        $filtro_fecha_inicio = date('Y-m-01');
        $filtro_fecha_fin = date('Y-m-t');
        break;
    case 'trimestre':
        $trimestre = ceil(date('n') / 3);
        $filtro_fecha_inicio = date('Y-') . sprintf('%02d', ($trimestre - 1) * 3 + 1) . '-01';
        $filtro_fecha_fin = date('Y-m-t', strtotime($filtro_fecha_inicio . ' +2 months'));
        break;
    case 'año':
        $filtro_fecha_inicio = date('Y-01-01');
        $filtro_fecha_fin = date('Y-12-31');
        break;
}

export_log("Fechas calculadas: inicio=$filtro_fecha_inicio, fin=$filtro_fecha_fin");

// Construir condición WHERE para filtros
$where_conditions = ["1=1"];
$params = [];
$types = "";

if ($filtro_ejecutivo !== 'todos') {
    $where_conditions[] = "p.usuario_id = ?";
    $params[] = $filtro_ejecutivo;
    $types .= "i";
}

$where_conditions[] = "DATE(p.fecha_registro) BETWEEN ? AND ?";
$params[] = $filtro_fecha_inicio;
$params[] = $filtro_fecha_fin;
$types .= "ss";

$where_clause = implode(" AND ", $where_conditions);

export_log("Cláusula WHERE construida: $where_clause");

// Consulta para obtener prospectos con campos correctos según la estructura de la tabla
$query = "
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.direccion_comercial, p.fecha_registro,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE " . $where_clause . "
    ORDER BY p.fecha_registro DESC";

export_log("Consulta SQL preparada: " . str_replace("\n", " ", $query));

// Preparar y ejecutar la consulta
$stmt = $mysqli->prepare($query);
if (!$stmt) {
    $error_msg = "Error preparando consulta: " . $mysqli->error;
    export_log($error_msg, "ERROR");
    header('Content-Type: application/json');
    echo json_encode(['error' => $error_msg, 'log_file' => basename($log_file)]);
    exit;
}

export_log("Consulta preparada correctamente");

if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
    export_log("Parámetros vinculados a la consulta");
}

if (!$stmt->execute()) {
    $error_msg = "Error ejecutando consulta: " . $stmt->error;
    export_log($error_msg, "ERROR");
    header('Content-Type: application/json');
    echo json_encode(['error' => $error_msg, 'log_file' => basename($log_file)]);
    exit;
}

export_log("Consulta ejecutada correctamente");

// Obtener resultados usando bind_result (compatible con PHP 7.3.33)
$id = $tipo_persona = $rut = $razon_social = $rubro = $email = $telefono = $direccion = $fecha = $ejecutivo = null;

$stmt->bind_result(
    $id, $tipo_persona, $rut, $razon_social, $rubro, 
    $email, $telefono, $direccion, $fecha, $ejecutivo
);

export_log("Variables vinculadas para resultados");

// Preparar datos para exportación
$prospectos = [];
$contador = 0;

while ($stmt->fetch()) {
    $contador++;
    $prospectos[] = [
        'ID' => $id,
        'Tipo de Persona' => $tipo_persona,
        'RUT' => $rut,
        'Razón Social' => $razon_social,
        'Rubro' => $rubro,
        'Email' => $email,
        'Teléfono' => $telefono,
        'Dirección' => $direccion,
        'Fecha Registro' => $fecha,
        'Ejecutivo' => $ejecutivo
    ];
}

export_log("Se encontraron $contador prospectos");

$stmt->close();
$mysqli->close();

// Verificar si hay datos para exportar
if (empty($prospectos)) {
    $error_msg = "No hay datos para exportar con los filtros seleccionados";
    export_log($error_msg, "WARNING");
    header('Content-Type: application/json');
    echo json_encode(['error' => $error_msg, 'log_file' => basename($log_file)]);
    exit;
}

// Configurar encabezados para descarga de XLSX
$fecha_actual = date('Y-m-d');
$nombre_archivo = "prospectos_inteletgroup_{$fecha_actual}.xlsx";

export_log("Generando archivo XLSX: $nombre_archivo");

// Crear archivo Excel simple sin PhpSpreadsheet
try {
    // Crear archivo CSV temporal
    $temp_file = tempnam(sys_get_temp_dir(), 'export_');
    $csv_file = $temp_file . '.csv';
    rename($temp_file, $csv_file);
    
    $fp = fopen($csv_file, 'w');
    
    // Escribir BOM para UTF-8
    fprintf($fp, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Escribir encabezados
    fputcsv($fp, array_keys($prospectos[0]));
    
    // Escribir datos
    foreach ($prospectos as $prospecto) {
        fputcsv($fp, $prospecto);
    }
    
    fclose($fp);
    
    export_log("Archivo CSV temporal creado: $csv_file");
    
    // Enviar archivo al navegador
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $nombre_archivo . '"');
    header('Cache-Control: max-age=0');
    
    readfile($csv_file);
    
    // Eliminar archivo temporal
    unlink($csv_file);
    
    export_log("Archivo enviado correctamente y archivo temporal eliminado");
    exit;
    
} catch (Exception $e) {
    $error_msg = "Error generando el archivo: " . $e->getMessage();
    export_log($error_msg, "ERROR");
    header('Content-Type: application/json');
    echo json_encode(['error' => $error_msg, 'log_file' => basename($log_file)]);
    exit;
}
?>
