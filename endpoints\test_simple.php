<?php
/**
 * Test muy simple para verificar que PHP funciona
 */

// Establecer tipo de contenido JSON
header('Content-Type: application/json');

// Crear un log simple
file_put_contents(__DIR__ . '/test_simple.log', date('Y-m-d H:i:s') . " - <PERSON>ript ejecutado\n", FILE_APPEND);

// Responder con JSON simple
echo json_encode([
    'success' => true,
    'message' => 'El endpoint PHP está funcionando',
    'timestamp' => date('Y-m-d H:i:s'),
    'php_version' => phpversion(),
    'method' => $_SERVER['REQUEST_METHOD']
]);
?>