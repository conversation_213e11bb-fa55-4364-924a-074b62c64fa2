<?php
// Conexión a la base de datos


$inc = include("con_db.php");
session_start();

// Obtener los datos del formulario
$orden = $_POST['orden'];
$reque = $_POST['reque'];
$motivo = $_POST['motivo'];
$segmento = $_POST['segmento'];
$super = $_POST['super'];
$nota =  $_POST['star'];
$usuario = $_POST['usuario'];



// Realizar la consulta SQL para insertar los datos en la tabla
$sql = "INSERT INTO TB_SOPORTE_CIERRE_ASEGURADO 
values ('$orden'  , '' ,  '$usuario'  , now()  , 'Cierre Asegurado'  ,  '$reque'  ,  '$motivo','$nota', '$segmento' ,'','')
";


// Ejecutar la consulta
if (mysqli_query($conex, $sql)) {
echo "<script>alert('Datos guardados correctamente');</script>";
?>

<script type="text/javascript">
    window.location.replace("Form_SoporteCierreAsegurado.php?usuario=<?php echo $usuario; ?>");
</script>


<?php    

} else 
{
echo "Error al guardar los datos: " . mysqli_error($conex);
}
 





// Cerrar la conexión
mysqli_close($conex);
?>