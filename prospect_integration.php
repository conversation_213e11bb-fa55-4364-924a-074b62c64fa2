<?php
/**
 * Script de integración para el sistema de registro de prospectos
 * Incluye todas las funcionalidades necesarias para el formulario modal
 */

// Verificar autenticación
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header('Location: login.php');
    exit();
}

// Incluir archivos necesarios
require_once("con_db.php");

// Obtener información del usuario logueado
$usuario_id = $_SESSION['usuario_id'];
$nombre_usuario = '';
$es_admin = false;

try {
    $stmt = $mysqli->prepare("SELECT nombre_usuario, rol FROM tb_experian_usuarios WHERE id = ?");
    $stmt->bind_param("i", $usuario_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        $nombre_usuario = $row['nombre_usuario'] ?: 'Usuario';
        $es_admin = $row['rol'] === 'admin';
    }
    $stmt->close();
} catch (Exception $e) {
    error_log("Error obteniendo información del usuario: " . $e->getMessage());
    $nombre_usuario = 'Usuario';
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Gestión de Prospectos</title>
    
    <!-- CSS Framework (Bootstrap o similar) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- CSS personalizado -->
    <link href="css/prospect-form.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 20px auto;
            max-width: 1200px;
            overflow: hidden;
        }
        
        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content-section {
            padding: 30px;
        }
        
        .stats-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        
        .btn-prospect {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-prospect:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
            color: white;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header-section">
            <h1><i class="fas fa-users"></i> Sistema de Gestión de Prospectos</h1>
            <p>Bienvenido, <strong><?php echo htmlspecialchars($nombre_usuario); ?></strong></p>
            <?php if ($es_admin): ?>
                <span class="badge bg-warning">Administrador</span>
            <?php endif; ?>
        </div>
        
        <div class="content-section">
            <!-- Estadísticas rápidas -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="stats-card">
                        <h5><i class="fas fa-chart-line"></i> Prospectos Totales</h5>
                        <h3 id="totalProspectos">-</h3>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <h5><i class="fas fa-clock"></i> Pendientes</h5>
                        <h3 id="prospectosPendientes">-</h3>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <h5><i class="fas fa-check-circle"></i> Completados</h5>
                        <h3 id="prospectosCompletados">-</h3>
                    </div>
                </div>
            </div>
            
            <!-- Botones de acción -->
            <div class="row mb-4">
                <div class="col-md-12 text-center">
                    <button type="button" class="btn btn-prospect me-3" onclick="abrirModalProspecto()">
                        <i class="fas fa-plus"></i> Nuevo Prospecto
                    </button>
                    
                    <button type="button" class="btn btn-outline-primary me-3" onclick="cargarProspectos()">
                        <i class="fas fa-refresh"></i> Actualizar Lista
                    </button>
                    
                    <?php if ($es_admin): ?>
                    <button type="button" class="btn btn-outline-success" onclick="exportarProspectos()">
                        <i class="fas fa-download"></i> Exportar Datos
                    </button>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Tabla de prospectos -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-table"></i> Lista de Prospectos</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="prospectosTable">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>RUT</th>
                                            <th>Razón Social</th>
                                            <th>Ejecutivo</th>
                                            <th>Teléfono</th>
                                            <th>Estado</th>
                                            <th>Fecha</th>
                                            <th>Acciones</th>
                                        </tr>
                                    </thead>
                                    <tbody id="prospectosTableBody">
                                        <tr>
                                            <td colspan="7" class="text-center">
                                                <div class="spinner-border" role="status">
                                                    <span class="visually-hidden">Cargando...</span>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Incluir el modal del formulario -->
    <?php include 'prospect_form_modal.html'; ?>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Variables globales para JavaScript -->
    <script>
        window.currentUserName = '<?php echo addslashes($nombre_usuario); ?>';
        window.currentUserId = <?php echo $usuario_id; ?>;
        window.isAdmin = <?php echo $es_admin ? 'true' : 'false'; ?>;
    </script>
    
    <!-- Scripts personalizados -->
    <script src="js/prospect-form.js"></script>
    
    <script>
        // Funciones de integración
        function cargarProspectos() {
            fetch('endpoints/obtener_prospectos.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        actualizarTablaProspectos(data.data);
                        actualizarEstadisticas(data.data);
                    } else {
                        console.error('Error cargando prospectos:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }
        
        function actualizarTablaProspectos(prospectos) {
            const tbody = document.getElementById('prospectosTableBody');
            
            if (prospectos.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center">No hay prospectos registrados</td></tr>';
                return;
            }
            
            tbody.innerHTML = prospectos.map(prospecto => `
                <tr>
                    <td>${prospecto.rut_ejecutivo || '-'}</td>
                    <td>${prospecto.razon_social || '-'}</td>
                    <td>${prospecto.nombre_ejecutivo || '-'}</td>
                    <td>${prospecto.telefono || '-'}</td>
                    <td>
                        <span class="badge bg-${getEstadoBadgeClass(prospecto.ultimo_estado || prospecto.estado)}">
                            ${prospecto.ultimo_estado || prospecto.estado || 'Sin estado'}
                        </span>
                    </td>
                    <td>${formatearFecha(prospecto.fecha_registro)}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="verDetalle(${prospecto.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-success" onclick="agregarBitacora('${prospecto.rut_ejecutivo}')">
                            <i class="fas fa-plus"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }
        
        function actualizarEstadisticas(prospectos) {
            const total = prospectos.length;
            const pendientes = prospectos.filter(p => 
                !p.ultimo_estado || p.ultimo_estado === 'Envio información' || p.ultimo_estado === 'Negociación'
            ).length;
            const completados = prospectos.filter(p => 
                p.ultimo_estado === 'Habilitado' || p.ultimo_estado === 'Firmado'
            ).length;
            
            document.getElementById('totalProspectos').textContent = total;
            document.getElementById('prospectosPendientes').textContent = pendientes;
            document.getElementById('prospectosCompletados').textContent = completados;
        }
        
        function getEstadoBadgeClass(estado) {
            switch (estado) {
                case 'Envio información': return 'primary';
                case 'Negociación': return 'warning';
                case 'Cerrado': return 'info';
                case 'B.O. Experian': return 'secondary';
                case 'Proceso de Firma': return 'dark';
                case 'Firmado': return 'success';
                case 'Habilitado': return 'success';
                default: return 'light';
            }
        }
        
        function formatearFecha(fecha) {
            if (!fecha) return '-';
            const date = new Date(fecha);
            return date.toLocaleDateString('es-CL');
        }
        
        function verDetalle(prospectoId) {
            // Implementar vista de detalle
            alert('Función de detalle en desarrollo');
        }
        
        function agregarBitacora(rut) {
            // Implementar modal de bitácora
            alert('Función de bitácora en desarrollo para RUT: ' + rut);
        }
        
        function exportarProspectos() {
            window.open('endpoints/exportar_prospectos.php', '_blank');
        }
        
        // Cargar datos al inicializar
        document.addEventListener('DOMContentLoaded', function() {
            cargarProspectos();
        });
    </script>
</body>
</html>
