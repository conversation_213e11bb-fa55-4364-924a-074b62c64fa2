<?php 


header('Content-Type: text/html; charset=UTF-8');
//Iniciar una nueva sesión o reanudar la existente.
session_start();

$inc = include("con_db.php");


$usuario = $_GET['usuario'];

$data1 = '';
$data2 = '';
$data3 = '';
$data4 = '';

$totalPts = '';
$totalPtsMensual = '';

$semanal = $conex->query(
    "select  
    Fecha, 
    Ptos, 
    SUM(Ptos) OVER () AS TotalPts 
FROM 
    (
        SELECT 
            DATE_FORMAT(`Fecha fin#`, '%m%d') Fecha, 
            SUM(Ptos_referencial) Ptos 
        FROM 
            tb_paso_pyNdc tppn 
        WHERE 
            RutTecnicoOrig = '".$usuario."' 
            AND `Fecha fin#` >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY 
            DATE_FORMAT(`Fecha fin#`, '%m%d')
    ) subconsulta
    ORDER BY 1 ASC 
  ");

  while ($row = mysqli_fetch_array($semanal)) {

      $data3 = $data3 . '"'. round($row['Ptos'],1).'",';
      $data4 = $data4 . '"'. $row['Fecha'].'",';
      $totalPts = round($row['TotalPts'],1);
    }
  $data3 = trim($data3,",");
  $data4 = trim($data4,",");



$mensual = $conex->query(
    "select  
    Fecha, 
    Ptos, 
    SUM(Ptos) OVER () AS TotalPts 
FROM 
    (
        SELECT 
            DATE_FORMAT(`Fecha fin#`, '%m%d') Fecha, 
            SUM(Ptos_referencial) Ptos 
        FROM 
            tb_paso_pyNdc tppn 
        WHERE 
            RutTecnicoOrig = '".$usuario."'         
        GROUP BY 
            DATE_FORMAT(`Fecha fin#`, '%m%d')
    ) subconsulta
    ORDER BY 1 ASC 
  ");

  while ($row = mysqli_fetch_array($mensual)) {

      $data1 = $data1 . '"'. round($row['Ptos'],1).'",';
      $data2 = $data2 . '"'. $row['Fecha'].'",';
      $totalPtsMensual = round($row['TotalPts'],2);
    }
  $data1 = trim($data1,",");
  $data2 = trim($data2,",");

?> 



<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Affan - PWA Mobile HTML Template">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>APP TQW</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/logo_con.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card -->
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">
      <!-- Header Content -->
      <div class="header-content position-relative d-flex align-items-center justify-content-between">
        <!-- Back Button -->
        <!-- <div class="back-button">
          <a href="home.php?usuario=">
            <i class="bi bi-arrow-left-short"></i>
          </a>
        </div> -->

        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0"><?php echo $usuario; ?></h6>
        </div>

        <!-- Settings -->
        <!-- <div class="setting-wrapper">
          <div class="setting-trigger-btn" id="settingTriggerBtn">
            <i class="bi bi-gear"></i>
            <span></span>
          </div>
        </div> -->
      </div>
    </div>
  </div>

    


  <div class="page-content-wrapper py-3">
    <div class="container">
      <!-- Element Heading -->
      <!-- <div class="element-heading">
        <h6>Column One</h6>
      </div> -->
    </div>

    

    <div class="container">
      <!-- Element Heading -->
      <div class="element-heading mt-3">
        <h6>Producción Puntos</h6>
      </div>
    </div>


    <div class="container">
      <div class="card">
        <div class="card-body">
          <ul class="nav nav-tabs" id="bootstrapTab" role="tablist">
            <li class="nav-item me-2" role="presentation">
              <button class="nav-link active" id="home-tab" data-bs-toggle="tab" data-bs-target="#home" type="button"
                role="tab" aria-controls="home" aria-selected="true">Los ultimos 7 días</button>
            </li>
            <li class="nav-item me-2" role="presentation">
              <button class="nav-link" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button"
                role="tab" aria-controls="profile" aria-selected="false">Este mes</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button"
                role="tab" aria-controls="contact" aria-selected="false">Los ultimos 12 meses</button>
            </li>
          </ul>

          <div class="tab-content p-3 border-top-0" id="bootstrapTabContent">
            <div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">
            <!-- <div class="container">
                            <div class="card shadow-sm"> -->
                                <!-- <div class="card-body pb-0"> -->
                                    <div class="charts-wrapper">
                                        <div id="areaChart4"></div>
                                    </div>
                                <!-- </div> -->
                            <!-- </div>
                        </div> -->
            </div>
            <div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">
              <<div class="charts-wrapper">
                                        <div id="areaChart3"></div>
                                    </div>
            </div>
            <div class="tab-pane fade" id="contact" role="tabpanel" aria-labelledby="contact-tab">
              <h6>EN DESARROLLO!</h6>
              <p class="mb-0">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Nam, impedit natus itaque fuga
                aperiam qui eos ut.</p>
            </div>
          </div>
        </div>
      </div>
    </div>


    




  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          <li class="active">
            <a href="home.php?usuario=<?php echo $usuario; ?>">
              <i class="bi bi-house"></i>
              <span>Home</span>
            </a>
          </li>

          <li class="active">
          <a href="TecnicoProduccion.php?usuario=<?php echo $usuario; ?>">
            
              <i class="bi bi-bar-chart-fill"></i>
              <span>Graficos</span>
            </a>
          </li>

          
        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/apexcharts.min.js"></script>
  <script src="js/chart-active.js"></script>
  <script src="js/pwa.js"></script>

<script>

    
var areaChart3 = {
    chart: {
        height: 240,
        type: 'area',
        animations: {
            enabled: true,
            easing: 'easeinout',
            speed: 1000
        },
        dropShadow: {
            enabled: true,
            opacity: 0.1,
            blur: 1,
            left: -5,
            top: 18
        },
        zoom: {
            enabled: false
        },
        toolbar: {
            show: false
        },
    },
    
    colors: ['#0134d4', '#ea4c62'],
    dataLabels: {
  enabled: true,
  style: {
    colors: ['#0134d4', '#ea4c62'],
    fontSize: '12px',
    fontWeight: 'bold',
    textShadow: 'none',
  }
},
    fill: {
        type: "gradient",
        gradient: {
            type: "vertical",
            shadeIntensity: 1,
            inverseColors: true,
            opacityFrom: 0.15,
            opacityTo: 0.02,
            stops: [40, 100],
        }
    },
    grid: {
        borderColor: '#dbeaea',
        strokeDashArray: 4,
        xaxis: {
            lines: {
                show: true
            }
        },
        yaxis: {
            lines: {
                show: false,
            }
        },
        padding: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 0
        },
    },
    legend: {
        position: 'bottom',
        horizontalAlign: 'center',
        offsetY: 4,
        fontSize: '14px',
        markers: {
            width: 9,
            height: 9,
            strokeWidth: 0,
            radius: 20
        },
        itemMargin: {
            horizontal: 5,
            vertical: 0
        }
    },
    title: {
        text: '<?php echo $totalPtsMensual; ?> Ptos',
        align: 'left',
        margin: 0,
        offsetX: 0,
        offsetY: 20,
        floating: false,
        style: {
            fontSize: '16px',
            color: '#8480ae'
        }
    },
    tooltip: {
        theme: 'dark',
        marker: {
            show: true,
        },
        x: {
            show: false,
        }
    },
    subtitle: {
        text: 'Este mes',
        align: 'left',
        margin: 0,
        offsetX: 0,
        offsetY: 0,
        floating: false,
        style: {
            fontSize: '14px',
            color: '#8480ae'
        }
    },
    stroke: {
        show: true,
        curve: 'smooth',
        width: 3
    },
    labels: [<?php echo $data2; ?>],
    series: [{
        name: 'Produccion',
        data: [<?php echo $data1; ?>]
    }
    
],
    xaxis: {
        crosshairs: {
            show: true
        },
        labels: {
            offsetX: 0,
            offsetY: 0,
            style: {
                colors: '#8480ae',
                fontSize: '12px',
            },
        },
        tooltip: {
            enabled: false,
        },
    },
    yaxis: {
        tickAmount: 5,
        labels: {
            offsetX: -10,
            offsetY: 0,
            style: {
                colors: '#8480ae',
                fontSize: '12px',
            },
        }
    },
}

const areaCharts3 = document.getElementById('areaChart3');

if (areaCharts3) {
    var areaChart_03 = new ApexCharts(areaCharts3, areaChart3);
    areaChart_03.render();
}


    </script>


<script>

    
var areaChart4 = {
    chart: {
        height: 240,
        type: 'area',
        animations: {
            enabled: true,
            easing: 'easeinout',
            speed: 1000
        },
        dropShadow: {
            enabled: true,
            opacity: 0.1,
            blur: 1,
            left: -5,
            top: 18
        },
        zoom: {
            enabled: false
        },
        toolbar: {
            show: false
        },
    },
    
    colors: ['#0134d4', '#ea4c62'],
    dataLabels: {
  enabled: true,
  style: {
    colors: ['#0134d4', '#ea4c62'],
    fontSize: '12px',
    fontWeight: 'bold',
    textShadow: 'none',
  }
},
    fill: {
        type: "gradient",
        gradient: {
            type: "vertical",
            shadeIntensity: 1,
            inverseColors: true,
            opacityFrom: 0.15,
            opacityTo: 0.02,
            stops: [40, 100],
        }
    },
    grid: {
        borderColor: '#dbeaea',
        strokeDashArray: 4,
        xaxis: {
            lines: {
                show: true
            }
        },
        yaxis: {
            lines: {
                show: false,
            }
        },
        padding: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 0
        },
    },
    legend: {
        position: 'bottom',
        horizontalAlign: 'center',
        offsetY: 4,
        fontSize: '14px',
        markers: {
            width: 9,
            height: 9,
            strokeWidth: 0,
            radius: 20
        },
        itemMargin: {
            horizontal: 5,
            vertical: 0
        }
    },
    title: {
        text: '<?php echo $totalPts; ?> Ptos',
            align: 'left',
            margin: 0,
            offsetX: 0,
            offsetY: 20,
            floating: false,
            style: {
                fontSize: '16px',
                color: '#8480ae'
            }
        },
        tooltip: {
            theme: 'dark',
            marker: {
                show: true,
            },
            x: {
                show: false,
            }
        },
        subtitle: {
            text: 'Esta semana',
            align: 'left',
            margin: 0,
            offsetX: 0,
            offsetY: 0,
            floating: false,
            style: {
                fontSize: '14px',
                color: '#8480ae'
            }
        },
        stroke: {
            show: true,
            curve: 'smooth',
            width: 3
        },
        labels: [<?php echo $data4; ?>],
        series: [{
            name: 'Produccion',
            data: [<?php echo $data3; ?>]
        }

        ],
        xaxis: {
            crosshairs: {
                show: true
            },
            labels: {
                offsetX: 0,
                offsetY: 0,
                style: {
                    colors: '#8480ae',
                    fontSize: '12px',
                },
            },
            tooltip: {
                enabled: false,
            },
        },
        yaxis: {
            tickAmount: 5,
            labels: {
                offsetX: -10,
                offsetY: 0,
                style: {
                    colors: '#8480ae',
                    fontSize: '12px',
                },
            }
        },
    }

    const areaCharts4 = document.getElementById('areaChart4');

    if (areaCharts4) {
        var areaChart_04 = new ApexCharts(areaCharts4, areaChart4);
        areaChart_04.render();
    }


</script>
</body>

</html>