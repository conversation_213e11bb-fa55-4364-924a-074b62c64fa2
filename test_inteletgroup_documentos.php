<?php
// Test script para diagnosticar el error 500
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log inicial
error_log("TEST_INTELETGROUP: Script iniciado");

echo "<!DOCTYPE html>";
echo "<html><head><title>Test InteletGroup Documentos</title></head><body>";
echo "<h1>Test de Diagnóstico - InteletGroup Documentos</h1>";

try {
    // Test 1: Session
    session_start();
    echo "✓ Session iniciada<br>";
    
    // Test 2: Autenticación
    if (!isset($_SESSION['usuario_id'])) {
        echo "✗ No hay sesión de usuario<br>";
        echo "<a href='login.php'>Ir a login</a><br>";
        exit;
    }
    echo "✓ Usuario autenticado: " . $_SESSION['usuario_id'] . "<br>";
    
    // Test 3: Cache utils
    if (file_exists('cache_utils.php')) {
        require_once 'cache_utils.php';
        echo "✓ cache_utils.php cargado<br>";
        
        // Test función no_cache_meta
        if (function_exists('no_cache_meta')) {
            echo "✓ Función no_cache_meta() disponible<br>";
        } else {
            echo "✗ Función no_cache_meta() NO disponible<br>";
        }
    } else {
        echo "✗ cache_utils.php no encontrado<br>";
    }
    
    // Test 4: Conexión DB
    if (file_exists('con_db.php')) {
        require_once 'con_db.php';
        echo "✓ con_db.php cargado<br>";
    } else {
        echo "✗ con_db.php no encontrado<br>";
        exit;
    }
    
    // Test 5: Variable mysqli
    if (isset($mysqli)) {
        echo "✓ Variable mysqli definida<br>";
        if ($mysqli->connect_error) {
            echo "✗ Error de conexión: " . $mysqli->connect_error . "<br>";
        } else {
            echo "✓ Conexión a base de datos OK<br>";
        }
    } else {
        echo "✗ Variable mysqli no definida<br>";
    }
    
    // Test 6: Consulta simple
    if (isset($mysqli) && !$mysqli->connect_error) {
        $result = $mysqli->query("SELECT 1");
        if ($result) {
            echo "✓ Consulta de prueba exitosa<br>";
        } else {
            echo "✗ Error en consulta de prueba: " . $mysqli->error . "<br>";
        }
    }
    
    // Test 7: Verificar tablas necesarias
    if (isset($mysqli)) {
        $tables = [
            'tb_inteletgroup_prospectos',
            'tb_inteletgroup_documentos',
            'tb_inteletgroup_tipos_documento',
            'tb_inteletgroup_documento_checklist'
        ];
        
        echo "<br><strong>Verificación de tablas:</strong><br>";
        foreach ($tables as $table) {
            $result = $mysqli->query("SHOW TABLES LIKE '$table'");
            if ($result && $result->num_rows > 0) {
                echo "✓ Tabla $table existe<br>";
            } else {
                echo "✗ Tabla $table NO existe<br>";
            }
        }
    }
    
    // Test 8: Parámetro prospecto_id
    if (isset($_GET['prospecto_id'])) {
        $prospecto_id = intval($_GET['prospecto_id']);
        echo "<br><strong>Buscando prospecto ID: $prospecto_id</strong><br>";
        
        // Test 9: Buscar prospecto
        if (isset($mysqli)) {
            $stmt = $mysqli->prepare("SELECT id, razon_social, tipo_persona FROM tb_inteletgroup_prospectos WHERE id = ?");
            if ($stmt) {
                $stmt->bind_param("i", $prospecto_id);
                $stmt->execute();
                $stmt->bind_result($id, $razon_social, $tipo_persona);
                if ($stmt->fetch()) {
                    echo "✓ Prospecto encontrado: $razon_social (ID: $id, Tipo: $tipo_persona)<br>";
                } else {
                    echo "✗ Prospecto no encontrado<br>";
                }
                $stmt->close();
            } else {
                echo "✗ Error preparando consulta: " . $mysqli->error . "<br>";
            }
        }
    } else {
        echo "<br>✓ No se especificó prospecto_id<br>";
    }
    
    echo "<br><strong>Todos los tests completados exitosamente</strong><br>";
    echo "<br><a href='inteletgroup_documentos_enhanced.php'>Ir a página enhanced</a> | ";
    echo "<a href='inteletgroup_documentos_enhanced.php?prospecto_id=42'>Ir a página enhanced con prospecto 42</a><br>";
    
} catch (Exception $e) {
    echo "✗ Excepción capturada: " . $e->getMessage() . "<br>";
    echo "Archivo: " . $e->getFile() . "<br>";
    echo "Línea: " . $e->getLine() . "<br>";
    echo "Trace:<pre>" . $e->getTraceAsString() . "</pre>";
    error_log("TEST_INTELETGROUP Exception: " . $e->getMessage());
}

echo "</body></html>";
?>