<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dashboard Simple</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .chart-container {
            height: 300px;
            border: 2px solid #007bff;
            margin: 20px 0;
            padding: 10px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Test Dashboard Simple</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Gráfico de Prueba</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="testChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>Información de Debug:</h3>
            <div id="debugInfo" class="alert alert-info">
                Cargando...
            </div>
        </div>
    </div>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        console.log('=== TEST DASHBOARD SIMPLE INICIADO ===');
        
        function updateDebugInfo(message) {
            const debugDiv = document.getElementById('debugInfo');
            if (debugDiv) {
                debugDiv.innerHTML += '<br>' + message;
            }
            console.log(message);
        }
        
        // Simular datos como los del dashboard real
        const testData = {
            evolucion_mensual: [
                {dia: '2025-07-07', prospectos: 2, documentos: 0},
                {dia: '2025-07-08', prospectos: 1, documentos: 0}
            ],
            prospectos_por_tipo: {
                'Natural': 3,
                'Juridica': 0
            },
            prospectos_por_ejecutivo: [
                {ejecutivo: 'CATHERINE DEL CARMEN PINCHEIRA BRITO', prospectos: 2, documentos: 0},
                {ejecutivo: 'Sin asignar', prospectos: 1, documentos: 0}
            ]
        };
        
        updateDebugInfo('Datos de prueba preparados');
        
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo('DOM cargado');
            
            if (typeof Chart === 'undefined') {
                updateDebugInfo('❌ Chart.js NO está disponible');
                return;
            }
            
            updateDebugInfo('✅ Chart.js disponible - Versión: ' + Chart.version);
            
            const canvas = document.getElementById('testChart');
            if (!canvas) {
                updateDebugInfo('❌ Canvas no encontrado');
                return;
            }
            
            updateDebugInfo('✅ Canvas encontrado');
            
            try {
                const ctx = canvas.getContext('2d');
                updateDebugInfo('✅ Contexto 2D obtenido');
                
                // Crear gráfico simple con los datos de evolución
                const chart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: testData.evolucion_mensual.map(item => {
                            const date = new Date(item.dia);
                            return date.toLocaleDateString('es-ES', { month: 'short', day: 'numeric' });
                        }),
                        datasets: [{
                            label: 'Prospectos',
                            data: testData.evolucion_mensual.map(item => parseInt(item.prospectos) || 0),
                            borderColor: '#3b82f6',
                            backgroundColor: '#3b82f620',
                            tension: 0.4,
                            pointRadius: 5,
                            pointHoverRadius: 8
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top',
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        }
                    }
                });
                
                updateDebugInfo('✅ Gráfico creado exitosamente');
                
            } catch (error) {
                updateDebugInfo('❌ Error creando gráfico: ' + error.message);
                console.error('Error completo:', error);
            }
        });
        
        window.addEventListener('load', function() {
            updateDebugInfo('Window load event disparado');
        });
    </script>
</body>
</html>
