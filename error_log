[31-Mar-2025 22:35:02 UTC] [2025-03-31 22:35:02] Logout - Usuario: <PERSON>@gestarservicios.cl - IP: ************
[31-Mar-2025 22:44:52 UTC] Datos recibidos en guardar_formulario.php: Array
(
    [tipo_cliente] => Cliente Vigente
    [rut] => 76.927.007-8
    [razon_social] => EMPRESAS IITC SPA
    [nombre_representante1] => JANNINA ALEJANDRA PARRA GONZALEZ
    [rut_representante1] => 18.975.491-4
    [nombre_representante2] => 
    [rut_representante2] => 
    [nombre_representante3] => 
    [rut_representante3] => 
    [sistema_creacion] => Tradicional
    [fecha_creacion] => 2025-03-31
    [notaria] => 
    [actividad_economica] => 473000
    [fecha_constitucion] => 2018-10-05
    [direccion] => ISABEL RIQUELME 10 VILLARRICA
    [comuna] => VILLARRICA
    [pagina_web] => 
    [email] => <EMAIL>
    [telefono] => 950497734
    [clasificacion_sii] => Antiguo
    [contacto_nombre] => JANNINA ALEJANDRA PARRA GONZALEZ
    [contacto_rut] => 18.975.491-4
    [contacto_telefono] => 950497734
    [contacto_email] => <EMAIL>
    [contacto_backup_nombre] => JUAN MANUEL MORALES CAMPOS
    [contacto_backup_rut] => 11.456.372-2
    [contacto_backup_telefono] => 993378142
    [contacto_backup_email] => <EMAIL>
    [morosos_plan] => 
    [morosos_consultas] => 
    [morosos_uf] => 
    [morosos_descuento] => 0
    [morosos_nuevo_valor] => 
    [advanced_plan] => 25
    [advanced_consultas] => 0.054
    [advanced_uf] => 1.34
    [advanced_descuento] => 0
    [advanced_nuevo_valor] => 
)

[31-Mar-2025 22:44:52 UTC] ID de usuario de la sesión: 1
[01-Apr-2025 03:01:34 UTC] [2025-04-01 03:01:34] Error de autenticación - Usuario no identificado - IP: ************
[01-Apr-2025 03:18:12 UTC] PHP Notice:  Undefined variable: conn in /home/<USER>/public_html/intranet/dist/cargar_ejecutivos.php on line 31
[01-Apr-2025 03:18:12 UTC] PHP Warning:  mysqli_query() expects parameter 1 to be mysqli, null given in /home/<USER>/public_html/intranet/dist/cargar_ejecutivos.php on line 31
[01-Apr-2025 03:18:12 UTC] PHP Notice:  Undefined variable: conn in /home/<USER>/public_html/intranet/dist/cargar_ejecutivos.php on line 45
[01-Apr-2025 03:18:12 UTC] PHP Warning:  mysqli_error() expects parameter 1 to be mysqli, null given in /home/<USER>/public_html/intranet/dist/cargar_ejecutivos.php on line 45
[01-Apr-2025 03:18:12 UTC] PHP Notice:  Undefined variable: conn in /home/<USER>/public_html/intranet/dist/cargar_ejecutivos.php on line 49
[01-Apr-2025 03:18:12 UTC] PHP Warning:  mysqli_close() expects parameter 1 to be mysqli, null given in /home/<USER>/public_html/intranet/dist/cargar_ejecutivos.php on line 49
[01-Apr-2025 03:22:14 UTC] PHP Notice:  Undefined variable: conn in /home/<USER>/public_html/intranet/dist/cargar_prospecto.php on line 31
[01-Apr-2025 03:22:14 UTC] PHP Warning:  mysqli_query() expects parameter 1 to be mysqli, null given in /home/<USER>/public_html/intranet/dist/cargar_prospecto.php on line 31
[01-Apr-2025 03:22:14 UTC] PHP Notice:  Undefined variable: conn in /home/<USER>/public_html/intranet/dist/cargar_prospecto.php on line 45
[01-Apr-2025 03:22:14 UTC] PHP Warning:  mysqli_error() expects parameter 1 to be mysqli, null given in /home/<USER>/public_html/intranet/dist/cargar_prospecto.php on line 45
[01-Apr-2025 03:22:14 UTC] PHP Notice:  Undefined variable: conn in /home/<USER>/public_html/intranet/dist/cargar_prospecto.php on line 49
[01-Apr-2025 03:22:14 UTC] PHP Warning:  mysqli_close() expects parameter 1 to be mysqli, null given in /home/<USER>/public_html/intranet/dist/cargar_prospecto.php on line 49
[01-Apr-2025 03:22:49 UTC] PHP Notice:  Undefined variable: conn in /home/<USER>/public_html/intranet/dist/guardar_prospecto.php on line 29
[01-Apr-2025 03:22:49 UTC] PHP Warning:  mysqli_real_escape_string() expects parameter 1 to be mysqli, null given in /home/<USER>/public_html/intranet/dist/guardar_prospecto.php on line 22
[01-Apr-2025 03:22:49 UTC] PHP Notice:  Undefined variable: conn in /home/<USER>/public_html/intranet/dist/guardar_prospecto.php on line 30
[01-Apr-2025 03:22:49 UTC] PHP Warning:  mysqli_real_escape_string() expects parameter 1 to be mysqli, null given in /home/<USER>/public_html/intranet/dist/guardar_prospecto.php on line 22
[01-Apr-2025 03:22:49 UTC] PHP Notice:  Undefined variable: conn in /home/<USER>/public_html/intranet/dist/guardar_prospecto.php on line 31
[01-Apr-2025 03:22:49 UTC] PHP Warning:  mysqli_real_escape_string() expects parameter 1 to be mysqli, null given in /home/<USER>/public_html/intranet/dist/guardar_prospecto.php on line 22
[01-Apr-2025 03:22:49 UTC] PHP Notice:  Undefined variable: conn in /home/<USER>/public_html/intranet/dist/guardar_prospecto.php on line 32
[01-Apr-2025 03:22:49 UTC] PHP Warning:  mysqli_real_escape_string() expects parameter 1 to be mysqli, null given in /home/<USER>/public_html/intranet/dist/guardar_prospecto.php on line 22
[01-Apr-2025 03:22:49 UTC] PHP Notice:  Undefined variable: conn in /home/<USER>/public_html/intranet/dist/guardar_prospecto.php on line 33
[01-Apr-2025 03:22:49 UTC] PHP Warning:  mysqli_real_escape_string() expects parameter 1 to be mysqli, null given in /home/<USER>/public_html/intranet/dist/guardar_prospecto.php on line 22
[01-Apr-2025 03:22:49 UTC] PHP Notice:  Undefined variable: conn in /home/<USER>/public_html/intranet/dist/guardar_prospecto.php on line 34
[01-Apr-2025 03:22:49 UTC] PHP Warning:  mysqli_real_escape_string() expects parameter 1 to be mysqli, null given in /home/<USER>/public_html/intranet/dist/guardar_prospecto.php on line 22
[01-Apr-2025 03:22:49 UTC] PHP Notice:  Undefined variable: conn in /home/<USER>/public_html/intranet/dist/guardar_prospecto.php on line 35
[01-Apr-2025 03:22:49 UTC] PHP Warning:  mysqli_real_escape_string() expects parameter 1 to be mysqli, null given in /home/<USER>/public_html/intranet/dist/guardar_prospecto.php on line 22
[01-Apr-2025 03:22:49 UTC] PHP Notice:  Undefined variable: conn in /home/<USER>/public_html/intranet/dist/guardar_prospecto.php on line 36
[01-Apr-2025 03:22:49 UTC] PHP Warning:  mysqli_real_escape_string() expects parameter 1 to be mysqli, null given in /home/<USER>/public_html/intranet/dist/guardar_prospecto.php on line 22
[01-Apr-2025 03:22:51 UTC] PHP Notice:  Undefined variable: conn in /home/<USER>/public_html/intranet/dist/cargar_prospecto.php on line 31
[01-Apr-2025 03:22:51 UTC] PHP Warning:  mysqli_query() expects parameter 1 to be mysqli, null given in /home/<USER>/public_html/intranet/dist/cargar_prospecto.php on line 31
[01-Apr-2025 03:22:51 UTC] PHP Notice:  Undefined variable: conn in /home/<USER>/public_html/intranet/dist/cargar_prospecto.php on line 45
[01-Apr-2025 03:22:51 UTC] PHP Warning:  mysqli_error() expects parameter 1 to be mysqli, null given in /home/<USER>/public_html/intranet/dist/cargar_prospecto.php on line 45
[01-Apr-2025 03:22:51 UTC] PHP Notice:  Undefined variable: conn in /home/<USER>/public_html/intranet/dist/cargar_prospecto.php on line 49
[01-Apr-2025 03:22:51 UTC] PHP Warning:  mysqli_close() expects parameter 1 to be mysqli, null given in /home/<USER>/public_html/intranet/dist/cargar_prospecto.php on line 49
[01-Apr-2025 12:22:19 UTC] [2025-04-01 12:22:19] Error de autenticación - Usuario no identificado - IP: ************
[01-Apr-2025 12:25:04 UTC] [2025-04-01 12:25:04] Logout - Usuario: <EMAIL> - IP: ************
[01-Apr-2025 15:03:15 UTC] [2025-04-01 15:03:15] Error de autenticación - Usuario no identificado - IP: **************
[01-Apr-2025 22:14:14 UTC] [2025-04-01 22:14:14] Error de autenticación - Usuario no identificado - IP: ************
[01-Apr-2025 22:14:14 UTC] [2025-04-01 22:14:14] Error de autenticación - Usuario no identificado - IP: ************
[01-Apr-2025 22:14:14 UTC] [2025-04-01 22:14:14] Error de autenticación - Usuario no identificado - IP: ************
[01-Apr-2025 22:17:59 UTC] [2025-04-01 22:17:59] Logout - Usuario: <EMAIL> - IP: ************
[02-Apr-2025 02:03:10 UTC] [2025-04-02 02:03:10] Error de autenticación - Usuario no identificado - IP: ************
[02-Apr-2025 02:03:10 UTC] [2025-04-02 02:03:10] Error de autenticación - Usuario no identificado - IP: ************
[02-Apr-2025 02:03:10 UTC] [2025-04-02 02:03:10] Error de autenticación - Usuario no identificado - IP: ************
[04-Apr-2025 20:36:34 UTC] [2025-04-04 20:36:34] Error de autenticación - Usuario no identificado - IP: ***************
[04-Apr-2025 22:15:14 UTC] [2025-04-04 22:15:14] Error de autenticación - Usuario no identificado - IP: ***************
[04-Apr-2025 22:16:32 UTC] Solicitud recibida en get_plan_details.php
[04-Apr-2025 22:16:32 UTC] POST data: Array
(
    [plan] => XS
)

[04-Apr-2025 22:16:32 UTC] Plan seleccionado: XS
[04-Apr-2025 22:16:32 UTC] Datos encontrados - Plan consumo: Hasta $5.000.000, Valor UF: 2.43
[04-Apr-2025 23:17:44 UTC] [2025-04-04 23:17:44] Error de autenticación - Usuario no identificado - IP: ***************
[04-Apr-2025 23:19:50 UTC] Solicitud recibida en get_plan_details.php
[04-Apr-2025 23:19:50 UTC] POST data: Array
(
    [plan] => XS
)

[04-Apr-2025 23:19:50 UTC] Plan seleccionado: XS
[04-Apr-2025 23:19:50 UTC] Datos encontrados - Plan consumo: Hasta $5.000.000, Valor UF: 2.43
[05-Apr-2025 00:48:44 UTC] [2025-04-05 00:48:44] Error de autenticación - Usuario no identificado - IP: ***************
[05-Apr-2025 00:50:18 UTC] Solicitud recibida en get_plan_details.php
[05-Apr-2025 00:50:18 UTC] POST data: Array
(
    [plan] => XS
)

[05-Apr-2025 00:50:18 UTC] Plan seleccionado: XS
[05-Apr-2025 00:50:18 UTC] Datos encontrados - Plan consumo: Hasta $5.000.000, Valor UF: 2.43
[05-Apr-2025 00:50:38 UTC] Datos recibidos en guardar_formulario.php: Array
(
    [tipo_cliente] => Cliente Vigente
    [rut] => 20.047.582-8
    [razon_social] => Razón Social test2
    [nombre_representante1] => Razón Social test
    [rut_representante1] => 7.518.994-K
    [nombre_representante2] => Razón Social test
    [rut_representante2] => 
    [nombre_representante3] => 
    [rut_representante3] => 
    [sistema_creacion] => Tradicional
    [fecha_creacion] => 2025-04-04
    [notaria] => 12312312
    [actividad_economica] => 1232131
    [fecha_constitucion] => 2025-04-04
    [direccion] => Catapilco 9171 
    [comuna] => La Florida
    [pagina_web] => www.experian.cl
    [email] => <EMAIL>
    [telefono] => 930274938
    [clasificacion_sii] => Antiguo
    [contacto_nombre] => Nicolas
    [contacto_rut] => 17.227.171-5
    [contacto_telefono] => 930274939
    [contacto_email] => <EMAIL>
    [contacto_backup_nombre] => 
    [contacto_backup_rut] => 
    [contacto_backup_telefono] => 
    [contacto_backup_email] => 
    [morosos_plan] => XS
    [morosos_consultas] => Hasta $5.000.000
    [morosos_uf] => 2.43
    [morosos_descuento] => 5
    [morosos_nuevo_valor] => 2.31
    [advanced_plan] => 200
    [advanced_consultas] => 0.036
    [advanced_uf] => 7.26
    [advanced_descuento] => 5
    [advanced_nuevo_valor] => 6.90
    [clave_nombre] => Nombres_key_user_test
    [clave_rut] => 17.227.171-5
    [clave_email] => <EMAIL>
    [clave_telefono] => 930219023
    [backup_clave_nombre] => Nombres_key_user_test
    [backup_clave_rut] => 17.227.171-5
    [backup_clave_email] => <EMAIL>
    [backup_clave_telefono] => 930219023
)

[05-Apr-2025 00:50:38 UTC] ID de usuario de la sesión: 4
[05-Apr-2025 00:50:38 UTC] Campos a insertar: id_usuario, tipo_cliente, rut, razon_social, nombre_representante1, rut_representante1, nombre_representante2, rut_representante2, nombre_representante3, rut_representante3, sistema_creacion, fecha_creacion, notaria, actividad_economica, fecha_constitucion, direccion, comuna, pagina_web, email, telefono, clasificacion_sii, contacto_nombre, contacto_rut, contacto_telefono, contacto_email, contacto_backup_nombre, contacto_backup_rut, contacto_backup_telefono, contacto_backup_email, morosos_plan, morosos_consultas, morosos_uf, morosos_descuento, morosos_nuevo_valor, advanced_plan, advanced_consultas, advanced_uf, advanced_descuento, advanced_nuevo_valor, key_user_nombre, key_user_rut, key_user_email, key_user_telefono, key_user_backup_nombre, key_user_backup_rut, key_user_backup_email, key_user_backup_telefono
[05-Apr-2025 00:50:38 UTC] Tipos de parámetros: issssssssssssssssssssssssssssssdidssdidssssssss
[05-Apr-2025 00:50:38 UTC] Parámetros: Array
(
    [0] => 4
    [1] => Cliente Vigente
    [2] => 20.047.582-8
    [3] => Razón Social test2
    [4] => Razón Social test
    [5] => 7.518.994-K
    [6] => Razón Social test
    [7] => 
    [8] => 
    [9] => 
    [10] => Tradicional
    [11] => 2025-04-04
    [12] => 12312312
    [13] => 1232131
    [14] => 2025-04-04
    [15] => Catapilco 9171 
    [16] => La Florida
    [17] => www.experian.cl
    [18] => <EMAIL>
    [19] => 930274938
    [20] => Antiguo
    [21] => Nicolas
    [22] => 17.227.171-5
    [23] => 930274939
    [24] => <EMAIL>
    [25] => 
    [26] => 
    [27] => 
    [28] => 
    [29] => XS
    [30] => Hasta $5.000.000
    [31] => 2.43
    [32] => 5
    [33] => 2.31
    [34] => 200
    [35] => 0.036
    [36] => 7.26
    [37] => 5
    [38] => 6.9
    [39] => Nombres_key_user_test
    [40] => 17.227.171-5
    [41] => <EMAIL>
    [42] => 930219023
    [43] => Nombres_key_user_test
    [44] => 17.227.171-5
    [45] => <EMAIL>
    [46] => 930219023
)

[05-Apr-2025 01:00:22 UTC] [2025-04-05 01:00:22] Logout - Usuario: <EMAIL> - IP: ***************
[05-Apr-2025 01:00:49 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method mysqli_stmt::get_result() in /home/<USER>/public_html/intranet/dist/view_table.php:64
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/view_table.php on line 64
[05-Apr-2025 01:00:53 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method mysqli_stmt::get_result() in /home/<USER>/public_html/intranet/dist/view_table.php:64
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/view_table.php on line 64
[05-Apr-2025 01:33:41 UTC] Solicitud recibida en get_plan_details.php
[05-Apr-2025 01:33:41 UTC] POST data: Array
(
    [plan] => XS
)

[05-Apr-2025 01:33:41 UTC] Plan seleccionado: XS
[05-Apr-2025 01:33:41 UTC] Datos encontrados - Plan consumo: Hasta $5.000.000, Valor UF: 2.43
[05-Apr-2025 01:34:21 UTC] Datos recibidos en guardar_formulario.php: Array
(
    [tipo_cliente] => Cliente Vigente
    [rut] => 20.047.582-8
    [razon_social] => Nico SA
    [nombre_representante1] => Razón Social test
    [rut_representante1] => 
    [nombre_representante2] => 
    [rut_representante2] => 
    [nombre_representante3] => 
    [rut_representante3] => 
    [sistema_creacion] => Tradicional
    [fecha_creacion] => 2025-04-04
    [notaria] => 12312312
    [actividad_economica] => 123123
    [fecha_constitucion] => 2025-04-04
    [direccion] => Catapilco 9171 
    [comuna] => La Florida
    [pagina_web] => www.experian.cl
    [email] => <EMAIL>
    [telefono] => 930274938
    [clasificacion_sii] => Nuevo
    [contacto_nombre] => Nicolas
    [contacto_rut] => 17.227.171-5
    [contacto_telefono] => 930274939
    [contacto_email] => <EMAIL>
    [contacto_backup_nombre] => 
    [contacto_backup_rut] => 
    [contacto_backup_telefono] => 
    [contacto_backup_email] => 
    [morosos_plan] => XS
    [morosos_consultas] => Hasta $5.000.000
    [morosos_uf] => 2.43
    [morosos_descuento] => 15%
    [morosos_nuevo_valor] => 2.07
    [advanced_plan] => 400
    [advanced_consultas] => 0.023
    [advanced_uf] => 9.20
    [advanced_descuento] => 15%
    [advanced_nuevo_valor] => 7.82
    [clave_nombre] => Nombres_key_user_test
    [clave_rut] => 17.227.171-5
    [clave_email] => <EMAIL>
    [clave_telefono] => 930219023
    [backup_clave_nombre] => Nombres_key_user_test22
    [backup_clave_rut] => 17.227.171-5
    [backup_clave_email] => <EMAIL>
    [backup_clave_telefono] => 930219023
)

[05-Apr-2025 01:34:21 UTC] ID de usuario de la sesión: 4
[05-Apr-2025 01:34:21 UTC] No se ha recibido ningún archivo o hubo un error en la subida
[05-Apr-2025 01:34:21 UTC] Campos a insertar: id_usuario, tipo_cliente, rut, razon_social, nombre_representante1, rut_representante1, nombre_representante2, rut_representante2, nombre_representante3, rut_representante3, sistema_creacion, fecha_creacion, notaria, actividad_economica, fecha_constitucion, direccion, comuna, pagina_web, email, telefono, clasificacion_sii, contacto_nombre, contacto_rut, contacto_telefono, contacto_email, contacto_backup_nombre, contacto_backup_rut, contacto_backup_telefono, contacto_backup_email, morosos_plan, morosos_consultas, morosos_uf, morosos_descuento, morosos_nuevo_valor, advanced_plan, advanced_consultas, advanced_uf, advanced_descuento, advanced_nuevo_valor, key_user_nombre, key_user_rut, key_user_email, key_user_telefono, key_user_backup_nombre, key_user_backup_rut, key_user_backup_email, key_user_backup_telefono
[05-Apr-2025 01:34:21 UTC] Tipos de parámetros: issssssssssssssssssssssssssssssdidssdidssssssss
[05-Apr-2025 01:34:21 UTC] Parámetros: Array
(
    [0] => 4
    [1] => Cliente Vigente
    [2] => 20.047.582-8
    [3] => Nico SA
    [4] => Razón Social test
    [5] => 
    [6] => 
    [7] => 
    [8] => 
    [9] => 
    [10] => Tradicional
    [11] => 2025-04-04
    [12] => 12312312
    [13] => 123123
    [14] => 2025-04-04
    [15] => Catapilco 9171 
    [16] => La Florida
    [17] => www.experian.cl
    [18] => <EMAIL>
    [19] => 930274938
    [20] => Nuevo
    [21] => Nicolas
    [22] => 17.227.171-5
    [23] => 930274939
    [24] => <EMAIL>
    [25] => 
    [26] => 
    [27] => 
    [28] => 
    [29] => XS
    [30] => Hasta $5.000.000
    [31] => 2.43
    [32] => 15
    [33] => 2.07
    [34] => 400
    [35] => 0.023
    [36] => 9.2
    [37] => 15
    [38] => 7.82
    [39] => Nombres_key_user_test
    [40] => 17.227.171-5
    [41] => <EMAIL>
    [42] => 930219023
    [43] => Nombres_key_user_test22
    [44] => 17.227.171-5
    [45] => <EMAIL>
    [46] => 930219023
)

[05-Apr-2025 01:40:57 UTC] Solicitud recibida en get_plan_details.php
[05-Apr-2025 01:40:57 UTC] POST data: Array
(
    [plan] => S
)

[05-Apr-2025 01:40:57 UTC] Plan seleccionado: S
[05-Apr-2025 01:40:57 UTC] Datos encontrados - Plan consumo: Hasta $8.000.000, Valor UF: 3.33
[05-Apr-2025 01:41:02 UTC] Solicitud recibida en get_plan_details.php
[05-Apr-2025 01:41:02 UTC] POST data: Array
(
    [plan] => M
)

[05-Apr-2025 01:41:02 UTC] Plan seleccionado: M
[05-Apr-2025 01:41:02 UTC] Datos encontrados - Plan consumo: Hasta $11.000.000, Valor UF: 4.23
[05-Apr-2025 01:42:28 UTC] Solicitud recibida en get_plan_details.php
[05-Apr-2025 01:42:28 UTC] POST data: Array
(
    [plan] => S
)

[05-Apr-2025 01:42:28 UTC] Plan seleccionado: S
[05-Apr-2025 01:42:28 UTC] Datos encontrados - Plan consumo: Hasta $8.000.000, Valor UF: 3.33
[05-Apr-2025 01:42:56 UTC] Datos recibidos en guardar_formulario.php: Array
(
    [tipo_cliente] => Cliente Vigente
    [rut] => 20.047.582-8
    [razon_social] => Razón Social test2
    [nombre_representante1] => Razón Social test
    [rut_representante1] => 
    [nombre_representante2] => 
    [rut_representante2] => 
    [nombre_representante3] => 
    [rut_representante3] => 
    [sistema_creacion] => Tradicional
    [fecha_creacion] => 2025-04-04
    [notaria] => 12312312
    [actividad_economica] => 1232131
    [fecha_constitucion] => 2025-04-04
    [direccion] => Catapilco 9171 
    [comuna] => La Florida
    [pagina_web] => www.experian.cl
    [email] => <EMAIL>
    [telefono] => 930274938
    [clasificacion_sii] => Nuevo
    [contacto_nombre] => Nicolas
    [contacto_rut] => 17.227.171-5
    [contacto_telefono] => 930274939
    [contacto_email] => <EMAIL>
    [contacto_backup_nombre] => 
    [contacto_backup_rut] => 
    [contacto_backup_telefono] => 
    [contacto_backup_email] => 
    [morosos_plan] => S
    [morosos_consultas] => Hasta $8.000.000
    [morosos_uf] => 3.33
    [morosos_descuento] => 5
    [morosos_nuevo_valor] => 3.16
    [advanced_plan] => 500
    [advanced_consultas] => 0.020
    [advanced_uf] => 10.00
    [advanced_descuento] => 5
    [advanced_nuevo_valor] => 9.50
    [clave_nombre] => Nombres_key_user_test
    [clave_rut] => 17.227.171-5
    [clave_email] => <EMAIL>
    [clave_telefono] => 930219023
    [backup_clave_nombre] => Nombres_key_user_test22
    [backup_clave_rut] => 17.227.171-5
    [backup_clave_email] => <EMAIL>
    [backup_clave_telefono] => 930219023
)

[05-Apr-2025 01:42:56 UTC] ID de usuario de la sesión: 4
[05-Apr-2025 01:42:56 UTC] Archivo CI recibido: Array
(
    [name] => 1.png
    [type] => image/png
    [tmp_name] => /tmp/phpOLOx9R
    [error] => 0
    [size] => 76987
)

[05-Apr-2025 01:42:56 UTC] Archivo guardado en: documentosExperian/ci_4_67f08aa05a9df.png
[05-Apr-2025 01:42:56 UTC] Campos a insertar: id_usuario, ruta_ci, tipo_cliente, rut, razon_social, nombre_representante1, rut_representante1, nombre_representante2, rut_representante2, nombre_representante3, rut_representante3, sistema_creacion, fecha_creacion, notaria, actividad_economica, fecha_constitucion, direccion, comuna, pagina_web, email, telefono, clasificacion_sii, contacto_nombre, contacto_rut, contacto_telefono, contacto_email, contacto_backup_nombre, contacto_backup_rut, contacto_backup_telefono, contacto_backup_email, morosos_plan, morosos_consultas, morosos_uf, morosos_descuento, morosos_nuevo_valor, advanced_plan, advanced_consultas, advanced_uf, advanced_descuento, advanced_nuevo_valor, key_user_nombre, key_user_rut, key_user_email, key_user_telefono, key_user_backup_nombre, key_user_backup_rut, key_user_backup_email, key_user_backup_telefono
[05-Apr-2025 01:42:56 UTC] Tipos de parámetros: isssssssssssssssssssssssssssssssdidssdidssssssss
[05-Apr-2025 01:42:56 UTC] Parámetros: Array
(
    [0] => 4
    [1] => documentosExperian/ci_4_67f08aa05a9df.png
    [2] => Cliente Vigente
    [3] => 20.047.582-8
    [4] => Razón Social test2
    [5] => Razón Social test
    [6] => 
    [7] => 
    [8] => 
    [9] => 
    [10] => 
    [11] => Tradicional
    [12] => 2025-04-04
    [13] => 12312312
    [14] => 1232131
    [15] => 2025-04-04
    [16] => Catapilco 9171 
    [17] => La Florida
    [18] => www.experian.cl
    [19] => <EMAIL>
    [20] => 930274938
    [21] => Nuevo
    [22] => Nicolas
    [23] => 17.227.171-5
    [24] => 930274939
    [25] => <EMAIL>
    [26] => 
    [27] => 
    [28] => 
    [29] => 
    [30] => S
    [31] => Hasta $8.000.000
    [32] => 3.33
    [33] => 5
    [34] => 3.16
    [35] => 500
    [36] => 0.020
    [37] => 10
    [38] => 5
    [39] => 9.5
    [40] => Nombres_key_user_test
    [41] => 17.227.171-5
    [42] => <EMAIL>
    [43] => 930219023
    [44] => Nombres_key_user_test22
    [45] => 17.227.171-5
    [46] => <EMAIL>
    [47] => 930219023
)

[05-Apr-2025 01:54:23 UTC] Solicitud recibida en get_plan_details.php
[05-Apr-2025 01:54:23 UTC] POST data: Array
(
    [plan] => S
)

[05-Apr-2025 01:54:23 UTC] Plan seleccionado: S
[05-Apr-2025 01:54:23 UTC] Datos encontrados - Plan consumo: Hasta $8.000.000, Valor UF: 3.33
[05-Apr-2025 01:55:10 UTC] Datos recibidos en guardar_formulario.php: Array
(
    [tipo_cliente] => Cliente Vigente
    [rut] => 20.047.582-8
    [razon_social] => Nico SA
    [nombre_representante1] => Razón Social test
    [rut_representante1] => 7.518.994-K
    [nombre_representante2] => 
    [rut_representante2] => 
    [nombre_representante3] => 
    [rut_representante3] => 
    [sistema_creacion] => Empresa por un día
    [fecha_creacion] => 2025-04-04
    [notaria] => 12312312
    [actividad_economica] => 1231231
    [fecha_constitucion] => 2025-04-04
    [direccion] => Catapilco 9171 
    [comuna] => La Florida
    [pagina_web] => www.experian.cl
    [email] => <EMAIL>
    [telefono] => 930274938
    [clasificacion_sii] => Antiguo
    [contacto_nombre] => Nicolas
    [contacto_rut] => 17.227.171-5
    [contacto_telefono] => 930274939
    [contacto_email] => <EMAIL>
    [contacto_backup_nombre] => 
    [contacto_backup_rut] => 
    [contacto_backup_telefono] => 
    [contacto_backup_email] => 
    [morosos_plan] => S
    [morosos_consultas] => Hasta $8.000.000
    [morosos_uf] => 3.33
    [morosos_descuento] => 10
    [morosos_nuevo_valor] => 3.00
    [advanced_plan] => 2000
    [advanced_consultas] => 0.014
    [advanced_uf] => 28.00
    [advanced_descuento] => 10
    [advanced_nuevo_valor] => 25.20
    [clave_nombre] => Nombres_key_user_test
    [clave_rut] => 17.227.171-5
    [clave_email] => <EMAIL>
    [clave_telefono] => 930219023
    [backup_clave_nombre] => Nombres_key_user_test
    [backup_clave_rut] => 17.227.171-5
    [backup_clave_email] => <EMAIL>
    [backup_clave_telefono] => 930219023
)

[05-Apr-2025 01:55:10 UTC] Archivos recibidos: Array
(
    [archivo_ci] => Array
        (
            [name] => Imagen de WhatsApp 2024-08-27 a las 12.22.36_d7743eea.jpg
            [type] => image/jpeg
            [tmp_name] => /tmp/phpK9u3lM
            [error] => 0
            [size] => 30778
        )

    [archivo_erut] => Array
        (
            [name] => Imagen de WhatsApp 2024-08-15 a las 10.07.48_c1e68a62.jpg
            [type] => image/jpeg
            [tmp_name] => /tmp/phpmDEh5b
            [error] => 0
            [size] => 46010
        )

    [archivo_extracto] => Array
        (
            [name] => Imagen de WhatsApp 2024-06-24 a las 12.10.17_8218c8b8.jpg
            [type] => image/jpeg
            [tmp_name] => /tmp/phpdZ0ITl
            [error] => 0
            [size] => 152709
        )

    [archivo_ci_frente] => Array
        (
            [name] => Imagen de WhatsApp 2024-08-15 a las 10.07.48_c1e68a62.jpg
            [type] => image/jpeg
            [tmp_name] => /tmp/php9oZE26
            [error] => 0
            [size] => 46010
        )

    [archivo_ci_detras] => Array
        (
            [name] => Imagen de WhatsApp 2024-08-27 a las 12.22.36_d7743eea.jpg
            [type] => image/jpeg
            [tmp_name] => /tmp/phpnpwUnF
            [error] => 0
            [size] => 30778
        )

    [archivo_carpeta_tributaria] => Array
        (
            [name] => Imagen de WhatsApp 2024-08-27 a las 12.22.36_7fc71358.jpg
            [type] => image/jpeg
            [tmp_name] => /tmp/php3JltsB
            [error] => 0
            [size] => 39771
        )

    [archivo_consulta_terceros] => Array
        (
            [name] => Imagen de WhatsApp 2024-08-27 a las 12.22.36_7fc71358.jpg
            [type] => image/jpeg
            [tmp_name] => /tmp/phpzofAj8
            [error] => 0
            [size] => 39771
        )

)

[05-Apr-2025 01:55:10 UTC] ID de usuario de la sesión: 4
[05-Apr-2025 01:55:10 UTC] Procesando archivo: archivo_ci
[05-Apr-2025 01:55:10 UTC] Archivo 'archivo_ci' guardado en: documentosExperian/ci_4_67f08d7e01d5e.jpg
[05-Apr-2025 01:55:10 UTC] Procesando archivo: archivo_erut
[05-Apr-2025 01:55:10 UTC] Archivo 'archivo_erut' guardado en: documentosExperian/erut_4_67f08d7e01fec.jpg
[05-Apr-2025 01:55:10 UTC] Procesando archivo: archivo_extracto
[05-Apr-2025 01:55:10 UTC] Archivo 'archivo_extracto' guardado en: documentosExperian/extracto_4_67f08d7e0220c.jpg
[05-Apr-2025 01:55:10 UTC] Procesando archivo: archivo_ci_frente
[05-Apr-2025 01:55:10 UTC] Archivo 'archivo_ci_frente' guardado en: documentosExperian/cifrente_4_67f08d7e025a0.jpg
[05-Apr-2025 01:55:10 UTC] Procesando archivo: archivo_ci_detras
[05-Apr-2025 01:55:10 UTC] Archivo 'archivo_ci_detras' guardado en: documentosExperian/cidetras_4_67f08d7e027cb.jpg
[05-Apr-2025 01:55:10 UTC] Procesando archivo: archivo_carpeta_tributaria
[05-Apr-2025 01:55:10 UTC] Archivo 'archivo_carpeta_tributaria' guardado en: documentosExperian/carptrib_4_67f08d7e029ae.jpg
[05-Apr-2025 01:55:10 UTC] Procesando archivo: archivo_consulta_terceros
[05-Apr-2025 01:55:10 UTC] Archivo 'archivo_consulta_terceros' guardado en: documentosExperian/consterc_4_67f08d7e02bb6.jpg
[05-Apr-2025 01:55:10 UTC] Campos a insertar: id_usuario, ruta_ci, ruta_erut, ruta_extracto, ruta_ci_frente, ruta_ci_detras, ruta_carpeta_tributaria, ruta_consulta_terceros, tipo_cliente, rut, razon_social, nombre_representante1, rut_representante1, nombre_representante2, rut_representante2, nombre_representante3, rut_representante3, sistema_creacion, fecha_creacion, notaria, actividad_economica, fecha_constitucion, direccion, comuna, pagina_web, email, telefono, clasificacion_sii, contacto_nombre, contacto_rut, contacto_telefono, contacto_email, contacto_backup_nombre, contacto_backup_rut, contacto_backup_telefono, contacto_backup_email, morosos_plan, morosos_consultas, morosos_uf, morosos_descuento, morosos_nuevo_valor, advanced_plan, advanced_consultas, advanced_uf, advanced_descuento, advanced_nuevo_valor, key_user_nombre, key_user_rut, key_user_email, key_user_telefono, key_user_backup_nombre, key_user_backup_rut, key_user_backup_email, key_user_backup_telefono
[05-Apr-2025 01:55:10 UTC] Tipos de parámetros: isssssssssssssssssssssssssssssssssssssdidssdidssssssss
[05-Apr-2025 01:55:10 UTC] Parámetros: Array
(
    [0] => 4
    [1] => documentosExperian/ci_4_67f08d7e01d5e.jpg
    [2] => documentosExperian/erut_4_67f08d7e01fec.jpg
    [3] => documentosExperian/extracto_4_67f08d7e0220c.jpg
    [4] => documentosExperian/cifrente_4_67f08d7e025a0.jpg
    [5] => documentosExperian/cidetras_4_67f08d7e027cb.jpg
    [6] => documentosExperian/carptrib_4_67f08d7e029ae.jpg
    [7] => documentosExperian/consterc_4_67f08d7e02bb6.jpg
    [8] => Cliente Vigente
    [9] => 20.047.582-8
    [10] => Nico SA
    [11] => Razón Social test
    [12] => 7.518.994-K
    [13] => 
    [14] => 
    [15] => 
    [16] => 
    [17] => Empresa por un día
    [18] => 2025-04-04
    [19] => 12312312
    [20] => 1231231
    [21] => 2025-04-04
    [22] => Catapilco 9171 
    [23] => La Florida
    [24] => www.experian.cl
    [25] => <EMAIL>
    [26] => 930274938
    [27] => Antiguo
    [28] => Nicolas
    [29] => 17.227.171-5
    [30] => 930274939
    [31] => <EMAIL>
    [32] => 
    [33] => 
    [34] => 
    [35] => 
    [36] => S
    [37] => Hasta $8.000.000
    [38] => 3.33
    [39] => 10
    [40] => 3
    [41] => 2000
    [42] => 0.014
    [43] => 28
    [44] => 10
    [45] => 25.2
    [46] => Nombres_key_user_test
    [47] => 17.227.171-5
    [48] => <EMAIL>
    [49] => 930219023
    [50] => Nombres_key_user_test
    [51] => 17.227.171-5
    [52] => <EMAIL>
    [53] => 930219023
)

[05-Apr-2025 02:14:09 UTC] Datos recibidos en guardar_formulario.php: Array
(
    [tipo_cliente] => Cliente Vigente
    [rut] => 20.047.582-8
    [razon_social] => Nico SA
    [nombre_representante1] => Razón Social test
    [rut_representante1] => 7.518.994-K
    [nombre_representante2] => 
    [rut_representante2] => 
    [nombre_representante3] => 
    [rut_representante3] => 
    [sistema_creacion] => Empresa por un día
    [fecha_creacion] => 2025-04-04
    [notaria] => 12312312
    [actividad_economica] => 1231231
    [fecha_constitucion] => 2025-04-04
    [direccion] => Catapilco 9171 
    [comuna] => La Florida
    [pagina_web] => www.experian.cl
    [email] => <EMAIL>
    [telefono] => 930274938
    [clasificacion_sii] => Antiguo
    [contacto_nombre] => Nicolas
    [contacto_rut] => 17.227.171-5
    [contacto_telefono] => 930274939
    [contacto_email] => <EMAIL>
    [contacto_backup_nombre] => 
    [contacto_backup_rut] => 
    [contacto_backup_telefono] => 
    [contacto_backup_email] => 
    [morosos_plan] => S
    [morosos_consultas] => Hasta $8.000.000
    [morosos_uf] => 3.33
    [morosos_descuento] => 10
    [morosos_nuevo_valor] => 3.00
    [advanced_plan] => 2000
    [advanced_consultas] => 0.014
    [advanced_uf] => 28.00
    [advanced_descuento] => 10
    [advanced_nuevo_valor] => 25.20
    [clave_nombre] => Nombres_key_user_test
    [clave_rut] => 17.227.171-5
    [clave_email] => <EMAIL>
    [clave_telefono] => 930219023
    [backup_clave_nombre] => Nombres_key_user_test
    [backup_clave_rut] => 17.227.171-5
    [backup_clave_email] => <EMAIL>
    [backup_clave_telefono] => 930219023
)

[05-Apr-2025 02:14:09 UTC] Archivos recibidos: Array
(
    [archivo_ci] => Array
        (
            [name] => Imagen de WhatsApp 2024-08-27 a las 12.22.36_d7743eea.jpg
            [type] => image/jpeg
            [tmp_name] => /tmp/phpzWTlCx
            [error] => 0
            [size] => 30778
        )

    [archivo_erut] => Array
        (
            [name] => Imagen de WhatsApp 2024-08-15 a las 10.07.48_c1e68a62.jpg
            [type] => image/jpeg
            [tmp_name] => /tmp/phpfdjV44
            [error] => 0
            [size] => 46010
        )

    [archivo_extracto] => Array
        (
            [name] => Imagen de WhatsApp 2024-06-24 a las 12.10.17_8218c8b8.jpg
            [type] => image/jpeg
            [tmp_name] => /tmp/phpKTgpMq
            [error] => 0
            [size] => 152709
        )

    [archivo_ci_frente] => Array
        (
            [name] => Imagen de WhatsApp 2024-08-15 a las 10.07.48_c1e68a62.jpg
            [type] => image/jpeg
            [tmp_name] => /tmp/phpVhHqZj
            [error] => 0
            [size] => 46010
        )

    [archivo_ci_detras] => Array
        (
            [name] => Imagen de WhatsApp 2024-08-27 a las 12.22.36_d7743eea.jpg
            [type] => image/jpeg
            [tmp_name] => /tmp/phpEc1m3y
            [error] => 0
            [size] => 30778
        )

    [archivo_carpeta_tributaria] => Array
        (
            [name] => Imagen de WhatsApp 2024-08-27 a las 12.22.36_7fc71358.jpg
            [type] => image/jpeg
            [tmp_name] => /tmp/phpWITPHh
            [error] => 0
            [size] => 39771
        )

    [archivo_consulta_terceros] => Array
        (
            [name] => Imagen de WhatsApp 2024-08-27 a las 12.22.36_7fc71358.jpg
            [type] => image/jpeg
            [tmp_name] => /tmp/phpVyxhtW
            [error] => 0
            [size] => 39771
        )

)

[05-Apr-2025 02:14:09 UTC] ID de usuario de la sesión: 4
[05-Apr-2025 02:14:09 UTC] Procesando archivo: archivo_ci
[05-Apr-2025 02:14:09 UTC] Archivo 'archivo_ci' guardado en: documentosExperian/ci_4_67f091f146c76.jpg
[05-Apr-2025 02:14:09 UTC] Procesando archivo: archivo_erut
[05-Apr-2025 02:14:09 UTC] Archivo 'archivo_erut' guardado en: documentosExperian/erut_4_67f091f147008.jpg
[05-Apr-2025 02:14:09 UTC] Procesando archivo: archivo_extracto
[05-Apr-2025 02:14:09 UTC] Archivo 'archivo_extracto' guardado en: documentosExperian/extracto_4_67f091f1481a8.jpg
[05-Apr-2025 02:14:09 UTC] Procesando archivo: archivo_ci_frente
[05-Apr-2025 02:14:09 UTC] Archivo 'archivo_ci_frente' guardado en: documentosExperian/cifrente_4_67f091f1486ae.jpg
[05-Apr-2025 02:14:09 UTC] Procesando archivo: archivo_ci_detras
[05-Apr-2025 02:14:09 UTC] Archivo 'archivo_ci_detras' guardado en: documentosExperian/cidetras_4_67f091f148a1d.jpg
[05-Apr-2025 02:14:09 UTC] Procesando archivo: archivo_carpeta_tributaria
[05-Apr-2025 02:14:09 UTC] Archivo 'archivo_carpeta_tributaria' guardado en: documentosExperian/carptrib_4_67f091f148d41.jpg
[05-Apr-2025 02:14:09 UTC] Procesando archivo: archivo_consulta_terceros
[05-Apr-2025 02:14:09 UTC] Archivo 'archivo_consulta_terceros' guardado en: documentosExperian/consterc_4_67f091f149081.jpg
[05-Apr-2025 02:14:09 UTC] Campos a insertar: id_usuario, ruta_ci, ruta_erut, ruta_extracto, ruta_ci_frente, ruta_ci_detras, ruta_carpeta_tributaria, ruta_consulta_terceros, tipo_cliente, rut, razon_social, nombre_representante1, rut_representante1, nombre_representante2, rut_representante2, nombre_representante3, rut_representante3, sistema_creacion, fecha_creacion, notaria, actividad_economica, fecha_constitucion, direccion, comuna, pagina_web, email, telefono, clasificacion_sii, contacto_nombre, contacto_rut, contacto_telefono, contacto_email, contacto_backup_nombre, contacto_backup_rut, contacto_backup_telefono, contacto_backup_email, morosos_plan, morosos_consultas, morosos_uf, morosos_descuento, morosos_nuevo_valor, advanced_plan, advanced_consultas, advanced_uf, advanced_descuento, advanced_nuevo_valor, key_user_nombre, key_user_rut, key_user_email, key_user_telefono, key_user_backup_nombre, key_user_backup_rut, key_user_backup_email, key_user_backup_telefono
[05-Apr-2025 02:14:09 UTC] Tipos de parámetros: isssssssssssssssssssssssssssssssssssssdidssdidssssssss
[05-Apr-2025 02:14:09 UTC] Parámetros: Array
(
    [0] => 4
    [1] => documentosExperian/ci_4_67f091f146c76.jpg
    [2] => documentosExperian/erut_4_67f091f147008.jpg
    [3] => documentosExperian/extracto_4_67f091f1481a8.jpg
    [4] => documentosExperian/cifrente_4_67f091f1486ae.jpg
    [5] => documentosExperian/cidetras_4_67f091f148a1d.jpg
    [6] => documentosExperian/carptrib_4_67f091f148d41.jpg
    [7] => documentosExperian/consterc_4_67f091f149081.jpg
    [8] => Cliente Vigente
    [9] => 20.047.582-8
    [10] => Nico SA
    [11] => Razón Social test
    [12] => 7.518.994-K
    [13] => 
    [14] => 
    [15] => 
    [16] => 
    [17] => Empresa por un día
    [18] => 2025-04-04
    [19] => 12312312
    [20] => 1231231
    [21] => 2025-04-04
    [22] => Catapilco 9171 
    [23] => La Florida
    [24] => www.experian.cl
    [25] => <EMAIL>
    [26] => 930274938
    [27] => Antiguo
    [28] => Nicolas
    [29] => 17.227.171-5
    [30] => 930274939
    [31] => <EMAIL>
    [32] => 
    [33] => 
    [34] => 
    [35] => 
    [36] => S
    [37] => Hasta $8.000.000
    [38] => 3.33
    [39] => 10
    [40] => 3
    [41] => 2000
    [42] => 0.014
    [43] => 28
    [44] => 10
    [45] => 25.2
    [46] => Nombres_key_user_test
    [47] => 17.227.171-5
    [48] => <EMAIL>
    [49] => 930219023
    [50] => Nombres_key_user_test
    [51] => 17.227.171-5
    [52] => <EMAIL>
    [53] => 930219023
)

[05-Apr-2025 02:14:09 UTC] guardar_formulario.php finalizado completamente.
[05-Apr-2025 02:23:55 UTC] Solicitud recibida en get_plan_details.php
[05-Apr-2025 02:23:55 UTC] POST data: Array
(
    [plan] => XS
)

[05-Apr-2025 02:23:55 UTC] Plan seleccionado: XS
[05-Apr-2025 02:23:55 UTC] Datos encontrados - Plan consumo: Hasta $5.000.000, Valor UF: 2.43
[05-Apr-2025 02:26:23 UTC] Solicitud recibida en get_plan_details.php
[05-Apr-2025 02:26:23 UTC] POST data: Array
(
    [plan] => XS
)

[05-Apr-2025 02:26:23 UTC] Plan seleccionado: XS
[05-Apr-2025 02:26:23 UTC] Datos encontrados - Plan consumo: Hasta $5.000.000, Valor UF: 2.43
[05-Apr-2025 02:31:30 UTC] Datos recibidos en guardar_formulario.php: Array
(
    [tipo_cliente] => Cliente Vigente
    [rut] => 20.047.582-8
    [razon_social] => Razón Social test2
    [nombre_representante1] => Razón Social test
    [rut_representante1] => 
    [nombre_representante2] => 
    [rut_representante2] => 
    [nombre_representante3] => 
    [rut_representante3] => 
    [sistema_creacion] => Tradicional
    [fecha_creacion] => 2025-04-04
    [notaria] => 12312312
    [actividad_economica] => 12321321312
    [fecha_constitucion] => 2025-04-04
    [direccion] => Catapilco 9171 
    [comuna] => La Florida
    [pagina_web] => www.experian.cl
    [email] => <EMAIL>
    [telefono] => 930274938
    [clasificacion_sii] => Nuevo
    [contacto_nombre] => Nicolas
    [contacto_rut] => 17.227.171-5
    [contacto_telefono] => 930274939
    [contacto_email] => <EMAIL>
    [contacto_backup_nombre] => 
    [contacto_backup_rut] => 
    [contacto_backup_telefono] => 
    [contacto_backup_email] => 
    [morosos_plan] => XS
    [morosos_consultas] => Hasta $5.000.000
    [morosos_uf] => 2.43
    [morosos_descuento] => 5
    [morosos_nuevo_valor] => 2.31
    [advanced_plan] => 200
    [advanced_consultas] => 0.036
    [advanced_uf] => 7.26
    [advanced_descuento] => 10
    [advanced_nuevo_valor] => 6.53
    [clave_nombre] => Nombres_key_user_test
    [clave_rut] => 17.227.171-5
    [clave_email] => <EMAIL>
    [clave_telefono] => 930219023
    [backup_clave_nombre] => Nombres_key_user_test
    [backup_clave_rut] => 17.227.171-5
    [backup_clave_email] => <EMAIL>
    [backup_clave_telefono] => 930219023
)

[05-Apr-2025 02:31:30 UTC] Archivos recibidos: Array
(
    [archivo_ci] => Array
        (
            [name] => 1.png
            [type] => image/png
            [tmp_name] => /tmp/phpCbNmCV
            [error] => 0
            [size] => 76987
        )

    [archivo_erut] => Array
        (
            [name] => 2.png
            [type] => image/png
            [tmp_name] => /tmp/php1YSWJZ
            [error] => 0
            [size] => 33868
        )

    [archivo_extracto] => Array
        (
            [name] => 1.png
            [type] => image/png
            [tmp_name] => /tmp/phpLK2uFJ
            [error] => 0
            [size] => 76987
        )

    [archivo_ci_frente] => Array
        (
            [name] => 2.png
            [type] => image/png
            [tmp_name] => /tmp/phpca4Bdm
            [error] => 0
            [size] => 33868
        )

    [archivo_ci_detras] => Array
        (
            [name] => 1.png
            [type] => image/png
            [tmp_name] => /tmp/phpZo89Fa
            [error] => 0
            [size] => 76987
        )

    [archivo_carpeta_tributaria] => Array
        (
            [name] => 2.png
            [type] => image/png
            [tmp_name] => /tmp/phpnCdtNT
            [error] => 0
            [size] => 33868
        )

    [archivo_consulta_terceros] => Array
        (
            [name] => 1.png
            [type] => image/png
            [tmp_name] => /tmp/phptyyCa5
            [error] => 0
            [size] => 76987
        )

)

[05-Apr-2025 02:31:30 UTC] ID de usuario de la sesión: 4
[05-Apr-2025 02:31:30 UTC] Procesando archivo: archivo_ci
[05-Apr-2025 02:31:30 UTC] Archivo 'archivo_ci' guardado en: documentosExperian/ci_4_67f09602e834d.png
[05-Apr-2025 02:31:30 UTC] Procesando archivo: archivo_erut
[05-Apr-2025 02:31:30 UTC] Archivo 'archivo_erut' guardado en: documentosExperian/erut_4_67f09602e8675.png
[05-Apr-2025 02:31:30 UTC] Procesando archivo: archivo_extracto
[05-Apr-2025 02:31:30 UTC] Archivo 'archivo_extracto' guardado en: documentosExperian/extracto_4_67f09602e8883.png
[05-Apr-2025 02:31:30 UTC] Procesando archivo: archivo_ci_frente
[05-Apr-2025 02:31:30 UTC] Archivo 'archivo_ci_frente' guardado en: documentosExperian/cifrente_4_67f09602e8b0c.png
[05-Apr-2025 02:31:30 UTC] Procesando archivo: archivo_ci_detras
[05-Apr-2025 02:31:30 UTC] Archivo 'archivo_ci_detras' guardado en: documentosExperian/cidetras_4_67f09602e8e09.png
[05-Apr-2025 02:31:30 UTC] Procesando archivo: archivo_carpeta_tributaria
[05-Apr-2025 02:31:30 UTC] Archivo 'archivo_carpeta_tributaria' guardado en: documentosExperian/carptrib_4_67f09602e9109.png
[05-Apr-2025 02:31:30 UTC] Procesando archivo: archivo_consulta_terceros
[05-Apr-2025 02:31:30 UTC] Archivo 'archivo_consulta_terceros' guardado en: documentosExperian/consterc_4_67f09602e9314.png
[05-Apr-2025 02:31:30 UTC] Campos a insertar: id_usuario, ruta_ci, ruta_erut, ruta_extracto, ruta_ci_frente, ruta_ci_detras, ruta_carpeta_tributaria, ruta_consulta_terceros, tipo_cliente, rut, razon_social, nombre_representante1, rut_representante1, nombre_representante2, rut_representante2, nombre_representante3, rut_representante3, sistema_creacion, fecha_creacion, notaria, actividad_economica, fecha_constitucion, direccion, comuna, pagina_web, email, telefono, clasificacion_sii, contacto_nombre, contacto_rut, contacto_telefono, contacto_email, contacto_backup_nombre, contacto_backup_rut, contacto_backup_telefono, contacto_backup_email, morosos_plan, morosos_consultas, morosos_uf, morosos_descuento, morosos_nuevo_valor, advanced_plan, advanced_consultas, advanced_uf, advanced_descuento, advanced_nuevo_valor, key_user_nombre, key_user_rut, key_user_email, key_user_telefono, key_user_backup_nombre, key_user_backup_rut, key_user_backup_email, key_user_backup_telefono
[05-Apr-2025 02:31:30 UTC] Tipos de parámetros: isssssssssssssssssssssssssssssssssssssdidssdidssssssss
[05-Apr-2025 02:31:30 UTC] Parámetros: Array
(
    [0] => 4
    [1] => documentosExperian/ci_4_67f09602e834d.png
    [2] => documentosExperian/erut_4_67f09602e8675.png
    [3] => documentosExperian/extracto_4_67f09602e8883.png
    [4] => documentosExperian/cifrente_4_67f09602e8b0c.png
    [5] => documentosExperian/cidetras_4_67f09602e8e09.png
    [6] => documentosExperian/carptrib_4_67f09602e9109.png
    [7] => documentosExperian/consterc_4_67f09602e9314.png
    [8] => Cliente Vigente
    [9] => 20.047.582-8
    [10] => Razón Social test2
    [11] => Razón Social test
    [12] => 
    [13] => 
    [14] => 
    [15] => 
    [16] => 
    [17] => Tradicional
    [18] => 2025-04-04
    [19] => 12312312
    [20] => 12321321312
    [21] => 2025-04-04
    [22] => Catapilco 9171 
    [23] => La Florida
    [24] => www.experian.cl
    [25] => <EMAIL>
    [26] => 930274938
    [27] => Nuevo
    [28] => Nicolas
    [29] => 17.227.171-5
    [30] => 930274939
    [31] => <EMAIL>
    [32] => 
    [33] => 
    [34] => 
    [35] => 
    [36] => XS
    [37] => Hasta $5.000.000
    [38] => 2.43
    [39] => 5
    [40] => 2.31
    [41] => 200
    [42] => 0.036
    [43] => 7.26
    [44] => 10
    [45] => 6.53
    [46] => Nombres_key_user_test
    [47] => 17.227.171-5
    [48] => <EMAIL>
    [49] => 930219023
    [50] => Nombres_key_user_test
    [51] => 17.227.171-5
    [52] => <EMAIL>
    [53] => 930219023
)

[05-Apr-2025 02:31:30 UTC] guardar_formulario.php finalizado completamente.
[05-Apr-2025 02:44:23 UTC] Datos recibidos en guardar_formulario.php: Array
(
    [tipo_cliente] => Cliente Vigente
    [rut] => 20.047.582-8
    [razon_social] => Razón Social test2
    [nombre_representante1] => Razón Social test
    [rut_representante1] => 
    [nombre_representante2] => 
    [rut_representante2] => 
    [nombre_representante3] => 
    [rut_representante3] => 
    [sistema_creacion] => Tradicional
    [fecha_creacion] => 2025-04-04
    [notaria] => 12312312
    [actividad_economica] => 12321321312
    [fecha_constitucion] => 2025-04-04
    [direccion] => Catapilco 9171 
    [comuna] => La Florida
    [pagina_web] => www.experian.cl
    [email] => <EMAIL>
    [telefono] => 930274938
    [clasificacion_sii] => Nuevo
    [contacto_nombre] => Nicolas
    [contacto_rut] => 17.227.171-5
    [contacto_telefono] => 930274939
    [contacto_email] => <EMAIL>
    [contacto_backup_nombre] => 
    [contacto_backup_rut] => 
    [contacto_backup_telefono] => 
    [contacto_backup_email] => 
    [morosos_plan] => XS
    [morosos_consultas] => Hasta $5.000.000
    [morosos_uf] => 2.43
    [morosos_descuento] => 5
    [morosos_nuevo_valor] => 2.31
    [advanced_plan] => 200
    [advanced_consultas] => 0.036
    [advanced_uf] => 7.26
    [advanced_descuento] => 10
    [advanced_nuevo_valor] => 6.53
    [clave_nombre] => Nombres_key_user_test
    [clave_rut] => 17.227.171-5
    [clave_email] => <EMAIL>
    [clave_telefono] => 930219023
    [backup_clave_nombre] => Nombres_key_user_test
    [backup_clave_rut] => 17.227.171-5
    [backup_clave_email] => <EMAIL>
    [backup_clave_telefono] => 930219023
)

[05-Apr-2025 02:44:23 UTC] Archivos recibidos: Array
(
    [archivo_ci] => Array
        (
            [name] => 1.png
            [type] => image/png
            [tmp_name] => /tmp/phpMZV19c
            [error] => 0
            [size] => 76987
        )

    [archivo_erut] => Array
        (
            [name] => 2.png
            [type] => image/png
            [tmp_name] => /tmp/phpDp63wG
            [error] => 0
            [size] => 33868
        )

    [archivo_extracto] => Array
        (
            [name] => 1.png
            [type] => image/png
            [tmp_name] => /tmp/phpIvsGf4
            [error] => 0
            [size] => 76987
        )

    [archivo_ci_frente] => Array
        (
            [name] => 2.png
            [type] => image/png
            [tmp_name] => /tmp/phpFNAM7W
            [error] => 0
            [size] => 33868
        )

    [archivo_ci_detras] => Array
        (
            [name] => 1.png
            [type] => image/png
            [tmp_name] => /tmp/php16e1ZP
            [error] => 0
            [size] => 76987
        )

    [archivo_carpeta_tributaria] => Array
        (
            [name] => 2.png
            [type] => image/png
            [tmp_name] => /tmp/phpDgvVDj
            [error] => 0
            [size] => 33868
        )

    [archivo_consulta_terceros] => Array
        (
            [name] => 1.png
            [type] => image/png
            [tmp_name] => /tmp/phpWaiAqF
            [error] => 0
            [size] => 76987
        )

)

[05-Apr-2025 02:44:23 UTC] ID de usuario de la sesión: 4
[05-Apr-2025 02:44:23 UTC] Procesando archivo: archivo_ci
[05-Apr-2025 02:44:23 UTC] Archivo 'archivo_ci' guardado en: documentosExperian/ci_4_67f099077f9f1.png
[05-Apr-2025 02:44:23 UTC] Procesando archivo: archivo_erut
[05-Apr-2025 02:44:23 UTC] Archivo 'archivo_erut' guardado en: documentosExperian/erut_4_67f099077fedb.png
[05-Apr-2025 02:44:23 UTC] Procesando archivo: archivo_extracto
[05-Apr-2025 02:44:23 UTC] Archivo 'archivo_extracto' guardado en: documentosExperian/extracto_4_67f0990780119.png
[05-Apr-2025 02:44:23 UTC] Procesando archivo: archivo_ci_frente
[05-Apr-2025 02:44:23 UTC] Archivo 'archivo_ci_frente' guardado en: documentosExperian/cifrente_4_67f099078039d.png
[05-Apr-2025 02:44:23 UTC] Procesando archivo: archivo_ci_detras
[05-Apr-2025 02:44:23 UTC] Archivo 'archivo_ci_detras' guardado en: documentosExperian/cidetras_4_67f0990780594.png
[05-Apr-2025 02:44:23 UTC] Procesando archivo: archivo_carpeta_tributaria
[05-Apr-2025 02:44:23 UTC] Archivo 'archivo_carpeta_tributaria' guardado en: documentosExperian/carptrib_4_67f0990780828.png
[05-Apr-2025 02:44:23 UTC] Procesando archivo: archivo_consulta_terceros
[05-Apr-2025 02:44:23 UTC] Archivo 'archivo_consulta_terceros' guardado en: documentosExperian/consterc_4_67f0990780b6d.png
[05-Apr-2025 02:44:23 UTC] Campos a insertar: id_usuario, ruta_ci, ruta_erut, ruta_extracto, ruta_ci_frente, ruta_ci_detras, ruta_carpeta_tributaria, ruta_consulta_terceros, tipo_cliente, rut, razon_social, nombre_representante1, rut_representante1, nombre_representante2, rut_representante2, nombre_representante3, rut_representante3, sistema_creacion, fecha_creacion, notaria, actividad_economica, fecha_constitucion, direccion, comuna, pagina_web, email, telefono, clasificacion_sii, contacto_nombre, contacto_rut, contacto_telefono, contacto_email, contacto_backup_nombre, contacto_backup_rut, contacto_backup_telefono, contacto_backup_email, morosos_plan, morosos_consultas, morosos_uf, morosos_descuento, morosos_nuevo_valor, advanced_plan, advanced_consultas, advanced_uf, advanced_descuento, advanced_nuevo_valor, key_user_nombre, key_user_rut, key_user_email, key_user_telefono, key_user_backup_nombre, key_user_backup_rut, key_user_backup_email, key_user_backup_telefono
[05-Apr-2025 02:44:23 UTC] Tipos de parámetros: isssssssssssssssssssssssssssssssssssssdidssdidssssssss
[05-Apr-2025 02:44:23 UTC] Parámetros: Array
(
    [0] => 4
    [1] => documentosExperian/ci_4_67f099077f9f1.png
    [2] => documentosExperian/erut_4_67f099077fedb.png
    [3] => documentosExperian/extracto_4_67f0990780119.png
    [4] => documentosExperian/cifrente_4_67f099078039d.png
    [5] => documentosExperian/cidetras_4_67f0990780594.png
    [6] => documentosExperian/carptrib_4_67f0990780828.png
    [7] => documentosExperian/consterc_4_67f0990780b6d.png
    [8] => Cliente Vigente
    [9] => 20.047.582-8
    [10] => Razón Social test2
    [11] => Razón Social test
    [12] => 
    [13] => 
    [14] => 
    [15] => 
    [16] => 
    [17] => Tradicional
    [18] => 2025-04-04
    [19] => 12312312
    [20] => 12321321312
    [21] => 2025-04-04
    [22] => Catapilco 9171 
    [23] => La Florida
    [24] => www.experian.cl
    [25] => <EMAIL>
    [26] => 930274938
    [27] => Nuevo
    [28] => Nicolas
    [29] => 17.227.171-5
    [30] => 930274939
    [31] => <EMAIL>
    [32] => 
    [33] => 
    [34] => 
    [35] => 
    [36] => XS
    [37] => Hasta $5.000.000
    [38] => 2.43
    [39] => 5
    [40] => 2.31
    [41] => 200
    [42] => 0.036
    [43] => 7.26
    [44] => 10
    [45] => 6.53
    [46] => Nombres_key_user_test
    [47] => 17.227.171-5
    [48] => <EMAIL>
    [49] => 930219023
    [50] => Nombres_key_user_test
    [51] => 17.227.171-5
    [52] => <EMAIL>
    [53] => 930219023
)

[05-Apr-2025 02:54:02 UTC] Solicitud recibida en get_plan_details.php
[05-Apr-2025 02:54:02 UTC] POST data: Array
(
    [plan] => XS
)

[05-Apr-2025 02:54:02 UTC] Plan seleccionado: XS
[05-Apr-2025 02:54:02 UTC] Datos encontrados - Plan consumo: Hasta $5.000.000, Valor UF: 2.43
[05-Apr-2025 02:55:00 UTC] Datos recibidos en guardar_formulario.php: Array
(
    [tipo_cliente] => Cliente Vigente
    [rut] => 20.047.582-8
    [razon_social] => Razón Social test2
    [nombre_representante1] => Razón Social test
    [rut_representante1] => 7.518.994-K
    [nombre_representante2] => Razón Social test
    [rut_representante2] => 
    [nombre_representante3] => 
    [rut_representante3] => 
    [sistema_creacion] => Tradicional
    [fecha_creacion] => 2025-04-04
    [notaria] => 12312312
    [actividad_economica] => 12312321
    [fecha_constitucion] => 2025-04-04
    [direccion] => Catapilco 9171 
    [comuna] => La Florida
    [pagina_web] => www.experian.cl
    [email] => <EMAIL>
    [telefono] => 930274938
    [clasificacion_sii] => Nuevo
    [contacto_nombre] => Nicolas
    [contacto_rut] => 17.227.171-5
    [contacto_telefono] => 930274939
    [contacto_email] => <EMAIL>
    [contacto_backup_nombre] => 
    [contacto_backup_rut] => 
    [contacto_backup_telefono] => 
    [contacto_backup_email] => 
    [morosos_plan] => XS
    [morosos_consultas] => Hasta $5.000.000
    [morosos_uf] => 2.43
    [morosos_descuento] => 5
    [morosos_nuevo_valor] => 2.31
    [advanced_plan] => 100
    [advanced_consultas] => 0.051
    [advanced_uf] => 5.12
    [advanced_descuento] => 15%
    [advanced_nuevo_valor] => 4.35
    [clave_nombre] => Nombres_key_user_test
    [clave_rut] => 17.227.171-5
    [clave_email] => <EMAIL>
    [clave_telefono] => 930219023
    [backup_clave_nombre] => Nombres_key_user_test
    [backup_clave_rut] => 17.227.171-5
    [backup_clave_email] => <EMAIL>
    [backup_clave_telefono] => 930219023
)

[05-Apr-2025 02:55:00 UTC] Archivos recibidos: Array
(
    [archivo_ci] => Array
        (
            [name] => 1.png
            [type] => image/png
            [tmp_name] => /tmp/phpaNE0um
            [error] => 0
            [size] => 76987
        )

    [archivo_erut] => Array
        (
            [name] => 2.png
            [type] => image/png
            [tmp_name] => /tmp/php0O5Oui
            [error] => 0
            [size] => 33868
        )

    [archivo_extracto] => Array
        (
            [name] => 2.png
            [type] => image/png
            [tmp_name] => /tmp/phpc3HfBY
            [error] => 0
            [size] => 33868
        )

    [archivo_ci_frente] => Array
        (
            [name] => 1.png
            [type] => image/png
            [tmp_name] => /tmp/php78Utko
            [error] => 0
            [size] => 76987
        )

    [archivo_ci_detras] => Array
        (
            [name] => 2.png
            [type] => image/png
            [tmp_name] => /tmp/phpa76njP
            [error] => 0
            [size] => 33868
        )

    [archivo_carpeta_tributaria] => Array
        (
            [name] => 1.png
            [type] => image/png
            [tmp_name] => /tmp/phpRVOFgb
            [error] => 0
            [size] => 76987
        )

    [archivo_consulta_terceros] => Array
        (
            [name] => 2.png
            [type] => image/png
            [tmp_name] => /tmp/phphxGQ67
            [error] => 0
            [size] => 33868
        )

)

[05-Apr-2025 02:55:00 UTC] ID de usuario de la sesión: 4
[05-Apr-2025 02:55:00 UTC] Procesando archivo: archivo_ci
[05-Apr-2025 02:55:00 UTC] Archivo 'archivo_ci' guardado en: documentosExperian/ci_4_67f09b8496dcb.png
[05-Apr-2025 02:55:00 UTC] Procesando archivo: archivo_erut
[05-Apr-2025 02:55:00 UTC] Archivo 'archivo_erut' guardado en: documentosExperian/erut_4_67f09b84970fd.png
[05-Apr-2025 02:55:00 UTC] Procesando archivo: archivo_extracto
[05-Apr-2025 02:55:00 UTC] Archivo 'archivo_extracto' guardado en: documentosExperian/extracto_4_67f09b8497371.png
[05-Apr-2025 02:55:00 UTC] Procesando archivo: archivo_ci_frente
[05-Apr-2025 02:55:00 UTC] Archivo 'archivo_ci_frente' guardado en: documentosExperian/cifrente_4_67f09b8497662.png
[05-Apr-2025 02:55:00 UTC] Procesando archivo: archivo_ci_detras
[05-Apr-2025 02:55:00 UTC] Archivo 'archivo_ci_detras' guardado en: documentosExperian/cidetras_4_67f09b8497a25.png
[05-Apr-2025 02:55:00 UTC] Procesando archivo: archivo_carpeta_tributaria
[05-Apr-2025 02:55:00 UTC] Archivo 'archivo_carpeta_tributaria' guardado en: documentosExperian/carptrib_4_67f09b8497cd4.png
[05-Apr-2025 02:55:00 UTC] Procesando archivo: archivo_consulta_terceros
[05-Apr-2025 02:55:00 UTC] Archivo 'archivo_consulta_terceros' guardado en: documentosExperian/consterc_4_67f09b8497f5a.png
[05-Apr-2025 02:55:00 UTC] Campos a insertar: id_usuario, ruta_ci, ruta_erut, ruta_extracto, ruta_ci_frente, ruta_ci_detras, ruta_carpeta_tributaria, ruta_consulta_terceros, tipo_cliente, rut, razon_social, nombre_representante1, rut_representante1, nombre_representante2, rut_representante2, nombre_representante3, rut_representante3, sistema_creacion, fecha_creacion, notaria, actividad_economica, fecha_constitucion, direccion, comuna, pagina_web, email, telefono, clasificacion_sii, contacto_nombre, contacto_rut, contacto_telefono, contacto_email, contacto_backup_nombre, contacto_backup_rut, contacto_backup_telefono, contacto_backup_email, morosos_plan, morosos_consultas, morosos_uf, morosos_descuento, morosos_nuevo_valor, advanced_plan, advanced_consultas, advanced_uf, advanced_descuento, advanced_nuevo_valor, key_user_nombre, key_user_rut, key_user_email, key_user_telefono, key_user_backup_nombre, key_user_backup_rut, key_user_backup_email, key_user_backup_telefono
[05-Apr-2025 02:55:00 UTC] Tipos de parámetros: isssssssssssssssssssssssssssssssssssssdidssdidssssssss
[05-Apr-2025 02:55:00 UTC] Parámetros: Array
(
    [0] => 4
    [1] => documentosExperian/ci_4_67f09b8496dcb.png
    [2] => documentosExperian/erut_4_67f09b84970fd.png
    [3] => documentosExperian/extracto_4_67f09b8497371.png
    [4] => documentosExperian/cifrente_4_67f09b8497662.png
    [5] => documentosExperian/cidetras_4_67f09b8497a25.png
    [6] => documentosExperian/carptrib_4_67f09b8497cd4.png
    [7] => documentosExperian/consterc_4_67f09b8497f5a.png
    [8] => Cliente Vigente
    [9] => 20.047.582-8
    [10] => Razón Social test2
    [11] => Razón Social test
    [12] => 7.518.994-K
    [13] => Razón Social test
    [14] => 
    [15] => 
    [16] => 
    [17] => Tradicional
    [18] => 2025-04-04
    [19] => 12312312
    [20] => 12312321
    [21] => 2025-04-04
    [22] => Catapilco 9171 
    [23] => La Florida
    [24] => www.experian.cl
    [25] => <EMAIL>
    [26] => 930274938
    [27] => Nuevo
    [28] => Nicolas
    [29] => 17.227.171-5
    [30] => 930274939
    [31] => <EMAIL>
    [32] => 
    [33] => 
    [34] => 
    [35] => 
    [36] => XS
    [37] => Hasta $5.000.000
    [38] => 2.43
    [39] => 5
    [40] => 2.31
    [41] => 100
    [42] => 0.051
    [43] => 5.12
    [44] => 15
    [45] => 4.35
    [46] => Nombres_key_user_test
    [47] => 17.227.171-5
    [48] => <EMAIL>
    [49] => 930219023
    [50] => Nombres_key_user_test
    [51] => 17.227.171-5
    [52] => <EMAIL>
    [53] => 930219023
)

[05-Apr-2025 03:49:14 UTC] [2025-04-05 03:49:14] Logout - Usuario: <EMAIL> - IP: ***************
[05-Apr-2025 03:56:28 UTC] PHP Notice:  Undefined variable: pdo in /home/<USER>/public_html/intranet/dist/form_experian.php on line 777
[05-Apr-2025 03:56:28 UTC] PHP Fatal error:  Uncaught Error: Call to a member function prepare() on null in /home/<USER>/public_html/intranet/dist/form_experian.php:777
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/form_experian.php on line 777
[05-Apr-2025 03:56:33 UTC] PHP Notice:  Undefined variable: pdo in /home/<USER>/public_html/intranet/dist/form_experian.php on line 777
[05-Apr-2025 03:56:33 UTC] PHP Fatal error:  Uncaught Error: Call to a member function prepare() on null in /home/<USER>/public_html/intranet/dist/form_experian.php:777
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/form_experian.php on line 777
[05-Apr-2025 03:56:48 UTC] PHP Notice:  Undefined variable: pdo in /home/<USER>/public_html/intranet/dist/form_experian.php on line 777
[05-Apr-2025 03:56:48 UTC] PHP Fatal error:  Uncaught Error: Call to a member function prepare() on null in /home/<USER>/public_html/intranet/dist/form_experian.php:777
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/form_experian.php on line 777
[05-Apr-2025 03:56:51 UTC] PHP Notice:  Undefined variable: pdo in /home/<USER>/public_html/intranet/dist/form_experian.php on line 777
[05-Apr-2025 03:56:51 UTC] PHP Fatal error:  Uncaught Error: Call to a member function prepare() on null in /home/<USER>/public_html/intranet/dist/form_experian.php:777
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/form_experian.php on line 777
[05-Apr-2025 03:58:16 UTC] PHP Notice:  Undefined variable: pdo in /home/<USER>/public_html/intranet/dist/form_experian.php on line 777
[05-Apr-2025 03:58:16 UTC] PHP Fatal error:  Uncaught Error: Call to a member function prepare() on null in /home/<USER>/public_html/intranet/dist/form_experian.php:777
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/form_experian.php on line 777
[05-Apr-2025 04:13:55 UTC] PHP Notice:  Undefined variable: pdo in /home/<USER>/public_html/intranet/dist/form_experian.php on line 777
[05-Apr-2025 04:13:55 UTC] PHP Fatal error:  Uncaught Error: Call to a member function prepare() on null in /home/<USER>/public_html/intranet/dist/form_experian.php:777
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/form_experian.php on line 777
[05-Apr-2025 04:14:00 UTC] PHP Notice:  Undefined variable: pdo in /home/<USER>/public_html/intranet/dist/form_experian.php on line 777
[05-Apr-2025 04:14:00 UTC] PHP Fatal error:  Uncaught Error: Call to a member function prepare() on null in /home/<USER>/public_html/intranet/dist/form_experian.php:777
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/form_experian.php on line 777
[05-Apr-2025 04:17:41 UTC] PHP Notice:  Undefined variable: pdo in /home/<USER>/public_html/intranet/dist/form_experian.php on line 777
[05-Apr-2025 04:17:41 UTC] PHP Fatal error:  Uncaught Error: Call to a member function prepare() on null in /home/<USER>/public_html/intranet/dist/form_experian.php:777
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/form_experian.php on line 777
[05-Apr-2025 04:17:45 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:17:45 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[05-Apr-2025 04:17:52 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:17:52 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[05-Apr-2025 04:21:27 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:21:27 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[05-Apr-2025 04:21:27 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:21:27 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:21:30 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:21:30 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[05-Apr-2025 04:21:30 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:21:30 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:22:00 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:22:00 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[05-Apr-2025 04:22:00 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:22:00 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:29:05 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:29:05 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[05-Apr-2025 04:29:05 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:29:05 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:36:54 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:36:54 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[05-Apr-2025 04:36:54 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:36:54 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:37:03 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:37:03 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[05-Apr-2025 04:37:03 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:37:03 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:37:04 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:37:04 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[05-Apr-2025 04:37:04 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:37:04 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:37:06 UTC] [2025-04-05 04:37:06] Logout - Usuario: <EMAIL> - IP: ***************
[05-Apr-2025 04:37:11 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:37:11 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[05-Apr-2025 04:37:11 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:37:11 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:37:27 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:37:27 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[05-Apr-2025 04:37:27 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:37:27 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:37:33 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:37:33 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[05-Apr-2025 04:37:33 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:37:33 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:38:10 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:38:10 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[05-Apr-2025 04:38:10 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:38:10 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:38:27 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:38:27 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[05-Apr-2025 04:38:27 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:38:27 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:38:56 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:38:56 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[05-Apr-2025 04:38:56 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:38:56 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:41:00 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:41:00 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[05-Apr-2025 04:41:00 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:41:00 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:41:09 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:41:09 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[05-Apr-2025 04:41:09 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:41:09 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:41:16 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:41:16 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[05-Apr-2025 04:41:16 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[05-Apr-2025 04:41:16 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 12:10:58 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 12:10:58 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[07-Apr-2025 12:10:58 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 12:10:58 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 13:29:04 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 13:29:04 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[07-Apr-2025 13:29:04 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 13:29:04 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 13:56:35 UTC] [2025-04-07 13:56:35] Error de autenticación - Usuario no identificado - IP: ***************
[07-Apr-2025 13:56:39 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 13:56:39 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 13:56:39 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[07-Apr-2025 13:56:39 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 13:56:39 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 13:57:40 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 13:57:40 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 13:57:40 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[07-Apr-2025 13:57:40 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 13:57:40 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 13:57:43 UTC] [2025-04-07 13:57:43] Logout - Usuario: <EMAIL> - IP: ***************
[07-Apr-2025 13:57:48 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 13:57:48 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 13:57:48 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[07-Apr-2025 13:57:48 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 13:57:48 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 13:58:02 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 13:58:02 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 13:58:02 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[07-Apr-2025 13:58:02 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 13:58:02 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 14:23:02 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 14:23:02 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 14:23:02 UTC] Error en la consulta de prospectos: No se pudo establecer conexión con la base de datos
[07-Apr-2025 14:23:02 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 14:23:02 UTC] Error creando nueva conexión a la base de datos: SQLSTATE[28000] [1045] Access denied for user 'gestarse_admin'@'localhost' (using password: NO)
[07-Apr-2025 14:23:05 UTC] [2025-04-07 14:23:05] Logout - Usuario: <EMAIL> - IP: ***************
[07-Apr-2025 14:23:09 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[07-Apr-2025 14:23:09 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[07-Apr-2025 14:23:09 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[07-Apr-2025 14:59:23 UTC] [2025-04-07 14:59:23] Error de autenticación - Usuario no identificado - IP: ***************
[07-Apr-2025 14:59:28 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[07-Apr-2025 14:59:28 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[07-Apr-2025 14:59:28 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[07-Apr-2025 15:08:08 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[07-Apr-2025 15:08:08 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[07-Apr-2025 15:08:08 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[07-Apr-2025 15:13:31 UTC] [2025-04-07 15:13:31] Error de autenticación - Usuario no identificado - IP: **************
[07-Apr-2025 15:13:54 UTC] [2025-04-07 15:13:54] Error de autenticación - Usuario no identificado - IP: **************
[07-Apr-2025 15:14:46 UTC] [2025-04-07 15:14:46] Error de autenticación - Usuario no identificado - IP: **************
[07-Apr-2025 15:15:37 UTC] [2025-04-07 15:15:37] Error de autenticación - Usuario no identificado - IP: **************
[07-Apr-2025 15:22:21 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[07-Apr-2025 15:22:21 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[07-Apr-2025 15:22:21 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[07-Apr-2025 15:28:29 UTC] PHP Notice:  Undefined variable: correo in /home/<USER>/public_html/intranet/dist/index.php on line 45
[07-Apr-2025 15:49:34 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[07-Apr-2025 15:49:34 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[07-Apr-2025 15:49:34 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[07-Apr-2025 15:49:37 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[07-Apr-2025 15:49:37 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[07-Apr-2025 15:49:37 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[07-Apr-2025 15:49:38 UTC] [2025-04-07 15:49:38] Logout - Usuario: <EMAIL> - IP: ***************
[07-Apr-2025 15:49:41 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[07-Apr-2025 15:49:41 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[07-Apr-2025 15:49:41 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[07-Apr-2025 15:51:05 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[07-Apr-2025 15:51:05 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[07-Apr-2025 15:51:05 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[07-Apr-2025 20:43:45 UTC] [2025-04-07 20:43:45] Error de autenticación - Usuario no identificado - IP: ***************
[07-Apr-2025 20:43:48 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[07-Apr-2025 20:43:48 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[07-Apr-2025 20:43:48 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:15:03 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:15:03 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 01:15:03 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:16:57 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:16:57 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 01:16:57 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:17:01 UTC] [2025-04-08 01:17:01] Logout - Usuario: <EMAIL> - IP: ***************
[08-Apr-2025 01:17:07 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:17:07 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 01:17:07 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:24:20 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:24:20 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 01:24:20 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:24:22 UTC] [2025-04-08 01:24:22] Logout - Usuario: <EMAIL> - IP: ***************
[08-Apr-2025 01:24:26 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:24:26 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 01:24:26 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:30:05 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:30:05 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 01:30:05 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:30:08 UTC] [2025-04-08 01:30:08] Logout - Usuario: <EMAIL> - IP: ***************
[08-Apr-2025 01:30:11 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:30:11 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 01:30:11 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:34:19 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:34:19 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 01:34:19 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:34:27 UTC] [2025-04-08 01:34:27] Logout - Usuario: <EMAIL> - IP: ***************
[08-Apr-2025 01:34:31 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:34:31 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 01:34:31 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:39:49 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:39:49 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 01:39:49 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:40:00 UTC] [2025-04-08 01:40:00] Error de autenticación - Usuario no identificado - IP: ***************
[08-Apr-2025 01:40:04 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:40:04 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 01:40:04 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:43:13 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:43:13 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 01:43:13 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:43:16 UTC] [2025-04-08 01:43:16] Logout - Usuario: <EMAIL> - IP: ***************
[08-Apr-2025 01:43:25 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:43:25 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 01:43:25 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:43:32 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:43:32 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 01:43:32 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:45:51 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:45:51 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 01:45:51 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:45:53 UTC] [2025-04-08 01:45:53] Logout - Usuario: <EMAIL> - IP: ***************
[08-Apr-2025 01:46:07 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:46:07 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 01:46:07 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:50:49 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:50:49 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 01:50:49 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:50:53 UTC] [2025-04-08 01:50:53] Logout - Usuario: <EMAIL> - IP: ***************
[08-Apr-2025 01:51:10 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 01:51:10 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 01:51:10 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 02:02:32 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 02:02:32 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 02:02:32 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 02:02:59 UTC] [2025-04-08 02:02:59] Logout - Usuario: <EMAIL> - IP: ***************
[08-Apr-2025 02:11:19 UTC] [2025-04-08 02:11:19] Error de autenticación - Usuario no identificado - IP: ***************
[08-Apr-2025 02:11:25 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 02:11:25 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 02:11:25 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 02:11:27 UTC] [2025-04-08 02:11:27] Logout - Usuario: <EMAIL> - IP: ***************
[08-Apr-2025 02:11:31 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[08-Apr-2025 02:11:31 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[08-Apr-2025 02:11:31 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[09-Apr-2025 11:20:46 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[09-Apr-2025 11:20:46 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[09-Apr-2025 11:20:46 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[09-Apr-2025 16:55:31 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[09-Apr-2025 16:55:31 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[09-Apr-2025 16:55:31 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[15-Apr-2025 19:30:18 UTC] [2025-04-15 19:30:18] Error de autenticación - Usuario no identificado - IP: *************
[15-Apr-2025 19:30:34 UTC] [2025-04-15 19:30:34] Error de autenticación - Usuario no identificado - IP: *************
[15-Apr-2025 19:30:38 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[15-Apr-2025 19:30:38 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[15-Apr-2025 19:30:38 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[15-Apr-2025 19:30:44 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[15-Apr-2025 19:30:44 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[15-Apr-2025 19:30:44 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[15-Apr-2025 19:31:10 UTC] Error en la consulta de prospectos: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[15-Apr-2025 19:31:10 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[15-Apr-2025 19:31:10 UTC] Error loading prospects data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.prospectos' doesn't exist
[15-Apr-2025 19:34:23 UTC] [2025-04-15 19:34:23] Logout - Usuario: <EMAIL> - IP: *************
[15-Apr-2025 19:50:33 UTC] Datos recibidos en guardar_formulario.php: Array
(
    [tipo_cliente] => Cliente Vigente
    [rut] => 76.060.259-0
    [razon_social] => SERVICIO DE INGENIERIA SEGURIDAD Y TRANSPORTE TRANSCOM
    [nombre_representante1] => NICOLAS EDUARDO ARAYA TOLEDO
    [rut_representante1] => 16.957.764-1
    [nombre_representante2] => DANIELA DEL PILAR ARAYA TOLEDO
    [rut_representante2] => 16.957.763-3
    [nombre_representante3] => 
    [rut_representante3] => 
    [sistema_creacion] => Tradicional
    [fecha_creacion] => 2009-06-18
    [notaria] => 
    [actividad_economica] => 801001
    [fecha_constitucion] => 2009-06-18
    [direccion] => MANUEL BULNES 45
    [comuna] => TEMUCO
    [pagina_web] => 
    [email] => <EMAIL>
    [telefono] => 957699978
    [clasificacion_sii] => Nuevo
    [contacto_nombre] => NICOLAS EDUARDO ARAYA TOLEDO
    [contacto_rut] => 16.957.764-1
    [contacto_telefono] => 957699978
    [contacto_email] => <EMAIL>
    [contacto_backup_nombre] => DANIELA DEL PILAR ARAYA TOLEDO
    [contacto_backup_rut] => 16.957.763-3
    [contacto_backup_telefono] => 452213699
    [contacto_backup_email] => <EMAIL>
    [morosos_plan] => 
    [morosos_consultas] => 
    [morosos_uf] => 
    [morosos_descuento] => 0
    [morosos_nuevo_valor] => 
    [advanced_plan] => 100
    [advanced_consultas] => 0.051
    [advanced_uf] => 5.12
    [advanced_descuento] => 0
    [advanced_nuevo_valor] => 
    [clave_nombre] => NICOLAS EDUARDO ARAYA TOLEDO
    [clave_rut] => 16.957.764-1
    [clave_email] => <EMAIL>
    [clave_telefono] => 957699978
    [backup_clave_nombre] => DANIELA DEL PILAR ARAYA TOLEDO
    [backup_clave_rut] => 16.957.763-3
    [backup_clave_email] => <EMAIL>
    [backup_clave_telefono] => 452213699
)

[15-Apr-2025 19:50:33 UTC] Archivos recibidos: Array
(
    [archivo_ci] => Array
        (
            [name] => CARNET NICOLAS (1).pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpvbKi42
            [error] => 0
            [size] => 311489
        )

    [archivo_erut] => Array
        (
            [name] => RUT NICOLAS.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpwJ0awj
            [error] => 0
            [size] => 61062
        )

    [archivo_extracto] => Array
        (
            [name] => Escritura (Modificacion Repertorio 8898-2023).pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpFNrGKt
            [error] => 0
            [size] => 5700441
        )

    [archivo_ci_frente] => Array
        (
            [name] => Carnet Representante Legal Daniela Araya.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpG7PSKC
            [error] => 0
            [size] => 234671
        )

    [archivo_ci_detras] => Array
        (
            [name] => RUT DANIELA.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpf27VyR
            [error] => 0
            [size] => 61043
        )

    [archivo_carpeta_tributaria] => Array
        (
            [name] => 11002-23 not_esvidm_Copia Escritura  RECTIFICACION_123456861452.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpItkINr
            [error] => 0
            [size] => 1055057
        )

    [archivo_consulta_terceros] => Array
        (
            [name] => CERTPERSONERIA-2009-146-125-811479-2025-JGL.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpWC0oYs
            [error] => 0
            [size] => 445381
        )

)

[15-Apr-2025 19:50:33 UTC] ID de usuario de la sesión: 1
[15-Apr-2025 19:50:33 UTC] Procesando archivo: archivo_ci
[15-Apr-2025 19:50:33 UTC] Archivo 'archivo_ci' guardado en: documentosExperian/ci_1_67feb889bd002.pdf
[15-Apr-2025 19:50:33 UTC] Procesando archivo: archivo_erut
[15-Apr-2025 19:50:33 UTC] Archivo 'archivo_erut' guardado en: documentosExperian/erut_1_67feb889c0a96.pdf
[15-Apr-2025 19:50:33 UTC] Procesando archivo: archivo_extracto
[15-Apr-2025 19:50:33 UTC] Archivo eliminado por error: documentosExperian/ci_1_67feb889bd002.pdf
[15-Apr-2025 19:50:33 UTC] Archivo eliminado por error: documentosExperian/erut_1_67feb889c0a96.pdf
[15-Apr-2025 19:50:33 UTC] Error en guardar_formulario.php: El archivo 'archivo_extracto' es demasiado grande. Tamaño máximo: 5MB en /home/<USER>/public_html/intranet/dist/guardar_formulario.php:105
[15-Apr-2025 19:51:26 UTC] Datos recibidos en guardar_formulario.php: Array
(
    [tipo_cliente] => Cliente Vigente
    [rut] => 76.060.259-0
    [razon_social] => SERVICIO DE INGENIERIA SEGURIDAD Y TRANSPORTE TRANSCOM
    [nombre_representante1] => NICOLAS EDUARDO ARAYA TOLEDO
    [rut_representante1] => 16.957.764-1
    [nombre_representante2] => DANIELA DEL PILAR ARAYA TOLEDO
    [rut_representante2] => 16.957.763-3
    [nombre_representante3] => 
    [rut_representante3] => 
    [sistema_creacion] => Tradicional
    [fecha_creacion] => 2009-06-18
    [notaria] => 
    [actividad_economica] => 801001
    [fecha_constitucion] => 2009-06-18
    [direccion] => MANUEL BULNES 45
    [comuna] => TEMUCO
    [pagina_web] => 
    [email] => <EMAIL>
    [telefono] => 957699978
    [clasificacion_sii] => Nuevo
    [contacto_nombre] => NICOLAS EDUARDO ARAYA TOLEDO
    [contacto_rut] => 16.957.764-1
    [contacto_telefono] => 957699978
    [contacto_email] => <EMAIL>
    [contacto_backup_nombre] => DANIELA DEL PILAR ARAYA TOLEDO
    [contacto_backup_rut] => 16.957.763-3
    [contacto_backup_telefono] => 452213699
    [contacto_backup_email] => <EMAIL>
    [morosos_plan] => 
    [morosos_consultas] => 
    [morosos_uf] => 
    [morosos_descuento] => 0
    [morosos_nuevo_valor] => 
    [advanced_plan] => 100
    [advanced_consultas] => 0.051
    [advanced_uf] => 5.12
    [advanced_descuento] => 0
    [advanced_nuevo_valor] => 
    [clave_nombre] => NICOLAS EDUARDO ARAYA TOLEDO
    [clave_rut] => 16.957.764-1
    [clave_email] => <EMAIL>
    [clave_telefono] => 957699978
    [backup_clave_nombre] => DANIELA DEL PILAR ARAYA TOLEDO
    [backup_clave_rut] => 16.957.763-3
    [backup_clave_email] => <EMAIL>
    [backup_clave_telefono] => 452213699
)

[15-Apr-2025 19:51:26 UTC] Archivos recibidos: Array
(
    [archivo_ci] => Array
        (
            [name] => CARNET NICOLAS (1).pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpMhuPOF
            [error] => 0
            [size] => 311489
        )

    [archivo_erut] => Array
        (
            [name] => RUT NICOLAS.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpuTRBBj
            [error] => 0
            [size] => 61062
        )

    [archivo_extracto] => Array
        (
            [name] => Escritura (Modificacion Repertorio 8898-2023).pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpPEhK6F
            [error] => 0
            [size] => 5700441
        )

    [archivo_ci_frente] => Array
        (
            [name] => Carnet Representante Legal Daniela Araya.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpJtyvg7
            [error] => 0
            [size] => 234671
        )

    [archivo_ci_detras] => Array
        (
            [name] => RUT DANIELA.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/php46y0x5
            [error] => 0
            [size] => 61043
        )

    [archivo_carpeta_tributaria] => Array
        (
            [name] => 11002-23 not_esvidm_Copia Escritura  RECTIFICACION_123456861452.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpjmhtad
            [error] => 0
            [size] => 1055057
        )

    [archivo_consulta_terceros] => Array
        (
            [name] => CERTPERSONERIA-2009-146-125-811479-2025-JGL.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpI17kh4
            [error] => 0
            [size] => 445381
        )

)

[15-Apr-2025 19:51:26 UTC] ID de usuario de la sesión: 1
[15-Apr-2025 19:51:26 UTC] Procesando archivo: archivo_ci
[15-Apr-2025 19:51:26 UTC] Archivo 'archivo_ci' guardado en: documentosExperian/ci_1_67feb8be8b9d1.pdf
[15-Apr-2025 19:51:26 UTC] Procesando archivo: archivo_erut
[15-Apr-2025 19:51:26 UTC] Archivo 'archivo_erut' guardado en: documentosExperian/erut_1_67feb8be8bfb3.pdf
[15-Apr-2025 19:51:26 UTC] Procesando archivo: archivo_extracto
[15-Apr-2025 19:51:26 UTC] Archivo eliminado por error: documentosExperian/ci_1_67feb8be8b9d1.pdf
[15-Apr-2025 19:51:26 UTC] Archivo eliminado por error: documentosExperian/erut_1_67feb8be8bfb3.pdf
[15-Apr-2025 19:51:26 UTC] Error en guardar_formulario.php: El archivo 'archivo_extracto' es demasiado grande. Tamaño máximo: 5MB en /home/<USER>/public_html/intranet/dist/guardar_formulario.php:105
[15-Apr-2025 19:51:51 UTC] [2025-04-15 19:51:51] Logout - Usuario: <EMAIL> - IP: *************
[15-Apr-2025 19:51:57 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[15-Apr-2025 19:52:00 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[15-Apr-2025 20:12:48 UTC] [2025-04-15 20:12:48] Logout - Usuario: <EMAIL> - IP: *************
[15-Apr-2025 20:12:56 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[15-Apr-2025 20:15:34 UTC] Datos recibidos en guardar_formulario.php: Array
(
    [tipo_cliente] => Cliente Vigente
    [rut] => 76.060.259-0
    [razon_social] => SERVICIO DE INGENIERIA SEGURIDAD Y TRANSPORTE TRANSCOM
    [nombre_representante1] => NICOLAS EDUARDO ARAYA TOLEDO
    [rut_representante1] => 16.957.764-1
    [nombre_representante2] => DANIELA DEL PILAR ARAYA TOLEDO
    [rut_representante2] => 16.957.763-3
    [nombre_representante3] => 
    [rut_representante3] => 
    [sistema_creacion] => Tradicional
    [fecha_creacion] => 2009-06-18
    [notaria] => 
    [actividad_economica] => 801001
    [fecha_constitucion] => 2009-06-18
    [direccion] => MANUEL BULNES 45
    [comuna] => TEMUCO
    [pagina_web] => 
    [email] => <EMAIL>
    [telefono] => 957699978
    [clasificacion_sii] => Nuevo
    [contacto_nombre] => NICOLAS EDUARDO ARAYA TOLEDO
    [contacto_rut] => 16.957.764-1
    [contacto_telefono] => 957699978
    [contacto_email] => <EMAIL>
    [contacto_backup_nombre] => DANIELA DEL PILAR ARAYA TOLEDO
    [contacto_backup_rut] => 16.957.763-3
    [contacto_backup_telefono] => 452213699
    [contacto_backup_email] => <EMAIL>
    [morosos_plan] => 
    [morosos_consultas] => 
    [morosos_uf] => 
    [morosos_descuento] => 0
    [morosos_nuevo_valor] => 
    [advanced_plan] => 100
    [advanced_consultas] => 0.051
    [advanced_uf] => 5.12
    [advanced_descuento] => 0
    [advanced_nuevo_valor] => 
    [clave_nombre] => NICOLAS EDUARDO ARAYA TOLEDO
    [clave_rut] => 16.957.764-1
    [clave_email] => <EMAIL>
    [clave_telefono] => 957699978
    [backup_clave_nombre] => DANIELA DEL PILAR ARAYA TOLEDO
    [backup_clave_rut] => 16.957.763-3
    [backup_clave_email] => <EMAIL>
    [backup_clave_telefono] => 452213699
)

[15-Apr-2025 20:15:34 UTC] Archivos recibidos: Array
(
    [archivo_ci] => Array
        (
            [name] => CARNET NICOLAS (1).pdf
            [type] => application/pdf
            [tmp_name] => /tmp/php3rSSoR
            [error] => 0
            [size] => 311489
        )

    [archivo_erut] => Array
        (
            [name] => RUT NICOLAS.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/php1HSCyz
            [error] => 0
            [size] => 61062
        )

    [archivo_extracto] => Array
        (
            [name] => Escritura (Modificacion Repertorio 8898-2023).pdf
            [type] => application/pdf
            [tmp_name] => /tmp/php4DT7Uy
            [error] => 0
            [size] => 5700441
        )

    [archivo_ci_frente] => Array
        (
            [name] => Carnet Representante Legal Daniela Araya.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpT7UsBf
            [error] => 0
            [size] => 234671
        )

    [archivo_ci_detras] => Array
        (
            [name] => RUT DANIELA.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/php2PsmtP
            [error] => 0
            [size] => 61043
        )

    [archivo_carpeta_tributaria] => Array
        (
            [name] => 11002-23 not_esvidm_Copia Escritura  RECTIFICACION_123456861452.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phprd7LjI
            [error] => 0
            [size] => 1055057
        )

    [archivo_consulta_terceros] => Array
        (
            [name] => CERTPERSONERIA-2009-146-125-811479-2025-JGL.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpdSPuWD
            [error] => 0
            [size] => 445381
        )

)

[15-Apr-2025 20:15:34 UTC] ID de usuario de la sesión: 1
[15-Apr-2025 20:15:34 UTC] Procesando archivo: archivo_ci
[15-Apr-2025 20:15:34 UTC] Archivo 'archivo_ci' guardado en: documentosExperian/ci_1_67febe66a30e9.pdf
[15-Apr-2025 20:15:34 UTC] Procesando archivo: archivo_erut
[15-Apr-2025 20:15:34 UTC] Archivo 'archivo_erut' guardado en: documentosExperian/erut_1_67febe66a367a.pdf
[15-Apr-2025 20:15:34 UTC] Procesando archivo: archivo_extracto
[15-Apr-2025 20:15:34 UTC] Archivo 'archivo_extracto' guardado en: documentosExperian/extracto_1_67febe66a38bf.pdf
[15-Apr-2025 20:15:34 UTC] Procesando archivo: archivo_ci_frente
[15-Apr-2025 20:15:34 UTC] Archivo 'archivo_ci_frente' guardado en: documentosExperian/cifrente_1_67febe66a79ec.pdf
[15-Apr-2025 20:15:34 UTC] Procesando archivo: archivo_ci_detras
[15-Apr-2025 20:15:34 UTC] Archivo 'archivo_ci_detras' guardado en: documentosExperian/cidetras_1_67febe66a7fe3.pdf
[15-Apr-2025 20:15:34 UTC] Procesando archivo: archivo_carpeta_tributaria
[15-Apr-2025 20:15:34 UTC] Archivo 'archivo_carpeta_tributaria' guardado en: documentosExperian/carptrib_1_67febe66a832b.pdf
[15-Apr-2025 20:15:34 UTC] Procesando archivo: archivo_consulta_terceros
[15-Apr-2025 20:15:34 UTC] Archivo 'archivo_consulta_terceros' guardado en: documentosExperian/consterc_1_67febe66a9643.pdf
[15-Apr-2025 20:15:34 UTC] Campos a insertar: id_usuario, ruta_ci, ruta_erut, ruta_extracto, ruta_ci_frente, ruta_ci_detras, ruta_carpeta_tributaria, ruta_consulta_terceros, tipo_cliente, rut, razon_social, nombre_representante1, rut_representante1, nombre_representante2, rut_representante2, nombre_representante3, rut_representante3, sistema_creacion, fecha_creacion, notaria, actividad_economica, fecha_constitucion, direccion, comuna, pagina_web, email, telefono, clasificacion_sii, contacto_nombre, contacto_rut, contacto_telefono, contacto_email, contacto_backup_nombre, contacto_backup_rut, contacto_backup_telefono, contacto_backup_email, morosos_plan, morosos_consultas, morosos_uf, morosos_descuento, morosos_nuevo_valor, advanced_plan, advanced_consultas, advanced_uf, advanced_descuento, advanced_nuevo_valor, key_user_nombre, key_user_rut, key_user_email, key_user_telefono, key_user_backup_nombre, key_user_backup_rut, key_user_backup_email, key_user_backup_telefono
[15-Apr-2025 20:15:34 UTC] Tipos de parámetros: isssssssssssssssssssssssssssssssssssssdidssdidssssssss
[15-Apr-2025 20:15:34 UTC] Parámetros: Array
(
    [0] => 1
    [1] => documentosExperian/ci_1_67febe66a30e9.pdf
    [2] => documentosExperian/erut_1_67febe66a367a.pdf
    [3] => documentosExperian/extracto_1_67febe66a38bf.pdf
    [4] => documentosExperian/cifrente_1_67febe66a79ec.pdf
    [5] => documentosExperian/cidetras_1_67febe66a7fe3.pdf
    [6] => documentosExperian/carptrib_1_67febe66a832b.pdf
    [7] => documentosExperian/consterc_1_67febe66a9643.pdf
    [8] => Cliente Vigente
    [9] => 76.060.259-0
    [10] => SERVICIO DE INGENIERIA SEGURIDAD Y TRANSPORTE TRANSCOM
    [11] => NICOLAS EDUARDO ARAYA TOLEDO
    [12] => 16.957.764-1
    [13] => DANIELA DEL PILAR ARAYA TOLEDO
    [14] => 16.957.763-3
    [15] => 
    [16] => 
    [17] => Tradicional
    [18] => 2009-06-18
    [19] => 
    [20] => 801001
    [21] => 2009-06-18
    [22] => MANUEL BULNES 45
    [23] => TEMUCO
    [24] => 
    [25] => <EMAIL>
    [26] => 957699978
    [27] => Nuevo
    [28] => NICOLAS EDUARDO ARAYA TOLEDO
    [29] => 16.957.764-1
    [30] => 957699978
    [31] => <EMAIL>
    [32] => DANIELA DEL PILAR ARAYA TOLEDO
    [33] => 16.957.763-3
    [34] => 452213699
    [35] => <EMAIL>
    [36] => 
    [37] => 
    [38] => 
    [39] => 
    [40] => 
    [41] => 100
    [42] => 0.051
    [43] => 5.12
    [44] => 
    [45] => 
    [46] => NICOLAS EDUARDO ARAYA TOLEDO
    [47] => 16.957.764-1
    [48] => <EMAIL>
    [49] => 957699978
    [50] => DANIELA DEL PILAR ARAYA TOLEDO
    [51] => 16.957.763-3
    [52] => <EMAIL>
    [53] => 452213699
)

[15-Apr-2025 20:19:28 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[15-Apr-2025 21:45:33 UTC] [2025-04-15 21:45:33] Error de autenticación - Usuario no identificado - IP: *************
[15-Apr-2025 21:45:37 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[15-Apr-2025 22:42:45 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[16-Apr-2025 11:59:07 UTC] [2025-04-16 11:59:07] Error de autenticación - Usuario no identificado - IP: ***************
[16-Apr-2025 13:33:13 UTC] [2025-04-16 13:33:13] Error de autenticación - Usuario no identificado - IP: ***************
[16-Apr-2025 13:33:22 UTC] [2025-04-16 13:33:22] Error de autenticación - Usuario no identificado - IP: ***************
[16-Apr-2025 13:33:26 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[16-Apr-2025 13:34:24 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[16-Apr-2025 13:36:42 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[16-Apr-2025 15:34:44 UTC] Solicitud recibida en get_plan_details.php
[16-Apr-2025 15:34:44 UTC] POST data: Array
(
    [plan] => S
)

[16-Apr-2025 15:34:44 UTC] Plan seleccionado: S
[16-Apr-2025 15:34:44 UTC] Datos encontrados - Plan consumo: Hasta $8.000.000, Valor UF: 3.33
[16-Apr-2025 17:01:41 UTC] Solicitud recibida en get_plan_details.php
[16-Apr-2025 17:01:41 UTC] POST data: Array
(
    [plan] => XS
)

[16-Apr-2025 17:01:41 UTC] Plan seleccionado: XS
[16-Apr-2025 17:01:41 UTC] Datos encontrados - Plan consumo: Hasta $5.000.000, Valor UF: 2.43
[16-Apr-2025 17:28:50 UTC] [2025-04-16 17:28:50] Error de autenticación - Usuario no identificado - IP: ***************
[16-Apr-2025 17:28:56 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[16-Apr-2025 17:29:35 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[16-Apr-2025 17:29:37 UTC] [2025-04-16 17:29:37] Logout - Usuario: <EMAIL> - IP: ***************
[16-Apr-2025 17:29:41 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[21-Apr-2025 21:16:40 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[21-Apr-2025 22:57:18 UTC] [2025-04-21 22:57:18] Error de autenticación - Usuario no identificado - IP: ***************
[21-Apr-2025 22:57:22 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[21-Apr-2025 23:57:56 UTC] [2025-04-21 23:57:56] Error de autenticación - Usuario no identificado - IP: ***************
[21-Apr-2025 23:58:02 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[21-Apr-2025 23:59:43 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[21-Apr-2025 23:59:44 UTC] [2025-04-21 23:59:44] Logout - Usuario: <EMAIL> - IP: ***************
[21-Apr-2025 23:59:48 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[21-Apr-2025 23:59:52 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[21-Apr-2025 23:59:58 UTC] [2025-04-21 23:59:58] Error de autenticación - Usuario no identificado - IP: ***************
[22-Apr-2025 00:00:02 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:00:23 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:00:37 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:03:11 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:03:13 UTC] [2025-04-22 00:03:13] Logout - Usuario: <EMAIL> - IP: ***************
[22-Apr-2025 00:03:18 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:03:24 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:17:29 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:18:28 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:19:01 UTC] [2025-04-22 00:19:01] Error de autenticación - Usuario no identificado - IP: ***************
[22-Apr-2025 00:19:01 UTC] [2025-04-22 00:19:01] Error de autenticación - Usuario no identificado - IP: ***************
[22-Apr-2025 00:19:01 UTC] [2025-04-22 00:19:01] Error de autenticación - Usuario no identificado - IP: ***************
[22-Apr-2025 00:20:01 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:20:01 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:20:02 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:20:02 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:20:09 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:20:10 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:20:10 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:21:48 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:21:48 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:21:48 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:22:21 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:22:21 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:22:21 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:22:24 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:22:24 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:22:24 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:23:48 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:23:48 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:23:48 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:23:51 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:23:51 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:23:51 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:23:58 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:23:58 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:23:58 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:24:39 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:24:39 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:24:39 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:30:21 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:30:21 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:30:21 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:46:49 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:46:49 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:46:49 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:46:50 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:46:50 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:46:50 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:46:56 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:46:56 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 00:46:56 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:00:28 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:00:28 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:00:28 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:00:31 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:00:31 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:00:31 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:05:51 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:05:51 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:05:51 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:17:16 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:17:16 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:17:16 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:17:21 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:17:21 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:17:21 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:17:29 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:17:29 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:17:29 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:17:31 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:17:31 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:17:31 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:33:11 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:33:11 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:33:11 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:33:13 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:33:13 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:33:13 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:33:19 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:33:19 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:33:19 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:38:19 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:38:19 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:38:19 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:38:21 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:38:21 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 01:38:21 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:26:08 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:26:08 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:26:08 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:27:05 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:27:05 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:27:05 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:29:42 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:29:42 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:29:42 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:31:44 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:31:44 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:31:44 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:32:48 UTC] [2025-04-22 02:32:48] Error de autenticación - Usuario no identificado - IP: ***************
[22-Apr-2025 02:32:54 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:32:57 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:33:11 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:34:19 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:34:33 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:34:33 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:34:33 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:39:59 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:41:31 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:44:00 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:46:03 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:48:48 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:53:53 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:55:08 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:56:20 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 02:57:49 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:00:02 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:00:22 UTC] [2025-04-22 03:00:22] Error de autenticación - Usuario no identificado - IP: ***************
[22-Apr-2025 03:00:22 UTC] [2025-04-22 03:00:22] Error de autenticación - Usuario no identificado - IP: ***************
[22-Apr-2025 03:00:22 UTC] [2025-04-22 03:00:22] Error de autenticación - Usuario no identificado - IP: ***************
[22-Apr-2025 03:01:07 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:01:07 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:01:08 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:01:08 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:01:15 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:01:16 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:01:16 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:02:39 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:04:25 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:04:30 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:04:44 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:04:45 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:07:53 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:09:24 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:11:01 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:11:13 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:11:13 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:11:13 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:13:04 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:13:26 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:13:26 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:13:26 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:15:47 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:15:47 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:15:47 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:15:52 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:15:52 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:15:52 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:16:11 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:16:12 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:16:12 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:26:12 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:26:12 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:26:12 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:26:40 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:26:43 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:53:36 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:54:25 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:54:25 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 03:54:25 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:31:07 UTC] [2025-04-22 15:31:07] Error de autenticación - Usuario no identificado - IP: ***************
[22-Apr-2025 15:37:37 UTC] [2025-04-22 15:37:37] Error de autenticación - Usuario no identificado - IP: ***************
[22-Apr-2025 15:37:55 UTC] [2025-04-22 15:37:55] Error de autenticación - Usuario no identificado - IP: ***************
[22-Apr-2025 15:37:59 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:38:08 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:38:10 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:40:24 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:45:24 UTC] [2025-04-22 15:45:24] Error de autenticación - Usuario no identificado - IP: ***************
[22-Apr-2025 15:45:24 UTC] [2025-04-22 15:45:24] Error de autenticación - Usuario no identificado - IP: ***************
[22-Apr-2025 15:45:24 UTC] [2025-04-22 15:45:24] Error de autenticación - Usuario no identificado - IP: ***************
[22-Apr-2025 15:45:55 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:45:55 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:45:55 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:45:56 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:46:03 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:46:03 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:46:03 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:48:11 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:48:11 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:48:11 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:48:38 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:48:38 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:48:38 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:51:08 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:51:09 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:51:09 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:51:10 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:51:10 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:51:10 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:51:13 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:51:13 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:51:13 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:51:16 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:51:16 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:51:16 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:52:28 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:52:28 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:52:28 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:52:29 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:52:29 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:52:29 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:52:31 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:52:31 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 15:52:31 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 16:02:13 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 16:02:13 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 16:02:13 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 16:02:21 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 16:02:21 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 16:02:21 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 16:17:46 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 16:17:46 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 16:17:46 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 16:32:14 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 16:32:14 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 16:32:14 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 16:48:10 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 16:48:10 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 16:48:10 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 17:22:00 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 17:22:00 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[22-Apr-2025 17:22:00 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 00:56:06 UTC] [2025-04-23 00:56:06] Error de autenticación - Usuario no identificado - IP: ***************
[23-Apr-2025 00:56:06 UTC] [2025-04-23 00:56:06] Error de autenticación - Usuario no identificado - IP: ***************
[23-Apr-2025 00:56:06 UTC] [2025-04-23 00:56:06] Error de autenticación - Usuario no identificado - IP: ***************
[23-Apr-2025 00:56:52 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 00:56:52 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 00:56:52 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 00:56:52 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 00:56:57 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 00:56:57 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 00:56:57 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 03:36:01 UTC] [2025-04-23 03:36:01] Error de autenticación - Usuario no identificado - IP: ***************
[23-Apr-2025 03:36:06 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 03:36:57 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 03:48:36 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 03:59:13 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 03:59:20 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 11:49:42 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 11:49:42 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 11:49:42 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 11:49:43 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 11:49:47 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 11:49:48 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 11:49:48 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 11:58:01 UTC] [2025-04-23 11:58:01] Error de autenticación - Usuario no identificado - IP: ************
[23-Apr-2025 11:58:06 UTC] Error loading client data: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gestarse_experian.clientes' doesn't exist
[23-Apr-2025 13:12:46 UTC] [2025-04-23 13:12:46] Logout - Usuario: <EMAIL> - IP: ************
[23-Apr-2025 15:28:03 UTC] [2025-04-23 15:28:03] Error de autenticación - Usuario no identificado - IP: ************
[23-Apr-2025 17:43:09 UTC] [2025-04-23 17:43:09] Error de autenticación - Usuario no identificado - IP: ************
[23-Apr-2025 21:48:46 UTC] [2025-04-23 21:48:46] Error de autenticación - Usuario no identificado - IP: ************
[23-Apr-2025 22:27:52 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1042
[23-Apr-2025 22:27:57 UTC] [2025-04-23 22:27:57] Logout - Usuario: <EMAIL> - IP: ************
[23-Apr-2025 22:28:02 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1042
[23-Apr-2025 22:35:38 UTC] [2025-04-23 22:35:38] Logout - Usuario: <EMAIL> - IP: ************
[23-Apr-2025 23:04:52 UTC] [2025-04-23 23:04:52] Error de autenticación - Usuario no identificado - IP: ************
[23-Apr-2025 23:12:55 UTC] [2025-04-23 23:12:55] Logout - Usuario: <EMAIL> - IP: ************
[23-Apr-2025 23:12:59 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1059
[23-Apr-2025 23:12:59 UTC] form_experian2.php - Usuario ID: 4 - Datos: {"id":"4","correo":"<EMAIL>","nombre_usuario":"Oriana Camilo","rol":"admin"}
[23-Apr-2025 23:12:59 UTC] form_experian2.php - Usuario Oriana Camilo (ID: 4) es administrador
[23-Apr-2025 23:12:59 UTC] form_experian2.php - Consulta SQL para administrador: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC
[23-Apr-2025 23:12:59 UTC] form_experian2.php - Se encontraron 79 prospectos para el usuario ID: 4
[23-Apr-2025 23:24:10 UTC] [2025-04-23 23:24:10] Logout - Usuario: <EMAIL> - IP: ************
[23-Apr-2025 23:24:14 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[23-Apr-2025 23:24:14 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[23-Apr-2025 23:24:14 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[23-Apr-2025 23:24:14 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[23-Apr-2025 23:24:14 UTC] form_experian2.php - Se encontraron 19 prospectos para el usuario ID: 1
[23-Apr-2025 23:38:03 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[23-Apr-2025 23:38:03 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[23-Apr-2025 23:38:03 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[23-Apr-2025 23:38:03 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[23-Apr-2025 23:38:03 UTC] form_experian2.php - Se encontraron 19 prospectos para el usuario ID: 1
[23-Apr-2025 23:38:06 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[23-Apr-2025 23:38:06 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[23-Apr-2025 23:38:06 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[23-Apr-2025 23:38:06 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[23-Apr-2025 23:38:06 UTC] form_experian2.php - Se encontraron 19 prospectos para el usuario ID: 1
[23-Apr-2025 23:38:14 UTC] [2025-04-23 23:38:14] Logout - Usuario: <EMAIL> - IP: ************
[23-Apr-2025 23:38:17 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[23-Apr-2025 23:38:17 UTC] form_experian2.php - Usuario ID: 4 - Datos: {"id":"4","correo":"<EMAIL>","nombre_usuario":"Oriana Camilo","rol":"admin"}
[23-Apr-2025 23:38:17 UTC] form_experian2.php - Usuario Oriana Camilo (ID: 4) es administrador
[23-Apr-2025 23:38:17 UTC] form_experian2.php - Consulta SQL para administrador: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC
[23-Apr-2025 23:38:17 UTC] form_experian2.php - Se encontraron 79 prospectos para el usuario ID: 4
[24-Apr-2025 00:10:05 UTC] [2025-04-24 00:10:05] Logout - Usuario: <EMAIL> - IP: ************
[24-Apr-2025 00:10:10 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 00:10:10 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 00:10:10 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 00:10:10 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 00:10:10 UTC] form_experian2.php - Se encontraron 19 prospectos para el usuario ID: 1
[24-Apr-2025 00:10:44 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 00:10:44 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 00:10:44 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 00:10:44 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 00:10:44 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[24-Apr-2025 00:15:32 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 00:15:32 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 00:15:32 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 00:15:32 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 00:15:32 UTC] form_experian2.php - Se encontraron 19 prospectos para el usuario ID: 1
[24-Apr-2025 00:29:46 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 00:29:46 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 00:29:46 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 00:29:46 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 00:29:46 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[24-Apr-2025 00:30:30 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 00:30:30 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 00:30:30 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 00:30:30 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 00:30:30 UTC] form_experian2.php - Se encontraron 19 prospectos para el usuario ID: 1
[24-Apr-2025 00:30:33 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 00:30:33 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 00:30:33 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 00:30:33 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 00:30:33 UTC] form_experian2.php - Se encontraron 19 prospectos para el usuario ID: 1
[24-Apr-2025 00:31:15 UTC] [2025-04-24 00:31:15] Logout - Usuario: <EMAIL> - IP: ************
[24-Apr-2025 00:31:20 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 00:31:20 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 00:31:20 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 00:31:20 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 00:31:20 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[24-Apr-2025 00:31:30 UTC] [2025-04-24 00:31:30] Logout - Usuario: <EMAIL> - IP: ************
[24-Apr-2025 00:31:39 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 00:31:39 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 00:31:39 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 00:31:39 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 00:31:39 UTC] form_experian2.php - Se encontraron 19 prospectos para el usuario ID: 1
[24-Apr-2025 00:39:21 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 00:39:21 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 00:39:21 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 00:39:21 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 00:39:21 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[24-Apr-2025 00:39:25 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 00:39:25 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 00:39:25 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 00:39:25 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 00:39:25 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[24-Apr-2025 00:39:41 UTC] [2025-04-24 00:39:41] Logout - Usuario: <EMAIL> - IP: ************
[24-Apr-2025 00:39:45 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 00:39:45 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 00:39:45 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 00:39:45 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 00:39:45 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[24-Apr-2025 00:40:03 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 00:40:03 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 00:40:03 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 00:40:03 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 00:40:03 UTC] form_experian2.php - Se encontraron 19 prospectos para el usuario ID: 1
[24-Apr-2025 00:58:47 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 00:58:47 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 00:58:47 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 00:58:47 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 00:58:47 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[24-Apr-2025 00:59:01 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 00:59:01 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 00:59:01 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 00:59:01 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 00:59:01 UTC] form_experian2.php - Se encontraron 19 prospectos para el usuario ID: 1
[24-Apr-2025 00:59:04 UTC] [2025-04-24 00:59:04] Logout - Usuario: <EMAIL> - IP: ************
[24-Apr-2025 00:59:08 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 00:59:08 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 00:59:08 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 00:59:08 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 00:59:08 UTC] form_experian2.php - Se encontraron 19 prospectos para el usuario ID: 1
[24-Apr-2025 01:05:57 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 01:05:57 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 01:05:57 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 01:05:57 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 01:05:57 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[24-Apr-2025 01:10:46 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 01:10:46 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 01:10:46 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 01:10:46 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 01:10:46 UTC] form_experian2.php - Se encontraron 19 prospectos para el usuario ID: 1
[24-Apr-2025 01:15:02 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 01:15:02 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 01:15:02 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 01:15:02 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 01:15:02 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[24-Apr-2025 01:17:29 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 01:17:29 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 01:17:29 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 01:17:29 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 01:17:29 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[24-Apr-2025 01:18:19 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 01:18:19 UTC] form_experian2.php - Usuario ID: 4 - Datos: {"id":"4","correo":"<EMAIL>","nombre_usuario":"Oriana Camilo","rol":"admin"}
[24-Apr-2025 01:18:19 UTC] form_experian2.php - Usuario Oriana Camilo (ID: 4) es administrador
[24-Apr-2025 01:18:19 UTC] form_experian2.php - Consulta SQL para administrador: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC
[24-Apr-2025 01:18:19 UTC] form_experian2.php - Se encontraron 80 prospectos para el usuario ID: 4
[24-Apr-2025 01:18:41 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 01:18:41 UTC] form_experian2.php - Usuario ID: 4 - Datos: {"id":"4","correo":"<EMAIL>","nombre_usuario":"Oriana Camilo","rol":"admin"}
[24-Apr-2025 01:18:41 UTC] form_experian2.php - Usuario Oriana Camilo (ID: 4) es administrador
[24-Apr-2025 01:18:41 UTC] form_experian2.php - Consulta SQL para administrador: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC
[24-Apr-2025 01:18:41 UTC] form_experian2.php - Se encontraron 80 prospectos para el usuario ID: 4
[24-Apr-2025 01:18:48 UTC] [2025-04-24 01:18:48] Logout - Usuario: <EMAIL> - IP: ************
[24-Apr-2025 01:18:52 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 01:18:52 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 01:18:52 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 01:18:52 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 01:18:52 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[24-Apr-2025 01:18:55 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 01:18:55 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 01:18:55 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 01:18:55 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 01:18:55 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[24-Apr-2025 01:21:20 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 01:21:20 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 01:21:20 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 01:21:20 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 01:21:20 UTC] form_experian2.php - Se encontraron 21 prospectos para el usuario ID: 1
[24-Apr-2025 01:21:28 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 01:21:28 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 01:21:28 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 01:21:28 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 01:21:28 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[24-Apr-2025 01:23:56 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 01:23:56 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 01:23:56 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 01:23:56 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 01:23:56 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[24-Apr-2025 01:23:58 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 01:23:58 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 01:23:58 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 01:23:58 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 01:23:58 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[24-Apr-2025 01:24:22 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[24-Apr-2025 01:24:22 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[24-Apr-2025 01:24:22 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[24-Apr-2025 01:24:22 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[24-Apr-2025 01:24:22 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[05-May-2025 14:34:18 UTC] [2025-05-05 14:34:18] Error de autenticación - Usuario no identificado - IP: ************1
[05-May-2025 14:35:01 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[05-May-2025 14:35:01 UTC] form_experian2.php - Usuario ID: 4 - Datos: {"id":"4","correo":"<EMAIL>","nombre_usuario":"Oriana Camilo","rol":"admin"}
[05-May-2025 14:35:01 UTC] form_experian2.php - Usuario Oriana Camilo (ID: 4) es administrador
[05-May-2025 14:35:01 UTC] form_experian2.php - Consulta SQL para administrador: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC
[05-May-2025 14:35:01 UTC] form_experian2.php - Se encontraron 79 prospectos para el usuario ID: 4
[16-May-2025 19:32:16 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[16-May-2025 19:32:16 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[16-May-2025 19:32:16 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[16-May-2025 19:32:16 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[16-May-2025 19:32:16 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[19-May-2025 20:20:43 UTC] [2025-05-19 20:20:43] Error de autenticación - Usuario no identificado - IP: ***************
[19-May-2025 20:20:48 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[19-May-2025 20:20:48 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[19-May-2025 20:20:48 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[19-May-2025 20:20:48 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[19-May-2025 20:20:48 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[19-May-2025 20:25:23 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[19-May-2025 20:25:23 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[19-May-2025 20:25:23 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[19-May-2025 20:25:23 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[19-May-2025 20:25:23 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[19-May-2025 20:25:33 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[19-May-2025 20:25:33 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[19-May-2025 20:25:33 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[19-May-2025 20:25:33 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[19-May-2025 20:25:33 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[19-May-2025 20:31:07 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[19-May-2025 20:31:07 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[19-May-2025 20:31:07 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[19-May-2025 20:31:07 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[19-May-2025 20:31:07 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[19-May-2025 20:38:07 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1058
[19-May-2025 20:38:07 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[19-May-2025 20:38:07 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[19-May-2025 20:38:07 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[19-May-2025 20:38:07 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[19-May-2025 21:02:54 UTC] [2025-05-19 21:02:54] Error de autenticación - Usuario no identificado - IP: ***************
[19-May-2025 21:02:59 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1091
[19-May-2025 21:02:59 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[19-May-2025 21:02:59 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[19-May-2025 21:02:59 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[19-May-2025 21:02:59 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[19-May-2025 21:11:38 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1091
[19-May-2025 21:11:38 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[19-May-2025 21:11:38 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[19-May-2025 21:11:38 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[19-May-2025 21:11:38 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[19-May-2025 21:47:26 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1091
[19-May-2025 21:47:26 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[19-May-2025 21:47:26 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[19-May-2025 21:47:26 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[19-May-2025 21:47:26 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[19-May-2025 21:47:32 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1091
[19-May-2025 21:47:32 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[19-May-2025 21:47:32 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[19-May-2025 21:47:32 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[19-May-2025 21:47:32 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[19-May-2025 21:58:35 UTC] Datos recibidos en guardar_formulario.php: Array
(
    [tipo_cliente] => Cliente Vigente
    [rut] => 76.653.915-7
    [razon_social] => RORRO SPA
    [nombre_representante1] => RODRIGO JOSE ALEGRIA SANTANDER
    [rut_representante1] => 13.286.480-2
    [nombre_representante2] => 
    [rut_representante2] => 
    [nombre_representante3] => 
    [rut_representante3] => 
    [sistema_creacion] => Empresa por un día
    [fecha_creacion] => 2016-08-25
    [notaria] => 
    [actividad_economica] => 392300
    [fecha_constitucion] => 2016-08-25
    [direccion] => BARROS BORGOÑO 71, OFICINA 1105
    [comuna] => PROVIDENCIA
    [pagina_web] => 
    [email] => <EMAIL>
    [telefono] => 942872819
    [clasificacion_sii] => Antiguo
    [contacto_nombre] => RODRIGO JOSE ALEGRIA SANTANDER
    [contacto_rut] => 13.286.480-2
    [contacto_telefono] => 942872819
    [contacto_email] => <EMAIL>
    [contacto_backup_nombre] => RODRIGO JOSE ALEGRIA SANTANDER
    [contacto_backup_rut] => 13.286.480-2
    [contacto_backup_telefono] => 942872819
    [contacto_backup_email] => <EMAIL>
    [morosos_plan] => 
    [morosos_consultas] => 
    [morosos_uf] => 
    [morosos_descuento] => 0
    [morosos_nuevo_valor] => 
    [advanced_plan] => 50
    [advanced_consultas] => 
    [advanced_uf] => 
    [advanced_descuento] => 0
    [advanced_nuevo_valor] => 
    [clave_nombre] => RODRIGO JOSE ALEGRIA SANTANDER
    [clave_rut] => 13.286.480-2
    [clave_email] => <EMAIL>
    [clave_telefono] => 942872819
    [backup_clave_nombre] => RODRIGO JOSE ALEGRIA SANTANDER
    [backup_clave_rut] => 13.286.480-2
    [backup_clave_email] => <EMAIL>
    [backup_clave_telefono] => 942872819
)

[19-May-2025 21:58:35 UTC] Archivos recibidos: Array
(
    [archivo_ci] => Array
        (
            [name] => CEDULA RODRIGO ALEGRIA.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpUiB0UX
            [error] => 0
            [size] => 324884
        )

    [archivo_erut] => Array
        (
            [name] => CEDULA E-RUT.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpqjq9gM
            [error] => 0
            [size] => 61048
        )

    [archivo_extracto] => Array
        (
            [name] => CERTIFICADO VIGENCIA RORRO SPA.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpZXkH8Y
            [error] => 0
            [size] => 543121
        )

    [archivo_ci_frente] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_ci_detras] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_carpeta_tributaria] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_consulta_terceros] => Array
        (
            [name] => ESTATUTO RORRO SPA.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpKtcBkf
            [error] => 0
            [size] => 690418
        )

)

[19-May-2025 21:58:35 UTC] ID de usuario de la sesión: 1
[19-May-2025 21:58:35 UTC] Procesando archivo: archivo_ci
[19-May-2025 21:58:35 UTC] Archivo 'archivo_ci' guardado en: documentosExperian/ci_1_682ba98b597b7.pdf
[19-May-2025 21:58:35 UTC] Procesando archivo: archivo_erut
[19-May-2025 21:58:35 UTC] Archivo 'archivo_erut' guardado en: documentosExperian/erut_1_682ba98b59c4d.pdf
[19-May-2025 21:58:35 UTC] Procesando archivo: archivo_extracto
[19-May-2025 21:58:35 UTC] Archivo 'archivo_extracto' guardado en: documentosExperian/extracto_1_682ba98b59e1a.pdf
[19-May-2025 21:58:35 UTC] No se recibió archivo para: archivo_ci_frente
[19-May-2025 21:58:35 UTC] No se recibió archivo para: archivo_ci_detras
[19-May-2025 21:58:35 UTC] No se recibió archivo para: archivo_carpeta_tributaria
[19-May-2025 21:58:35 UTC] Procesando archivo: archivo_consulta_terceros
[19-May-2025 21:58:35 UTC] Archivo 'archivo_consulta_terceros' guardado en: documentosExperian/consterc_1_682ba98b5a471.pdf
[19-May-2025 21:58:35 UTC] Campos a insertar: id_usuario, ruta_ci, ruta_erut, ruta_extracto, ruta_consulta_terceros, tipo_cliente, rut, razon_social, nombre_representante1, rut_representante1, nombre_representante2, rut_representante2, nombre_representante3, rut_representante3, sistema_creacion, fecha_creacion, notaria, actividad_economica, fecha_constitucion, direccion, comuna, pagina_web, email, telefono, clasificacion_sii, contacto_nombre, contacto_rut, contacto_telefono, contacto_email, contacto_backup_nombre, contacto_backup_rut, contacto_backup_telefono, contacto_backup_email, morosos_plan, morosos_consultas, morosos_uf, morosos_descuento, morosos_nuevo_valor, advanced_plan, advanced_consultas, advanced_uf, advanced_descuento, advanced_nuevo_valor, key_user_nombre, key_user_rut, key_user_email, key_user_telefono, key_user_backup_nombre, key_user_backup_rut, key_user_backup_email, key_user_backup_telefono
[19-May-2025 21:58:35 UTC] Tipos de parámetros: issssssssssssssssssssssssssssssssssdidssdidssssssss
[19-May-2025 21:58:35 UTC] Parámetros: Array
(
    [0] => 1
    [1] => documentosExperian/ci_1_682ba98b597b7.pdf
    [2] => documentosExperian/erut_1_682ba98b59c4d.pdf
    [3] => documentosExperian/extracto_1_682ba98b59e1a.pdf
    [4] => documentosExperian/consterc_1_682ba98b5a471.pdf
    [5] => Cliente Vigente
    [6] => 76.653.915-7
    [7] => RORRO SPA
    [8] => RODRIGO JOSE ALEGRIA SANTANDER
    [9] => 13.286.480-2
    [10] => 
    [11] => 
    [12] => 
    [13] => 
    [14] => Empresa por un día
    [15] => 2016-08-25
    [16] => 
    [17] => 392300
    [18] => 2016-08-25
    [19] => BARROS BORGOÑO 71, OFICINA 1105
    [20] => PROVIDENCIA
    [21] => 
    [22] => <EMAIL>
    [23] => 942872819
    [24] => Antiguo
    [25] => RODRIGO JOSE ALEGRIA SANTANDER
    [26] => 13.286.480-2
    [27] => 942872819
    [28] => <EMAIL>
    [29] => RODRIGO JOSE ALEGRIA SANTANDER
    [30] => 13.286.480-2
    [31] => 942872819
    [32] => <EMAIL>
    [33] => 
    [34] => 
    [35] => 
    [36] => 
    [37] => 
    [38] => 50
    [39] => 
    [40] => 
    [41] => 
    [42] => 
    [43] => RODRIGO JOSE ALEGRIA SANTANDER
    [44] => 13.286.480-2
    [45] => <EMAIL>
    [46] => 942872819
    [47] => RODRIGO JOSE ALEGRIA SANTANDER
    [48] => 13.286.480-2
    [49] => <EMAIL>
    [50] => 942872819
)

[19-May-2025 21:58:35 UTC] Datos recibidos en guardar_formulario.php: Array
(
    [tipo_cliente] => Cliente Vigente
    [rut] => 76.653.915-7
    [razon_social] => RORRO SPA
    [nombre_representante1] => RODRIGO JOSE ALEGRIA SANTANDER
    [rut_representante1] => 13.286.480-2
    [nombre_representante2] => 
    [rut_representante2] => 
    [nombre_representante3] => 
    [rut_representante3] => 
    [sistema_creacion] => Empresa por un día
    [fecha_creacion] => 2016-08-25
    [notaria] => 
    [actividad_economica] => 392300
    [fecha_constitucion] => 2016-08-25
    [direccion] => BARROS BORGOÑO 71, OFICINA 1105
    [comuna] => PROVIDENCIA
    [pagina_web] => 
    [email] => <EMAIL>
    [telefono] => 942872819
    [clasificacion_sii] => Antiguo
    [contacto_nombre] => RODRIGO JOSE ALEGRIA SANTANDER
    [contacto_rut] => 13.286.480-2
    [contacto_telefono] => 942872819
    [contacto_email] => <EMAIL>
    [contacto_backup_nombre] => RODRIGO JOSE ALEGRIA SANTANDER
    [contacto_backup_rut] => 13.286.480-2
    [contacto_backup_telefono] => 942872819
    [contacto_backup_email] => <EMAIL>
    [morosos_plan] => 
    [morosos_consultas] => 
    [morosos_uf] => 
    [morosos_descuento] => 0
    [morosos_nuevo_valor] => 
    [advanced_plan] => 50
    [advanced_consultas] => 
    [advanced_uf] => 
    [advanced_descuento] => 0
    [advanced_nuevo_valor] => 
    [clave_nombre] => RODRIGO JOSE ALEGRIA SANTANDER
    [clave_rut] => 13.286.480-2
    [clave_email] => <EMAIL>
    [clave_telefono] => 942872819
    [backup_clave_nombre] => RODRIGO JOSE ALEGRIA SANTANDER
    [backup_clave_rut] => 13.286.480-2
    [backup_clave_email] => <EMAIL>
    [backup_clave_telefono] => 942872819
)

[19-May-2025 21:58:35 UTC] Archivos recibidos: Array
(
    [archivo_ci] => Array
        (
            [name] => CEDULA RODRIGO ALEGRIA.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpD4TIiC
            [error] => 0
            [size] => 324884
        )

    [archivo_erut] => Array
        (
            [name] => CEDULA E-RUT.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/php0uuY68
            [error] => 0
            [size] => 61048
        )

    [archivo_extracto] => Array
        (
            [name] => CERTIFICADO VIGENCIA RORRO SPA.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpg9ilbc
            [error] => 0
            [size] => 543121
        )

    [archivo_ci_frente] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_ci_detras] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_carpeta_tributaria] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_consulta_terceros] => Array
        (
            [name] => ESTATUTO RORRO SPA.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpHyuwVN
            [error] => 0
            [size] => 690418
        )

)

[19-May-2025 21:58:35 UTC] ID de usuario de la sesión: 1
[19-May-2025 21:58:35 UTC] Procesando archivo: archivo_ci
[19-May-2025 21:58:35 UTC] Archivo 'archivo_ci' guardado en: documentosExperian/ci_1_682ba98b6079f.pdf
[19-May-2025 21:58:35 UTC] Procesando archivo: archivo_erut
[19-May-2025 21:58:35 UTC] Archivo 'archivo_erut' guardado en: documentosExperian/erut_1_682ba98b60df3.pdf
[19-May-2025 21:58:35 UTC] Procesando archivo: archivo_extracto
[19-May-2025 21:58:35 UTC] Archivo 'archivo_extracto' guardado en: documentosExperian/extracto_1_682ba98b61026.pdf
[19-May-2025 21:58:35 UTC] No se recibió archivo para: archivo_ci_frente
[19-May-2025 21:58:35 UTC] No se recibió archivo para: archivo_ci_detras
[19-May-2025 21:58:35 UTC] No se recibió archivo para: archivo_carpeta_tributaria
[19-May-2025 21:58:35 UTC] Procesando archivo: archivo_consulta_terceros
[19-May-2025 21:58:35 UTC] Archivo 'archivo_consulta_terceros' guardado en: documentosExperian/consterc_1_682ba98b6180d.pdf
[19-May-2025 21:58:35 UTC] Campos a insertar: id_usuario, ruta_ci, ruta_erut, ruta_extracto, ruta_consulta_terceros, tipo_cliente, rut, razon_social, nombre_representante1, rut_representante1, nombre_representante2, rut_representante2, nombre_representante3, rut_representante3, sistema_creacion, fecha_creacion, notaria, actividad_economica, fecha_constitucion, direccion, comuna, pagina_web, email, telefono, clasificacion_sii, contacto_nombre, contacto_rut, contacto_telefono, contacto_email, contacto_backup_nombre, contacto_backup_rut, contacto_backup_telefono, contacto_backup_email, morosos_plan, morosos_consultas, morosos_uf, morosos_descuento, morosos_nuevo_valor, advanced_plan, advanced_consultas, advanced_uf, advanced_descuento, advanced_nuevo_valor, key_user_nombre, key_user_rut, key_user_email, key_user_telefono, key_user_backup_nombre, key_user_backup_rut, key_user_backup_email, key_user_backup_telefono
[19-May-2025 21:58:35 UTC] Tipos de parámetros: issssssssssssssssssssssssssssssssssdidssdidssssssss
[19-May-2025 21:58:35 UTC] Parámetros: Array
(
    [0] => 1
    [1] => documentosExperian/ci_1_682ba98b6079f.pdf
    [2] => documentosExperian/erut_1_682ba98b60df3.pdf
    [3] => documentosExperian/extracto_1_682ba98b61026.pdf
    [4] => documentosExperian/consterc_1_682ba98b6180d.pdf
    [5] => Cliente Vigente
    [6] => 76.653.915-7
    [7] => RORRO SPA
    [8] => RODRIGO JOSE ALEGRIA SANTANDER
    [9] => 13.286.480-2
    [10] => 
    [11] => 
    [12] => 
    [13] => 
    [14] => Empresa por un día
    [15] => 2016-08-25
    [16] => 
    [17] => 392300
    [18] => 2016-08-25
    [19] => BARROS BORGOÑO 71, OFICINA 1105
    [20] => PROVIDENCIA
    [21] => 
    [22] => <EMAIL>
    [23] => 942872819
    [24] => Antiguo
    [25] => RODRIGO JOSE ALEGRIA SANTANDER
    [26] => 13.286.480-2
    [27] => 942872819
    [28] => <EMAIL>
    [29] => RODRIGO JOSE ALEGRIA SANTANDER
    [30] => 13.286.480-2
    [31] => 942872819
    [32] => <EMAIL>
    [33] => 
    [34] => 
    [35] => 
    [36] => 
    [37] => 
    [38] => 50
    [39] => 
    [40] => 
    [41] => 
    [42] => 
    [43] => RODRIGO JOSE ALEGRIA SANTANDER
    [44] => 13.286.480-2
    [45] => <EMAIL>
    [46] => 942872819
    [47] => RODRIGO JOSE ALEGRIA SANTANDER
    [48] => 13.286.480-2
    [49] => <EMAIL>
    [50] => 942872819
)

[19-May-2025 21:59:25 UTC] Datos recibidos en guardar_formulario.php: Array
(
    [tipo_cliente] => Cliente Vigente
    [rut] => 76.653.915-7
    [razon_social] => RORRO SPA
    [nombre_representante1] => RODRIGO JOSE ALEGRIA SANTANDER
    [rut_representante1] => 13.286.480-2
    [nombre_representante2] => 
    [rut_representante2] => 
    [nombre_representante3] => 
    [rut_representante3] => 
    [sistema_creacion] => Empresa por un día
    [fecha_creacion] => 2016-08-25
    [notaria] => 
    [actividad_economica] => 392300
    [fecha_constitucion] => 2016-08-25
    [direccion] => BARROS BORGOÑO 71, OFICINA 1105
    [comuna] => PROVIDENCIA
    [pagina_web] => 
    [email] => <EMAIL>
    [telefono] => 942872819
    [clasificacion_sii] => Antiguo
    [contacto_nombre] => RODRIGO JOSE ALEGRIA SANTANDER
    [contacto_rut] => 13.286.480-2
    [contacto_telefono] => 942872819
    [contacto_email] => <EMAIL>
    [contacto_backup_nombre] => RODRIGO JOSE ALEGRIA SANTANDER
    [contacto_backup_rut] => 13.286.480-2
    [contacto_backup_telefono] => 942872819
    [contacto_backup_email] => <EMAIL>
    [morosos_plan] => 
    [morosos_consultas] => 
    [morosos_uf] => 
    [morosos_descuento] => 0
    [morosos_nuevo_valor] => 
    [advanced_plan] => 50
    [advanced_consultas] => 
    [advanced_uf] => 
    [advanced_descuento] => 0
    [advanced_nuevo_valor] => 
    [clave_nombre] => RODRIGO JOSE ALEGRIA SANTANDER
    [clave_rut] => 13.286.480-2
    [clave_email] => <EMAIL>
    [clave_telefono] => 942872819
    [backup_clave_nombre] => RODRIGO JOSE ALEGRIA SANTANDER
    [backup_clave_rut] => 13.286.480-2
    [backup_clave_email] => <EMAIL>
    [backup_clave_telefono] => 942872819
)

[19-May-2025 21:59:25 UTC] Archivos recibidos: Array
(
    [archivo_ci] => Array
        (
            [name] => CEDULA RODRIGO ALEGRIA.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpoGMIi8
            [error] => 0
            [size] => 324884
        )

    [archivo_erut] => Array
        (
            [name] => CEDULA E-RUT.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/php3WZxkj
            [error] => 0
            [size] => 61048
        )

    [archivo_extracto] => Array
        (
            [name] => CERTIFICADO VIGENCIA RORRO SPA.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpmeOOlI
            [error] => 0
            [size] => 543121
        )

    [archivo_ci_frente] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_ci_detras] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_carpeta_tributaria] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_consulta_terceros] => Array
        (
            [name] => ESTATUTO RORRO SPA.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpIce9cy
            [error] => 0
            [size] => 690418
        )

)

[19-May-2025 21:59:25 UTC] ID de usuario de la sesión: 1
[19-May-2025 21:59:25 UTC] Procesando archivo: archivo_ci
[19-May-2025 21:59:25 UTC] Archivo 'archivo_ci' guardado en: documentosExperian/ci_1_682ba9bd8384a.pdf
[19-May-2025 21:59:25 UTC] Procesando archivo: archivo_erut
[19-May-2025 21:59:25 UTC] Archivo 'archivo_erut' guardado en: documentosExperian/erut_1_682ba9bd83f24.pdf
[19-May-2025 21:59:25 UTC] Procesando archivo: archivo_extracto
[19-May-2025 21:59:25 UTC] Archivo 'archivo_extracto' guardado en: documentosExperian/extracto_1_682ba9bd84248.pdf
[19-May-2025 21:59:25 UTC] No se recibió archivo para: archivo_ci_frente
[19-May-2025 21:59:25 UTC] No se recibió archivo para: archivo_ci_detras
[19-May-2025 21:59:25 UTC] No se recibió archivo para: archivo_carpeta_tributaria
[19-May-2025 21:59:25 UTC] Procesando archivo: archivo_consulta_terceros
[19-May-2025 21:59:25 UTC] Archivo 'archivo_consulta_terceros' guardado en: documentosExperian/consterc_1_682ba9bd84bb8.pdf
[19-May-2025 21:59:25 UTC] Campos a insertar: id_usuario, ruta_ci, ruta_erut, ruta_extracto, ruta_consulta_terceros, tipo_cliente, rut, razon_social, nombre_representante1, rut_representante1, nombre_representante2, rut_representante2, nombre_representante3, rut_representante3, sistema_creacion, fecha_creacion, notaria, actividad_economica, fecha_constitucion, direccion, comuna, pagina_web, email, telefono, clasificacion_sii, contacto_nombre, contacto_rut, contacto_telefono, contacto_email, contacto_backup_nombre, contacto_backup_rut, contacto_backup_telefono, contacto_backup_email, morosos_plan, morosos_consultas, morosos_uf, morosos_descuento, morosos_nuevo_valor, advanced_plan, advanced_consultas, advanced_uf, advanced_descuento, advanced_nuevo_valor, key_user_nombre, key_user_rut, key_user_email, key_user_telefono, key_user_backup_nombre, key_user_backup_rut, key_user_backup_email, key_user_backup_telefono
[19-May-2025 21:59:25 UTC] Tipos de parámetros: issssssssssssssssssssssssssssssssssdidssdidssssssss
[19-May-2025 21:59:25 UTC] Parámetros: Array
(
    [0] => 1
    [1] => documentosExperian/ci_1_682ba9bd8384a.pdf
    [2] => documentosExperian/erut_1_682ba9bd83f24.pdf
    [3] => documentosExperian/extracto_1_682ba9bd84248.pdf
    [4] => documentosExperian/consterc_1_682ba9bd84bb8.pdf
    [5] => Cliente Vigente
    [6] => 76.653.915-7
    [7] => RORRO SPA
    [8] => RODRIGO JOSE ALEGRIA SANTANDER
    [9] => 13.286.480-2
    [10] => 
    [11] => 
    [12] => 
    [13] => 
    [14] => Empresa por un día
    [15] => 2016-08-25
    [16] => 
    [17] => 392300
    [18] => 2016-08-25
    [19] => BARROS BORGOÑO 71, OFICINA 1105
    [20] => PROVIDENCIA
    [21] => 
    [22] => <EMAIL>
    [23] => 942872819
    [24] => Antiguo
    [25] => RODRIGO JOSE ALEGRIA SANTANDER
    [26] => 13.286.480-2
    [27] => 942872819
    [28] => <EMAIL>
    [29] => RODRIGO JOSE ALEGRIA SANTANDER
    [30] => 13.286.480-2
    [31] => 942872819
    [32] => <EMAIL>
    [33] => 
    [34] => 
    [35] => 
    [36] => 
    [37] => 
    [38] => 50
    [39] => 
    [40] => 
    [41] => 
    [42] => 
    [43] => RODRIGO JOSE ALEGRIA SANTANDER
    [44] => 13.286.480-2
    [45] => <EMAIL>
    [46] => 942872819
    [47] => RODRIGO JOSE ALEGRIA SANTANDER
    [48] => 13.286.480-2
    [49] => <EMAIL>
    [50] => 942872819
)

[19-May-2025 23:09:59 UTC] [2025-05-19 23:09:59] Error de autenticación - Usuario no identificado - IP: ***************
[19-May-2025 23:10:04 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1091
[19-May-2025 23:10:04 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[19-May-2025 23:10:04 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[19-May-2025 23:10:04 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[19-May-2025 23:10:04 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[19-May-2025 23:20:08 UTC] PHP Fatal error:  Cannot redeclare getDBConnection() (previously declared in /home/<USER>/public_html/intranet/dist/form_experian2.php:39) in /home/<USER>/public_html/intranet/dist/con_db.php on line 135
[19-May-2025 23:25:52 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1186
[19-May-2025 23:25:52 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[19-May-2025 23:25:52 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[19-May-2025 23:25:52 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[19-May-2025 23:25:52 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[19-May-2025 23:28:37 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1186
[19-May-2025 23:28:37 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[19-May-2025 23:28:37 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[19-May-2025 23:28:37 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[19-May-2025 23:28:37 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[19-May-2025 23:32:14 UTC] PHP Notice:  Undefined variable: es_admin in /home/<USER>/public_html/intranet/dist/form_experian2.php on line 1186
[19-May-2025 23:32:14 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[19-May-2025 23:32:14 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[19-May-2025 23:32:14 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[19-May-2025 23:32:14 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[19-May-2025 23:39:47 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[19-May-2025 23:39:47 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[19-May-2025 23:39:47 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[19-May-2025 23:39:47 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[19-May-2025 23:47:32 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[19-May-2025 23:47:32 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[19-May-2025 23:47:32 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[19-May-2025 23:47:32 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[20-May-2025 00:09:19 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[20-May-2025 00:09:19 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[20-May-2025 00:09:19 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[20-May-2025 00:09:19 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[11-Jun-2025 17:55:55 UTC] [2025-06-11 17:55:55] Error de autenticación - Usuario no identificado - IP: *************
[11-Jun-2025 18:00:09 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[11-Jun-2025 18:00:09 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[11-Jun-2025 18:00:09 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[11-Jun-2025 18:00:09 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[11-Jun-2025 18:05:19 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[11-Jun-2025 18:05:19 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[11-Jun-2025 18:05:19 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[11-Jun-2025 18:05:19 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[11-Jun-2025 18:05:34 UTC] [2025-06-11 18:05:34] Error de autenticación - Usuario no identificado - IP: *************
[11-Jun-2025 18:05:48 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[11-Jun-2025 18:05:48 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[11-Jun-2025 18:05:48 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[11-Jun-2025 18:05:48 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[11-Jun-2025 18:11:01 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[11-Jun-2025 18:11:01 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[11-Jun-2025 18:11:01 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[11-Jun-2025 18:11:01 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[11-Jun-2025 18:11:21 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[11-Jun-2025 18:11:21 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[11-Jun-2025 18:11:21 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[11-Jun-2025 18:11:21 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[11-Jun-2025 18:11:29 UTC] [2025-06-11 18:11:29] Logout - Usuario: <EMAIL> - IP: *************
[11-Jun-2025 18:11:42 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[11-Jun-2025 18:11:42 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[11-Jun-2025 18:11:42 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[11-Jun-2025 18:11:42 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[11-Jun-2025 19:14:34 UTC] [2025-06-11 19:14:34] Error de autenticación - Usuario no identificado - IP: *************
[11-Jun-2025 19:14:48 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[11-Jun-2025 19:14:48 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[11-Jun-2025 19:14:48 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[11-Jun-2025 19:14:48 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[11-Jun-2025 19:30:19 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[11-Jun-2025 19:30:19 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[11-Jun-2025 19:30:19 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[11-Jun-2025 19:30:19 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[11-Jun-2025 19:31:01 UTC] [2025-06-11 19:31:01] Logout - Usuario: <EMAIL> - IP: *************
[11-Jun-2025 19:31:10 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[11-Jun-2025 19:31:10 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[11-Jun-2025 19:31:10 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[11-Jun-2025 19:31:10 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[11-Jun-2025 19:36:52 UTC] [2025-06-11 19:36:52] Logout - Usuario: <EMAIL> - IP: *************
[11-Jun-2025 19:37:03 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[11-Jun-2025 19:37:03 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[11-Jun-2025 19:37:03 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[11-Jun-2025 19:37:03 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[11-Jun-2025 19:46:49 UTC] [2025-06-11 19:46:49] Logout - Usuario: <EMAIL> - IP: *************
[11-Jun-2025 19:46:59 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[11-Jun-2025 19:46:59 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[11-Jun-2025 19:46:59 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[11-Jun-2025 19:46:59 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[11-Jun-2025 19:47:10 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[11-Jun-2025 19:47:10 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[11-Jun-2025 19:47:10 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[11-Jun-2025 19:47:10 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[11-Jun-2025 20:36:55 UTC] [2025-06-11 20:36:55] Error de autenticación - Usuario no identificado - IP: *************
[11-Jun-2025 20:37:11 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[11-Jun-2025 20:37:11 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[11-Jun-2025 20:37:11 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[11-Jun-2025 20:37:11 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[11-Jun-2025 20:37:25 UTC] [2025-06-11 20:37:25] Logout - Usuario: <EMAIL> - IP: *************
[11-Jun-2025 20:37:30 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[11-Jun-2025 20:37:30 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[11-Jun-2025 20:37:30 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[11-Jun-2025 20:37:30 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[11-Jun-2025 20:42:04 UTC] [2025-06-11 20:42:04] Error de autenticación - Usuario no identificado - IP: *************
[11-Jun-2025 20:42:16 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[11-Jun-2025 20:42:16 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[11-Jun-2025 20:42:16 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[11-Jun-2025 20:42:16 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[11-Jun-2025 21:03:19 UTC] PHP Notice:  Undefined variable: correo in /home/<USER>/public_html/intranet/dist/index.php on line 45
[11-Jun-2025 21:05:45 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[11-Jun-2025 21:05:45 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[11-Jun-2025 21:05:45 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[11-Jun-2025 21:05:45 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[11-Jun-2025 21:24:33 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[11-Jun-2025 21:24:33 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[11-Jun-2025 21:24:33 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[11-Jun-2025 21:24:33 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[11-Jun-2025 22:14:07 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[11-Jun-2025 22:14:07 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[11-Jun-2025 22:14:07 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[11-Jun-2025 22:14:07 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[11-Jun-2025 22:14:40 UTC] Datos recibidos en guardar_formulario.php: Array
(
    [tipo_cliente] => Cliente No vigente
    [rut] => 99.442.388-6
    [razon_social] => Industrias Metalúrgicas del Pacífico S.A.
    [nombre_representante1] => Ana Patricia Fernández Torres
    [rut_representante1] => 47.175.966-K
    [nombre_representante2] => 
    [rut_representante2] => 
    [nombre_representante3] => 
    [rut_representante3] => 
    [sistema_creacion] => Tradicional
    [fecha_creacion] => 2023-11-26
    [notaria] => Notaría Segunda de Las Condes
    [actividad_economica] => 435239
    [fecha_constitucion] => 2012-11-01
    [direccion] => Av. Principal 568, Viña del Mar
    [comuna] => Viña del Mar
    [pagina_web] => www.industriasmetalrgicasdelpacficosa.cl
    [email] => <EMAIL>
    [telefono] => 973797734
    [clasificacion_sii] => Antiguo
    [contacto_nombre] => Roberto José Hernández Castro
    [contacto_rut] => 57.046.191-5
    [contacto_telefono] => 954539630
    [contacto_email] => <EMAIL>
    [contacto_backup_nombre] => Luis Fernando García Rojas
    [contacto_backup_rut] => 85.934.896-3
    [contacto_backup_telefono] => 967788587
    [contacto_backup_email] => <EMAIL>
    [morosos_plan] => XL
    [morosos_consultas] => 
    [morosos_uf] => 
    [morosos_descuento] => 10
    [morosos_nuevo_valor] => 
    [advanced_plan] => 5000
    [advanced_consultas] => 
    [advanced_uf] => 
    [advanced_nuevo_valor] => 
    [clave_nombre] => Carmen Gloria Sánchez Morales
    [clave_rut] => 47.396.141-5
    [clave_email] => <EMAIL>
    [clave_telefono] => 982334358
    [backup_clave_nombre] => Patricia Isabel Vargas Mendoza
    [backup_clave_rut] => 87.603.470-0
    [backup_clave_email] => <EMAIL>
    [backup_clave_telefono] => 955157755
)

[11-Jun-2025 22:14:40 UTC] Archivos recibidos: Array
(
    [archivo_ci] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_erut] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_extracto] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_ci_frente] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_ci_detras] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_carpeta_tributaria] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_consulta_terceros] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

)

[11-Jun-2025 22:14:40 UTC] ID de usuario de la sesión: 1
[11-Jun-2025 22:14:40 UTC] No se recibió archivo para: archivo_ci
[11-Jun-2025 22:14:40 UTC] No se recibió archivo para: archivo_erut
[11-Jun-2025 22:14:40 UTC] No se recibió archivo para: archivo_extracto
[11-Jun-2025 22:14:40 UTC] No se recibió archivo para: archivo_ci_frente
[11-Jun-2025 22:14:40 UTC] No se recibió archivo para: archivo_ci_detras
[11-Jun-2025 22:14:40 UTC] No se recibió archivo para: archivo_carpeta_tributaria
[11-Jun-2025 22:14:40 UTC] No se recibió archivo para: archivo_consulta_terceros
[11-Jun-2025 22:14:40 UTC] Campos a insertar: id_usuario, tipo_cliente, rut, razon_social, nombre_representante1, rut_representante1, nombre_representante2, rut_representante2, nombre_representante3, rut_representante3, sistema_creacion, fecha_creacion, notaria, actividad_economica, fecha_constitucion, direccion, comuna, pagina_web, email, telefono, clasificacion_sii, contacto_nombre, contacto_rut, contacto_telefono, contacto_email, contacto_backup_nombre, contacto_backup_rut, contacto_backup_telefono, contacto_backup_email, morosos_plan, morosos_consultas, morosos_uf, morosos_descuento, morosos_nuevo_valor, advanced_plan, advanced_consultas, advanced_uf, advanced_nuevo_valor, key_user_nombre, key_user_rut, key_user_email, key_user_telefono, key_user_backup_nombre, key_user_backup_rut, key_user_backup_email, key_user_backup_telefono
[11-Jun-2025 22:14:40 UTC] Tipos de parámetros: issssssssssssssssssssssssssssssdidssddssssssss
[11-Jun-2025 22:14:40 UTC] Parámetros: Array
(
    [0] => 1
    [1] => Cliente No vigente
    [2] => 99.442.388-6
    [3] => Industrias Metalúrgicas del Pacífico S.A.
    [4] => Ana Patricia Fernández Torres
    [5] => 47.175.966-K
    [6] => 
    [7] => 
    [8] => 
    [9] => 
    [10] => Tradicional
    [11] => 2023-11-26
    [12] => Notaría Segunda de Las Condes
    [13] => 435239
    [14] => 2012-11-01
    [15] => Av. Principal 568, Viña del Mar
    [16] => Viña del Mar
    [17] => www.industriasmetalrgicasdelpacficosa.cl
    [18] => <EMAIL>
    [19] => 973797734
    [20] => Antiguo
    [21] => Roberto José Hernández Castro
    [22] => 57.046.191-5
    [23] => 954539630
    [24] => <EMAIL>
    [25] => Luis Fernando García Rojas
    [26] => 85.934.896-3
    [27] => 967788587
    [28] => <EMAIL>
    [29] => XL
    [30] => 
    [31] => 
    [32] => 10
    [33] => 
    [34] => 5000
    [35] => 
    [36] => 
    [37] => 
    [38] => Carmen Gloria Sánchez Morales
    [39] => 47.396.141-5
    [40] => <EMAIL>
    [41] => 982334358
    [42] => Patricia Isabel Vargas Mendoza
    [43] => 87.603.470-0
    [44] => <EMAIL>
    [45] => 955157755
)

[11-Jun-2025 22:51:28 UTC] form_experian2.php - Usuario ID: 1 - Datos: {"id":"1","correo":"<EMAIL>","nombre_usuario":"Marcel Betancourt","rol":"ejecutivos"}
[11-Jun-2025 22:51:28 UTC] form_experian2.php - Usuario Marcel Betancourt (ID: 1) NO es administrador
[11-Jun-2025 22:51:28 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 1)
[11-Jun-2025 22:51:28 UTC] form_experian2.php - Se encontraron 20 prospectos para el usuario ID: 1
[11-Jun-2025 23:28:46 UTC] Datos recibidos en guardar_formulario.php: Array
(
    [tipo_cliente] => Cliente Vigente
    [rut] => 77.055.454-3
    [razon_social] => INGENIERIA Y MONTAJES METALMIG SPA
    [nombre_representante1] => BENITO FUENTES FUENTES 
    [rut_representante1] => 12.081.268-8
    [nombre_representante2] => 
    [rut_representante2] => 
    [nombre_representante3] => 
    [rut_representante3] => 
    [sistema_creacion] => Empresa por un día
    [fecha_creacion] => 2019-08-14
    [notaria] => 
    [actividad_economica] => 251100
    [fecha_constitucion] => 2019-08-14
    [direccion] => AVENIDA SENADOR J GUZMAN 3145
    [comuna] => RENCA
    [pagina_web] => 
    [email] => <EMAIL>
    [telefono] => 976025824
    [clasificacion_sii] => Antiguo
    [contacto_nombre] => BENITO FUENTES FUENTES
    [contacto_rut] => 12.081.268-8
    [contacto_telefono] => 976025824
    [contacto_email] => <EMAIL>
    [contacto_backup_nombre] => MARCEL BETANCOURT
    [contacto_backup_rut] => 17.354.657-2
    [contacto_backup_telefono] => 989269451
    [contacto_backup_email] => <EMAIL>
    [morosos_plan] => XL
    [morosos_consultas] => 
    [morosos_uf] => 
    [morosos_descuento] => 0
    [morosos_nuevo_valor] => 
    [advanced_plan] => 
    [advanced_consultas] => 
    [advanced_uf] => 
    [advanced_descuento] => 0
    [advanced_nuevo_valor] => 
    [clave_nombre] => BENITO FUENTES FUENTES 
    [clave_rut] => 12.081.268-8
    [clave_email] => <EMAIL>
    [clave_telefono] => 976025824
    [backup_clave_nombre] => BENITO FUENTES FUENTES 
    [backup_clave_rut] => 12.081.268-8
    [backup_clave_email] => <EMAIL>
    [backup_clave_telefono] => 976025824
)

[11-Jun-2025 23:28:46 UTC] Archivos recibidos: Array
(
    [archivo_ci] => Array
        (
            [name] => Carnet.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpsb4ceV
            [error] => 0
            [size] => 950835
        )

    [archivo_erut] => Array
        (
            [name] => Rut Benito.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpZUqcCF
            [error] => 0
            [size] => 59747
        )

    [archivo_extracto] => Array
        (
            [name] => Estatuto Metalmig.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/php1bZu7K
            [error] => 0
            [size] => 760448
        )

    [archivo_ci_frente] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_ci_detras] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_carpeta_tributaria] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_consulta_terceros] => Array
        (
            [name] => Metalmig.Inscripción.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpUoQZRr
            [error] => 0
            [size] => 231427
        )

)

[11-Jun-2025 23:28:46 UTC] ID de usuario de la sesión: 1
[11-Jun-2025 23:28:46 UTC] Procesando archivo: archivo_ci
[11-Jun-2025 23:28:46 UTC] Archivo 'archivo_ci' guardado en: documentosExperian/ci_1_684a112e9e699.pdf
[11-Jun-2025 23:28:46 UTC] Procesando archivo: archivo_erut
[11-Jun-2025 23:28:46 UTC] Archivo 'archivo_erut' guardado en: documentosExperian/erut_1_684a112e9f40e.pdf
[11-Jun-2025 23:28:46 UTC] Procesando archivo: archivo_extracto
[11-Jun-2025 23:28:46 UTC] Archivo 'archivo_extracto' guardado en: documentosExperian/extracto_1_684a112e9f687.pdf
[11-Jun-2025 23:28:46 UTC] No se recibió archivo para: archivo_ci_frente
[11-Jun-2025 23:28:46 UTC] No se recibió archivo para: archivo_ci_detras
[11-Jun-2025 23:28:46 UTC] No se recibió archivo para: archivo_carpeta_tributaria
[11-Jun-2025 23:28:46 UTC] Procesando archivo: archivo_consulta_terceros
[11-Jun-2025 23:28:46 UTC] Archivo 'archivo_consulta_terceros' guardado en: documentosExperian/consterc_1_684a112ea0241.pdf
[11-Jun-2025 23:28:46 UTC] Campos a insertar: id_usuario, ruta_ci, ruta_erut, ruta_extracto, ruta_consulta_terceros, tipo_cliente, rut, razon_social, nombre_representante1, rut_representante1, nombre_representante2, rut_representante2, nombre_representante3, rut_representante3, sistema_creacion, fecha_creacion, notaria, actividad_economica, fecha_constitucion, direccion, comuna, pagina_web, email, telefono, clasificacion_sii, contacto_nombre, contacto_rut, contacto_telefono, contacto_email, contacto_backup_nombre, contacto_backup_rut, contacto_backup_telefono, contacto_backup_email, morosos_plan, morosos_consultas, morosos_uf, morosos_descuento, morosos_nuevo_valor, advanced_plan, advanced_consultas, advanced_uf, advanced_descuento, advanced_nuevo_valor, key_user_nombre, key_user_rut, key_user_email, key_user_telefono, key_user_backup_nombre, key_user_backup_rut, key_user_backup_email, key_user_backup_telefono
[11-Jun-2025 23:28:46 UTC] Tipos de parámetros: issssssssssssssssssssssssssssssssssdidssdidssssssss
[11-Jun-2025 23:28:46 UTC] Parámetros: Array
(
    [0] => 1
    [1] => documentosExperian/ci_1_684a112e9e699.pdf
    [2] => documentosExperian/erut_1_684a112e9f40e.pdf
    [3] => documentosExperian/extracto_1_684a112e9f687.pdf
    [4] => documentosExperian/consterc_1_684a112ea0241.pdf
    [5] => Cliente Vigente
    [6] => 77.055.454-3
    [7] => INGENIERIA Y MONTAJES METALMIG SPA
    [8] => BENITO FUENTES FUENTES 
    [9] => 12.081.268-8
    [10] => 
    [11] => 
    [12] => 
    [13] => 
    [14] => Empresa por un día
    [15] => 2019-08-14
    [16] => 
    [17] => 251100
    [18] => 2019-08-14
    [19] => AVENIDA SENADOR J GUZMAN 3145
    [20] => RENCA
    [21] => 
    [22] => <EMAIL>
    [23] => 976025824
    [24] => Antiguo
    [25] => BENITO FUENTES FUENTES
    [26] => 12.081.268-8
    [27] => 976025824
    [28] => <EMAIL>
    [29] => MARCEL BETANCOURT
    [30] => 17.354.657-2
    [31] => 989269451
    [32] => <EMAIL>
    [33] => XL
    [34] => 
    [35] => 
    [36] => 
    [37] => 
    [38] => 
    [39] => 
    [40] => 
    [41] => 
    [42] => 
    [43] => BENITO FUENTES FUENTES 
    [44] => 12.081.268-8
    [45] => <EMAIL>
    [46] => 976025824
    [47] => BENITO FUENTES FUENTES 
    [48] => 12.081.268-8
    [49] => <EMAIL>
    [50] => 976025824
)

[11-Jun-2025 23:38:52 UTC] Datos recibidos en guardar_formulario.php: Array
(
    [tipo_cliente] => Cliente Vigente
    [rut] => 77.131.259-4
    [razon_social] => VEGAFRESH SPA
    [nombre_representante1] => ANA MARIA LEPIN GONZALEZ
    [rut_representante1] => 13.290.856-7
    [nombre_representante2] => 
    [rut_representante2] => 
    [nombre_representante3] => 
    [rut_representante3] => 
    [sistema_creacion] => Tradicional
    [fecha_creacion] => 2020-03-09
    [notaria] => 
    [actividad_economica] => 463011
    [fecha_constitucion] => 2020-03-09
    [direccion] => MANCO CAPAC 1560
    [comuna] => INDEPENDENCIA
    [pagina_web] => 
    [email] => <EMAIL>
    [telefono] => 961420560
    [clasificacion_sii] => Nuevo
    [contacto_nombre] => ANA MARIA LEPIN GONZALEZ
    [contacto_rut] => 13.290.856-7
    [contacto_telefono] => 961420560
    [contacto_email] => <EMAIL>
    [contacto_backup_nombre] => CAMILA FERNANDA ABARZUA LEPIN
    [contacto_backup_rut] => 20.288.551-9
    [contacto_backup_telefono] => 983802784
    [contacto_backup_email] => <EMAIL>
    [morosos_plan] => L
    [morosos_consultas] => 
    [morosos_uf] => 
    [morosos_descuento] => 0
    [morosos_nuevo_valor] => 
    [advanced_plan] => 
    [advanced_consultas] => 
    [advanced_uf] => 
    [advanced_descuento] => 0
    [advanced_nuevo_valor] => 
    [clave_nombre] => ANA MARIA LEPIN GONZALEZ
    [clave_rut] => 13.290.856-7
    [clave_email] => <EMAIL>
    [clave_telefono] => 961420560
    [backup_clave_nombre] => CAMILA FERNANDA ABARZUA LEPIN
    [backup_clave_rut] => 20.288.551-9
    [backup_clave_email] => <EMAIL>
    [backup_clave_telefono] => 983802784
)

[11-Jun-2025 23:38:52 UTC] Archivos recibidos: Array
(
    [archivo_ci] => Array
        (
            [name] => C.I Ana Lepiěn.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phposJLpI
            [error] => 0
            [size] => 788882
        )

    [archivo_erut] => Array
        (
            [name] => E-RUT ANA MARIA LEPIN.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpLUjqGI
            [error] => 0
            [size] => 59452
        )

    [archivo_extracto] => Array
        (
            [name] => Copia Escritura - PROTOCOLIZACION EXTRACTO_123456814525.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpf5omfe
            [error] => 0
            [size] => 3023338
        )

    [archivo_ci_frente] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_ci_detras] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_carpeta_tributaria] => Array
        (
            [name] => 
            [type] => 
            [tmp_name] => 
            [error] => 4
            [size] => 0
        )

    [archivo_consulta_terceros] => Array
        (
            [name] => EXTRACTO VEGAFRESH.pdf
            [type] => application/pdf
            [tmp_name] => /tmp/phpC4VDDQ
            [error] => 0
            [size] => 560588
        )

)

[11-Jun-2025 23:38:52 UTC] ID de usuario de la sesión: 1
[11-Jun-2025 23:38:52 UTC] Procesando archivo: archivo_ci
[11-Jun-2025 23:38:52 UTC] Archivo 'archivo_ci' guardado en: documentosExperian/ci_1_684a138cc8e73.pdf
[11-Jun-2025 23:38:52 UTC] Procesando archivo: archivo_erut
[11-Jun-2025 23:38:52 UTC] Archivo 'archivo_erut' guardado en: documentosExperian/erut_1_684a138cc9925.pdf
[11-Jun-2025 23:38:52 UTC] Procesando archivo: archivo_extracto
[11-Jun-2025 23:38:52 UTC] Archivo 'archivo_extracto' guardado en: documentosExperian/extracto_1_684a138cc9b96.pdf
[11-Jun-2025 23:38:52 UTC] No se recibió archivo para: archivo_ci_frente
[11-Jun-2025 23:38:52 UTC] No se recibió archivo para: archivo_ci_detras
[11-Jun-2025 23:38:52 UTC] No se recibió archivo para: archivo_carpeta_tributaria
[11-Jun-2025 23:38:52 UTC] Procesando archivo: archivo_consulta_terceros
[11-Jun-2025 23:38:52 UTC] Archivo 'archivo_consulta_terceros' guardado en: documentosExperian/consterc_1_684a138ccbf07.pdf
[11-Jun-2025 23:38:52 UTC] Campos a insertar: id_usuario, ruta_ci, ruta_erut, ruta_extracto, ruta_consulta_terceros, tipo_cliente, rut, razon_social, nombre_representante1, rut_representante1, nombre_representante2, rut_representante2, nombre_representante3, rut_representante3, sistema_creacion, fecha_creacion, notaria, actividad_economica, fecha_constitucion, direccion, comuna, pagina_web, email, telefono, clasificacion_sii, contacto_nombre, contacto_rut, contacto_telefono, contacto_email, contacto_backup_nombre, contacto_backup_rut, contacto_backup_telefono, contacto_backup_email, morosos_plan, morosos_consultas, morosos_uf, morosos_descuento, morosos_nuevo_valor, advanced_plan, advanced_consultas, advanced_uf, advanced_descuento, advanced_nuevo_valor, key_user_nombre, key_user_rut, key_user_email, key_user_telefono, key_user_backup_nombre, key_user_backup_rut, key_user_backup_email, key_user_backup_telefono
[11-Jun-2025 23:38:52 UTC] Tipos de parámetros: issssssssssssssssssssssssssssssssssdidssdidssssssss
[11-Jun-2025 23:38:52 UTC] Parámetros: Array
(
    [0] => 1
    [1] => documentosExperian/ci_1_684a138cc8e73.pdf
    [2] => documentosExperian/erut_1_684a138cc9925.pdf
    [3] => documentosExperian/extracto_1_684a138cc9b96.pdf
    [4] => documentosExperian/consterc_1_684a138ccbf07.pdf
    [5] => Cliente Vigente
    [6] => 77.131.259-4
    [7] => VEGAFRESH SPA
    [8] => ANA MARIA LEPIN GONZALEZ
    [9] => 13.290.856-7
    [10] => 
    [11] => 
    [12] => 
    [13] => 
    [14] => Tradicional
    [15] => 2020-03-09
    [16] => 
    [17] => 463011
    [18] => 2020-03-09
    [19] => MANCO CAPAC 1560
    [20] => INDEPENDENCIA
    [21] => 
    [22] => <EMAIL>
    [23] => 961420560
    [24] => Nuevo
    [25] => ANA MARIA LEPIN GONZALEZ
    [26] => 13.290.856-7
    [27] => 961420560
    [28] => <EMAIL>
    [29] => CAMILA FERNANDA ABARZUA LEPIN
    [30] => 20.288.551-9
    [31] => 983802784
    [32] => <EMAIL>
    [33] => L
    [34] => 
    [35] => 
    [36] => 
    [37] => 
    [38] => 
    [39] => 
    [40] => 
    [41] => 
    [42] => 
    [43] => ANA MARIA LEPIN GONZALEZ
    [44] => 13.290.856-7
    [45] => <EMAIL>
    [46] => 961420560
    [47] => CAMILA FERNANDA ABARZUA LEPIN
    [48] => 20.288.551-9
    [49] => <EMAIL>
    [50] => 983802784
)

[13-Jun-2025 15:51:57 UTC] form_experian2.php - Usuario ID: 4 - Datos: {"id":"4","correo":"<EMAIL>","nombre_usuario":"Oriana Camilo","rol":"admin"}
[13-Jun-2025 15:51:57 UTC] form_experian2.php - Usuario Oriana Camilo (ID: 4) es administrador
[13-Jun-2025 15:51:57 UTC] form_experian2.php - Consulta SQL para administrador: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC
[13-Jun-2025 15:51:57 UTC] form_experian2.php - Se encontraron 79 prospectos para el usuario ID: 4
[13-Jun-2025 16:27:23 UTC] [2025-06-13 16:27:23] Error de autenticación - Usuario no identificado - IP: ***************
[13-Jun-2025 16:29:46 UTC] form_experian2.php - Usuario ID: 4 - Datos: {"id":"4","correo":"<EMAIL>","nombre_usuario":"Oriana Camilo","rol":"admin"}
[13-Jun-2025 16:29:46 UTC] form_experian2.php - Usuario Oriana Camilo (ID: 4) es administrador
[13-Jun-2025 16:29:46 UTC] form_experian2.php - Consulta SQL para administrador: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC
[13-Jun-2025 16:29:46 UTC] form_experian2.php - Se encontraron 79 prospectos para el usuario ID: 4
[13-Jun-2025 16:46:11 UTC] form_experian2.php - Usuario ID: 4 - Datos: {"id":"4","correo":"<EMAIL>","nombre_usuario":"Oriana Camilo","rol":"admin"}
[13-Jun-2025 16:46:11 UTC] form_experian2.php - Usuario Oriana Camilo (ID: 4) es administrador
[13-Jun-2025 16:46:11 UTC] form_experian2.php - Consulta SQL para administrador: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC
[13-Jun-2025 16:46:11 UTC] form_experian2.php - Se encontraron 79 prospectos para el usuario ID: 4
[13-Jun-2025 16:46:32 UTC] [2025-06-13 16:46:32] Logout - Usuario: <EMAIL> - IP: ***************
[13-Jun-2025 16:46:37 UTC] form_experian2.php - Usuario ID: 4 - Datos: {"id":"4","correo":"<EMAIL>","nombre_usuario":"Oriana Camilo","rol":"admin"}
[13-Jun-2025 16:46:37 UTC] form_experian2.php - Usuario Oriana Camilo (ID: 4) es administrador
[13-Jun-2025 16:46:37 UTC] form_experian2.php - Consulta SQL para administrador: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC
[13-Jun-2025 16:46:37 UTC] form_experian2.php - Se encontraron 79 prospectos para el usuario ID: 4
[13-Jun-2025 16:47:12 UTC] [2025-06-13 16:47:12] Error de autenticación - Usuario no identificado - IP: ***************
[13-Jun-2025 16:47:32 UTC] form_experian2.php - Usuario ID: 4 - Datos: {"id":"4","correo":"<EMAIL>","nombre_usuario":"Oriana Camilo","rol":"admin"}
[13-Jun-2025 16:47:32 UTC] form_experian2.php - Usuario Oriana Camilo (ID: 4) es administrador
[13-Jun-2025 16:47:32 UTC] form_experian2.php - Consulta SQL para administrador: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC
[13-Jun-2025 16:47:32 UTC] form_experian2.php - Se encontraron 79 prospectos para el usuario ID: 4
[13-Jun-2025 16:59:49 UTC] form_experian2.php - Usuario ID: 4 - Datos: {"id":"4","correo":"<EMAIL>","nombre_usuario":"Oriana Camilo","rol":"admin"}
[13-Jun-2025 16:59:49 UTC] form_experian2.php - Usuario Oriana Camilo (ID: 4) es administrador
[13-Jun-2025 16:59:49 UTC] form_experian2.php - Consulta SQL para administrador: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC
[13-Jun-2025 16:59:49 UTC] form_experian2.php - Se encontraron 79 prospectos para el usuario ID: 4
[13-Jun-2025 17:07:10 UTC] form_experian2.php - Usuario ID: 4 - Datos: {"id":"4","correo":"<EMAIL>","nombre_usuario":"Oriana Camilo","rol":"admin"}
[13-Jun-2025 17:07:10 UTC] form_experian2.php - Usuario Oriana Camilo (ID: 4) es administrador
[13-Jun-2025 17:07:10 UTC] form_experian2.php - Consulta SQL para administrador: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC
[13-Jun-2025 17:07:10 UTC] form_experian2.php - Se encontraron 79 prospectos para el usuario ID: 4
[13-Jun-2025 17:12:31 UTC] form_experian2.php - Usuario ID: 4 - Datos: {"id":"4","correo":"<EMAIL>","nombre_usuario":"Oriana Camilo","rol":"admin"}
[13-Jun-2025 17:12:31 UTC] form_experian2.php - Usuario Oriana Camilo (ID: 4) es administrador
[13-Jun-2025 17:12:31 UTC] form_experian2.php - Consulta SQL para administrador: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC
[13-Jun-2025 17:12:31 UTC] form_experian2.php - Se encontraron 79 prospectos para el usuario ID: 4
[16-Jun-2025 15:01:13 UTC] form_experian2.php - Usuario ID: 4 - Datos: {"id":"4","correo":"<EMAIL>","nombre_usuario":"Oriana Camilo","rol":"admin"}
[16-Jun-2025 15:01:13 UTC] form_experian2.php - Usuario Oriana Camilo (ID: 4) es administrador
[16-Jun-2025 15:01:13 UTC] form_experian2.php - Consulta SQL para administrador: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC
[16-Jun-2025 15:01:13 UTC] form_experian2.php - Se encontraron 79 prospectos para el usuario ID: 4
[02-Jul-2025 22:33:12 UTC] [2025-07-02 22:33:12] Error de autenticación - Usuario no identificado - IP: ************
[02-Jul-2025 23:11:17 UTC] PHP Warning:  require_once(dist/con_db.php): failed to open stream: No such file or directory in /home/<USER>/public_html/intranet/dist/update_usuarios_schema.php on line 2
[02-Jul-2025 23:11:17 UTC] PHP Fatal error:  require_once(): Failed opening required 'dist/con_db.php' (include_path='.:/opt/alt/php73/usr/share/pear') in /home/<USER>/public_html/intranet/dist/update_usuarios_schema.php on line 2
[02-Jul-2025 23:11:30 UTC] PHP Notice:  Undefined variable: correo in /home/<USER>/public_html/intranet/dist/index.php on line 45
[02-Jul-2025 23:17:30 UTC] PHP Warning:  require_once(dist/con_db.php): failed to open stream: No such file or directory in /home/<USER>/public_html/intranet/dist/test_prospect_system.php on line 41
[02-Jul-2025 23:17:30 UTC] PHP Fatal error:  require_once(): Failed opening required 'dist/con_db.php' (include_path='.:/opt/alt/php73/usr/share/pear') in /home/<USER>/public_html/intranet/dist/test_prospect_system.php on line 41
[02-Jul-2025 23:20:28 UTC] form_experian2.php - Usuario ID: 7 - Datos: {"id":"7","correo":"<EMAIL>","nombre_usuario":"CATHERINE DEL CARMEN PINCHEIRA BRITO","rol":"admin"}
[02-Jul-2025 23:20:28 UTC] form_experian2.php - Usuario CATHERINE DEL CARMEN PINCHEIRA BRITO (ID: 7) es administrador
[02-Jul-2025 23:20:28 UTC] form_experian2.php - Consulta SQL para administrador: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC
[02-Jul-2025 23:20:28 UTC] form_experian2.php - Se encontraron 79 prospectos para el usuario ID: 7
[02-Jul-2025 23:31:46 UTC] PHP Warning:  require_once(dist/con_db.php): failed to open stream: No such file or directory in /home/<USER>/public_html/intranet/dist/test_project_redirection.php on line 7
[02-Jul-2025 23:31:46 UTC] PHP Fatal error:  require_once(): Failed opening required 'dist/con_db.php' (include_path='.:/opt/alt/php73/usr/share/pear') in /home/<USER>/public_html/intranet/dist/test_project_redirection.php on line 7
[02-Jul-2025 23:34:21 UTC] [2025-07-02 23:34:21] Error de autenticación - Usuario no identificado - IP: ************
[02-Jul-2025 23:34:28 UTC] [2025-07-02 23:34:28] Error de autenticación - Usuario no identificado - IP: ************
[02-Jul-2025 23:37:27 UTC] form_experian2.php - Usuario ID: 7 - Datos: {"id":"7","correo":"<EMAIL>","nombre_usuario":"CATHERINE DEL CARMEN PINCHEIRA BRITO","rol":"admin"}
[02-Jul-2025 23:37:27 UTC] form_experian2.php - Usuario CATHERINE DEL CARMEN PINCHEIRA BRITO (ID: 7) es administrador
[02-Jul-2025 23:37:27 UTC] form_experian2.php - Consulta SQL para administrador: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC
[02-Jul-2025 23:37:27 UTC] form_experian2.php - Se encontraron 79 prospectos para el usuario ID: 7
[02-Jul-2025 23:37:39 UTC] [2025-07-02 23:37:39] Logout - Usuario: <EMAIL> - IP: ************
[02-Jul-2025 23:38:11 UTC] [2025-07-02 23:38:11] Logout - Usuario: <EMAIL> - IP: ************
[02-Jul-2025 23:46:19 UTC] form_experian2.php - Usuario ID: 7 - Datos: {"id":"7","correo":"<EMAIL>","nombre_usuario":"CATHERINE DEL CARMEN PINCHEIRA BRITO","rol":"admin"}
[02-Jul-2025 23:46:19 UTC] form_experian2.php - Usuario CATHERINE DEL CARMEN PINCHEIRA BRITO (ID: 7) es administrador
[02-Jul-2025 23:46:19 UTC] form_experian2.php - Consulta SQL para administrador: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC
[02-Jul-2025 23:46:19 UTC] form_experian2.php - Se encontraron 79 prospectos para el usuario ID: 7
[02-Jul-2025 23:46:29 UTC] [2025-07-02 23:46:29] Logout - Usuario: <EMAIL> - IP: ************
[02-Jul-2025 23:52:41 UTC] form_experian2.php - Usuario ID: 7 - Datos: {"id":"7","correo":"<EMAIL>","nombre_usuario":"CATHERINE DEL CARMEN PINCHEIRA BRITO","rol":"admin"}
[02-Jul-2025 23:52:41 UTC] form_experian2.php - Usuario CATHERINE DEL CARMEN PINCHEIRA BRITO (ID: 7) es administrador
[02-Jul-2025 23:52:41 UTC] form_experian2.php - Consulta SQL para administrador: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC
[02-Jul-2025 23:52:41 UTC] form_experian2.php - Se encontraron 79 prospectos para el usuario ID: 7
[02-Jul-2025 23:52:52 UTC] [2025-07-02 23:52:52] Logout - Usuario: <EMAIL> - IP: ************
[02-Jul-2025 23:53:18 UTC] form_experian2.php - Usuario ID: 3 - Datos: {"id":"3","correo":"<EMAIL>","nombre_usuario":"Jose Galaz","rol":"ejecutivos"}
[02-Jul-2025 23:53:18 UTC] form_experian2.php - Usuario Jose Galaz (ID: 3) NO es administrador
[02-Jul-2025 23:53:18 UTC] form_experian2.php - Consulta SQL para usuario regular: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC (usuario_id = 3)
[02-Jul-2025 23:53:18 UTC] form_experian2.php - Se encontraron 37 prospectos para el usuario ID: 3
[02-Jul-2025 23:53:38 UTC] [2025-07-02 23:53:38] Logout - Usuario: <EMAIL> - IP: ************
[03-Jul-2025 01:03:33 UTC] [2025-07-03 01:03:33] Error de autenticación - Usuario no identificado - IP: ************
[03-Jul-2025 01:10:52 UTC] PHP Notice:  Undefined variable: conexion in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 103
[03-Jul-2025 01:10:52 UTC] PHP Fatal error:  Uncaught Error: Call to a member function prepare() on null in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:103
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 103
[03-Jul-2025 01:13:31 UTC] PHP Notice:  Undefined variable: conexion in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 103
[03-Jul-2025 01:13:31 UTC] PHP Fatal error:  Uncaught Error: Call to a member function prepare() on null in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:103
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 103
[03-Jul-2025 01:13:58 UTC] form_experian2.php - Usuario ID: 7 - Datos: {"id":"7","correo":"<EMAIL>","nombre_usuario":"CATHERINE DEL CARMEN PINCHEIRA BRITO","rol":"admin"}
[03-Jul-2025 01:13:58 UTC] form_experian2.php - Usuario CATHERINE DEL CARMEN PINCHEIRA BRITO (ID: 7) es administrador
[03-Jul-2025 01:13:58 UTC] form_experian2.php - Consulta SQL para administrador: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC
[03-Jul-2025 01:13:58 UTC] form_experian2.php - Se encontraron 79 prospectos para el usuario ID: 7
[03-Jul-2025 01:16:31 UTC] PHP Notice:  Undefined variable: conexion in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 103
[03-Jul-2025 01:16:31 UTC] PHP Fatal error:  Uncaught Error: Call to a member function prepare() on null in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:103
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 103
[03-Jul-2025 01:17:38 UTC] PHP Notice:  Undefined variable: conexion in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 103
[03-Jul-2025 01:17:38 UTC] PHP Fatal error:  Uncaught Error: Call to a member function prepare() on null in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:103
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 103
[03-Jul-2025 01:28:10 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 01:28:10 UTC] REQUEST_METHOD: POST
[03-Jul-2025 01:28:10 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 7
    [rol] => admin
    [nombre_usuario] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [proyecto] => inteletGroup
    [auth_token] => 0344554dbabbfae8df12cbdea2e81006c5e13d13f3f592f7feea111860f21ef8
    [session_time] => **********
    [last_activity] => 1751506039
)

[03-Jul-2025 01:28:10 UTC] POST: Array
(
    [nombre_ejecutivo] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [rut_cliente] => ********-5
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [usuario_id] => 7
)

[03-Jul-2025 01:28:10 UTC] Datos recibidos - RUT: ********-5, Razón Social: EMPRESA EJEMPLO LTDA
[03-Jul-2025 01:28:10 UTC] PHP Notice:  Undefined variable: conexion in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 112
[03-Jul-2025 01:28:10 UTC] PHP Fatal error:  Uncaught Error: Call to a member function prepare() on null in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:112
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 112
[03-Jul-2025 01:28:21 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 01:28:21 UTC] REQUEST_METHOD: GET
[03-Jul-2025 01:28:21 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 7
    [rol] => admin
    [nombre_usuario] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [proyecto] => inteletGroup
    [auth_token] => 0344554dbabbfae8df12cbdea2e81006c5e13d13f3f592f7feea111860f21ef8
    [session_time] => **********
    [last_activity] => 1751506039
)

[03-Jul-2025 01:28:21 UTC] POST: Array
(
)

[03-Jul-2025 01:29:31 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 01:29:31 UTC] REQUEST_METHOD: POST
[03-Jul-2025 01:29:31 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 7
    [rol] => admin
    [nombre_usuario] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [proyecto] => inteletGroup
    [auth_token] => 0344554dbabbfae8df12cbdea2e81006c5e13d13f3f592f7feea111860f21ef8
    [session_time] => **********
    [last_activity] => 1751506039
)

[03-Jul-2025 01:29:31 UTC] POST: Array
(
    [nombre_ejecutivo] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [rut_cliente] => ********-1
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [usuario_id] => 7
)

[03-Jul-2025 01:29:31 UTC] Datos recibidos - RUT: ********-1, Razón Social: EMPRESA EJEMPLO LTDA
[03-Jul-2025 01:29:31 UTC] PHP Notice:  Undefined variable: conexion in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 112
[03-Jul-2025 01:29:31 UTC] PHP Fatal error:  Uncaught Error: Call to a member function prepare() on null in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:112
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 112
[03-Jul-2025 01:29:42 UTC] form_experian2.php - Usuario ID: 7 - Datos: {"id":"7","correo":"<EMAIL>","nombre_usuario":"CATHERINE DEL CARMEN PINCHEIRA BRITO","rol":"admin"}
[03-Jul-2025 01:29:42 UTC] form_experian2.php - Usuario CATHERINE DEL CARMEN PINCHEIRA BRITO (ID: 7) es administrador
[03-Jul-2025 01:29:42 UTC] form_experian2.php - Consulta SQL para administrador: SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC
[03-Jul-2025 01:29:42 UTC] form_experian2.php - Se encontraron 79 prospectos para el usuario ID: 7
[03-Jul-2025 01:31:31 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 01:31:31 UTC] REQUEST_METHOD: POST
[03-Jul-2025 01:31:31 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 7
    [rol] => admin
    [nombre_usuario] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [proyecto] => inteletGroup
    [auth_token] => 0344554dbabbfae8df12cbdea2e81006c5e13d13f3f592f7feea111860f21ef8
    [session_time] => **********
    [last_activity] => **********
)

[03-Jul-2025 01:31:31 UTC] POST: Array
(
    [nombre_ejecutivo] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [rut_cliente] => ********-4
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [usuario_id] => 7
)

[03-Jul-2025 01:31:31 UTC] Datos recibidos - RUT: ********-4, Razón Social: EMPRESA EJEMPLO LTDA
[03-Jul-2025 01:31:31 UTC] PHP Notice:  Undefined variable: conexion in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 112
[03-Jul-2025 01:31:31 UTC] PHP Fatal error:  Uncaught Error: Call to a member function prepare() on null in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:112
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 112
[03-Jul-2025 01:33:54 UTC] [2025-07-03 01:33:54] Error de autenticación - Usuario no identificado - IP: ************
[03-Jul-2025 01:36:03 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 01:36:03 UTC] REQUEST_METHOD: POST
[03-Jul-2025 01:36:03 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 11
    [rol] => ejecutivos
    [nombre_usuario] => TANIA ELIZABETH MUÑOZ PIZARRO
    [proyecto] => inteletGroup
    [auth_token] => 94e52ed23f5e6916fc2bf14150b1b52acb6fb002ae7119b0655fafff35827fe8
    [session_time] => 1751506525
    [last_activity] => 1751506528
)

[03-Jul-2025 01:36:03 UTC] POST: Array
(
    [nombre_ejecutivo] => TANIA ELIZABETH MUÑOZ PIZARRO
    [rut_cliente] => ********-9
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [documentos] => Array
        (
            [0] => [object File]
        )

    [usuario_id] => 11
)

[03-Jul-2025 01:36:03 UTC] Datos recibidos - RUT: ********-9, Razón Social: EMPRESA EJEMPLO LTDA
[03-Jul-2025 01:36:03 UTC] PHP Notice:  Undefined variable: conexion in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 112
[03-Jul-2025 01:36:03 UTC] PHP Fatal error:  Uncaught Error: Call to a member function prepare() on null in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:112
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 112
[03-Jul-2025 01:46:14 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 01:46:14 UTC] REQUEST_METHOD: POST
[03-Jul-2025 01:46:14 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 11
    [rol] => ejecutivos
    [nombre_usuario] => TANIA ELIZABETH MUÑOZ PIZARRO
    [proyecto] => inteletGroup
    [auth_token] => 94e52ed23f5e6916fc2bf14150b1b52acb6fb002ae7119b0655fafff35827fe8
    [session_time] => 1751506525
    [last_activity] => 1751507144
)

[03-Jul-2025 01:46:14 UTC] POST: Array
(
    [nombre_ejecutivo] => TANIA ELIZABETH MUÑOZ PIZARRO
    [rut_cliente] => ********-9
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [documentos] => Array
        (
            [0] => [object File]
        )

    [usuario_id] => 11
)

[03-Jul-2025 01:46:14 UTC] Datos recibidos - RUT: ********-9, Razón Social: EMPRESA EJEMPLO LTDA
[03-Jul-2025 01:46:14 UTC] PHP Notice:  Undefined variable: conexion in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 112
[03-Jul-2025 01:46:14 UTC] PHP Fatal error:  Uncaught Error: Call to a member function prepare() on null in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:112
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 112
[03-Jul-2025 01:47:46 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 01:47:46 UTC] REQUEST_METHOD: POST
[03-Jul-2025 01:47:46 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 7
    [rol] => admin
    [nombre_usuario] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [proyecto] => inteletGroup
    [auth_token] => 0344554dbabbfae8df12cbdea2e81006c5e13d13f3f592f7feea111860f21ef8
    [session_time] => **********
    [last_activity] => **********
)

[03-Jul-2025 01:47:46 UTC] POST: Array
(
    [nombre_ejecutivo] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [rut_cliente] => ********-5
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [usuario_id] => 7
)

[03-Jul-2025 01:47:46 UTC] Datos recibidos - RUT: ********-5, Razón Social: EMPRESA EJEMPLO LTDA
[03-Jul-2025 01:47:46 UTC] PHP Notice:  Undefined variable: conexion in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 112
[03-Jul-2025 01:47:46 UTC] PHP Fatal error:  Uncaught Error: Call to a member function prepare() on null in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:112
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 112
[03-Jul-2025 01:53:11 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 01:53:11 UTC] REQUEST_METHOD: POST
[03-Jul-2025 01:53:11 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 7
    [rol] => admin
    [nombre_usuario] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [proyecto] => inteletGroup
    [auth_token] => 0344554dbabbfae8df12cbdea2e81006c5e13d13f3f592f7feea111860f21ef8
    [session_time] => **********
    [last_activity] => **********
)

[03-Jul-2025 01:53:11 UTC] POST: Array
(
    [nombre_ejecutivo] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [rut_cliente] => ********-1
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [usuario_id] => 7
)

[03-Jul-2025 01:53:11 UTC] Datos recibidos - RUT: ********-1, Razón Social: EMPRESA EJEMPLO LTDA
[03-Jul-2025 01:53:11 UTC] PHP Notice:  Undefined variable: conexion in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 112
[03-Jul-2025 01:53:11 UTC] PHP Fatal error:  Uncaught Error: Call to a member function prepare() on null in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:112
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 112
[03-Jul-2025 01:59:41 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 01:59:41 UTC] REQUEST_METHOD: POST
[03-Jul-2025 01:59:41 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 11
    [rol] => ejecutivos
    [nombre_usuario] => TANIA ELIZABETH MUÑOZ PIZARRO
    [proyecto] => inteletGroup
    [auth_token] => 94e52ed23f5e6916fc2bf14150b1b52acb6fb002ae7119b0655fafff35827fe8
    [session_time] => 1751506525
    [last_activity] => 1751507949
)

[03-Jul-2025 01:59:41 UTC] POST: Array
(
    [nombre_ejecutivo] => TANIA ELIZABETH MUÑOZ PIZARRO
    [rut_cliente] => ********-9
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [documentos] => Array
        (
            [0] => [object File]
        )

    [usuario_id] => 11
    [timestamp] => *************
    [bypass_sw] => 1
)

[03-Jul-2025 01:59:41 UTC] GET: Array
(
    [t] => *************
    [r] => l3wo4cvce
)

[03-Jul-2025 01:59:41 UTC] Inicio procesamiento: 01:59:41
[03-Jul-2025 01:59:41 UTC] Datos recibidos - RUT: ********-9, Razón Social: EMPRESA EJEMPLO LTDA
[03-Jul-2025 01:59:41 UTC] PHP Notice:  Undefined variable: conexion in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 148
[03-Jul-2025 01:59:41 UTC] PHP Fatal error:  Uncaught Error: Call to a member function query() on null in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:148
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 148
[03-Jul-2025 01:59:41 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 01:59:41 UTC] REQUEST_METHOD: POST
[03-Jul-2025 01:59:41 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 11
    [rol] => ejecutivos
    [nombre_usuario] => TANIA ELIZABETH MUÑOZ PIZARRO
    [proyecto] => inteletGroup
    [auth_token] => 94e52ed23f5e6916fc2bf14150b1b52acb6fb002ae7119b0655fafff35827fe8
    [session_time] => 1751506525
    [last_activity] => 1751507949
)

[03-Jul-2025 01:59:41 UTC] POST: Array
(
    [nombre_ejecutivo] => TANIA ELIZABETH MUÑOZ PIZARRO
    [rut_cliente] => ********-9
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [documentos] => Array
        (
            [0] => [object File]
        )

    [usuario_id] => 11
    [timestamp] => *************
    [bypass_sw] => 1
)

[03-Jul-2025 01:59:41 UTC] GET: Array
(
    [t] => *************
    [r] => l3wo4cvce
)

[03-Jul-2025 01:59:41 UTC] Inicio procesamiento: 01:59:41
[03-Jul-2025 01:59:41 UTC] Datos recibidos - RUT: ********-9, Razón Social: EMPRESA EJEMPLO LTDA
[03-Jul-2025 01:59:41 UTC] PHP Notice:  Undefined variable: conexion in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 148
[03-Jul-2025 01:59:41 UTC] PHP Fatal error:  Uncaught Error: Call to a member function query() on null in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:148
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 148
[03-Jul-2025 02:03:37 UTC] PHP Notice:  Undefined variable: correo in /home/<USER>/public_html/intranet/dist/index.php on line 45
[03-Jul-2025 02:05:07 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 02:05:39 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 02:05:39 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 02:05:41 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 02:05:46 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 02:05:46 UTC] REQUEST_METHOD: POST
[03-Jul-2025 02:05:46 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 7
    [rol] => admin
    [nombre_usuario] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [proyecto] => inteletGroup
    [auth_token] => 402b03e5328d23bc419532820e1c9df892ab3faf1814f5ac51d52b0fe9bf809b
    [session_time] => **********
    [last_activity] => **********
)

[03-Jul-2025 02:05:46 UTC] POST: Array
(
    [nombre_ejecutivo] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [rut_cliente] => ********-9
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [documentos] => Array
        (
            [0] => [object File]
        )

    [usuario_id] => 7
    [timestamp] => *************
    [bypass_sw] => 1
)

[03-Jul-2025 02:05:46 UTC] GET: Array
(
    [t] => *************
    [r] => 9beg9zp2j
)

[03-Jul-2025 02:05:46 UTC] Inicio procesamiento: 02:05:46
[03-Jul-2025 02:05:46 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 02:05:46 UTC] Error crítico: No se pudo establecer conexión a la base de datos
[03-Jul-2025 02:05:46 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 02:05:46 UTC] REQUEST_METHOD: POST
[03-Jul-2025 02:05:46 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 7
    [rol] => admin
    [nombre_usuario] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [proyecto] => inteletGroup
    [auth_token] => 402b03e5328d23bc419532820e1c9df892ab3faf1814f5ac51d52b0fe9bf809b
    [session_time] => **********
    [last_activity] => **********
)

[03-Jul-2025 02:05:46 UTC] POST: Array
(
    [nombre_ejecutivo] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [rut_cliente] => ********-9
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [documentos] => Array
        (
            [0] => [object File]
        )

    [usuario_id] => 7
    [timestamp] => *************
    [bypass_sw] => 1
)

[03-Jul-2025 02:05:46 UTC] GET: Array
(
    [t] => *************
    [r] => 9beg9zp2j
)

[03-Jul-2025 02:05:46 UTC] Inicio procesamiento: 02:05:46
[03-Jul-2025 02:05:46 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 02:05:46 UTC] Error crítico: No se pudo establecer conexión a la base de datos
[03-Jul-2025 02:08:17 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 02:08:17 UTC] REQUEST_METHOD: POST
[03-Jul-2025 02:08:17 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 7
    [rol] => admin
    [nombre_usuario] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [proyecto] => inteletGroup
    [auth_token] => 0344554dbabbfae8df12cbdea2e81006c5e13d13f3f592f7feea111860f21ef8
    [session_time] => **********
    [last_activity] => **********
)

[03-Jul-2025 02:08:17 UTC] POST: Array
(
    [nombre_ejecutivo] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [rut_cliente] => ********-1
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [usuario_id] => 7
)

[03-Jul-2025 02:08:17 UTC] GET: Array
(
)

[03-Jul-2025 02:08:17 UTC] Inicio procesamiento: 02:08:17
[03-Jul-2025 02:08:17 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 02:08:17 UTC] Error crítico: No se pudo establecer conexión a la base de datos
[03-Jul-2025 02:12:07 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 02:13:05 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 02:13:05 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 02:13:05 UTC] REQUEST_METHOD: POST
[03-Jul-2025 02:13:05 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 7
    [rol] => admin
    [nombre_usuario] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [proyecto] => inteletGroup
    [auth_token] => 0344554dbabbfae8df12cbdea2e81006c5e13d13f3f592f7feea111860f21ef8
    [session_time] => **********
    [last_activity] => **********
)

[03-Jul-2025 02:13:05 UTC] POST: Array
(
    [nombre_ejecutivo] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [rut_cliente] => ********-9
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [documentos] => Array
        (
            [0] => [object File]
        )

    [usuario_id] => 7
    [timestamp] => *************
    [bypass_sw] => 1
)

[03-Jul-2025 02:13:05 UTC] GET: Array
(
    [t] => *************
    [r] => ia5ygi3xy
)

[03-Jul-2025 02:13:05 UTC] Inicio procesamiento: 02:13:05
[03-Jul-2025 02:13:05 UTC] DATOS FORMULARIO: usuario_id=7, nombre=CATHERINE DEL CARMEN PINCHEIRA BRITO, rut=********-9
[03-Jul-2025 02:13:05 UTC] Datos recibidos - RUT: ********-9, Razón Social: EMPRESA EJEMPLO LTDA
[03-Jul-2025 02:13:05 UTC] Creando tabla si no existe: tb_inteletgroup_prospectos
[03-Jul-2025 02:13:05 UTC] Tabla verificada/creada correctamente
[03-Jul-2025 02:13:05 UTC] SQL Verificación: SELECT id FROM tb_inteletgroup_prospectos WHERE rut_cliente = ? con parámetro: ********-9
[03-Jul-2025 02:13:05 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method mysqli_stmt::get_result() in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:192
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 192
[03-Jul-2025 02:13:05 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 02:13:05 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 02:13:05 UTC] REQUEST_METHOD: POST
[03-Jul-2025 02:13:05 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 7
    [rol] => admin
    [nombre_usuario] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [proyecto] => inteletGroup
    [auth_token] => 0344554dbabbfae8df12cbdea2e81006c5e13d13f3f592f7feea111860f21ef8
    [session_time] => **********
    [last_activity] => **********
)

[03-Jul-2025 02:13:05 UTC] POST: Array
(
    [nombre_ejecutivo] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [rut_cliente] => ********-9
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [documentos] => Array
        (
            [0] => [object File]
        )

    [usuario_id] => 7
    [timestamp] => *************
    [bypass_sw] => 1
)

[03-Jul-2025 02:13:05 UTC] GET: Array
(
    [t] => *************
    [r] => ia5ygi3xy
)

[03-Jul-2025 02:13:05 UTC] Inicio procesamiento: 02:13:05
[03-Jul-2025 02:13:05 UTC] DATOS FORMULARIO: usuario_id=7, nombre=CATHERINE DEL CARMEN PINCHEIRA BRITO, rut=********-9
[03-Jul-2025 02:13:05 UTC] Datos recibidos - RUT: ********-9, Razón Social: EMPRESA EJEMPLO LTDA
[03-Jul-2025 02:13:05 UTC] Creando tabla si no existe: tb_inteletgroup_prospectos
[03-Jul-2025 02:13:05 UTC] Tabla verificada/creada correctamente
[03-Jul-2025 02:13:05 UTC] SQL Verificación: SELECT id FROM tb_inteletgroup_prospectos WHERE rut_cliente = ? con parámetro: ********-9
[03-Jul-2025 02:13:05 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method mysqli_stmt::get_result() in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:192
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 192
[03-Jul-2025 02:17:47 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 02:17:47 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 02:17:47 UTC] REQUEST_METHOD: POST
[03-Jul-2025 02:17:47 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 7
    [rol] => admin
    [nombre_usuario] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [proyecto] => inteletGroup
    [auth_token] => 0344554dbabbfae8df12cbdea2e81006c5e13d13f3f592f7feea111860f21ef8
    [session_time] => **********
    [last_activity] => **********
)

[03-Jul-2025 02:17:47 UTC] POST: Array
(
    [nombre_ejecutivo] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [rut_cliente] => ********-4
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [usuario_id] => 7
)

[03-Jul-2025 02:17:47 UTC] GET: Array
(
)

[03-Jul-2025 02:17:47 UTC] Inicio procesamiento: 02:17:47
[03-Jul-2025 02:17:47 UTC] DATOS FORMULARIO: usuario_id=7, nombre=CATHERINE DEL CARMEN PINCHEIRA BRITO, rut=********-4
[03-Jul-2025 02:17:47 UTC] Datos recibidos - RUT: ********-4, Razón Social: EMPRESA EJEMPLO LTDA
[03-Jul-2025 02:17:47 UTC] Creando tabla si no existe: tb_inteletgroup_prospectos
[03-Jul-2025 02:17:47 UTC] SQL de creación: CREATE TABLE IF NOT EXISTS tb_inteletgroup_prospectos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        nombre_ejecutivo VARCHAR(255) NOT NULL,
        rut_cliente VARCHAR(20) NOT NULL,
        razon_social VARCHAR(255) NOT NULL,
        rubro VARCHAR(255) NOT NULL,
        direccion_comercial TEXT NOT NULL,
        telefono_celular VARCHAR(20) NOT NULL,
        email VARCHAR(255) NOT NULL,
        numero_pos VARCHAR(50),
        tipo_cuenta VARCHAR(50) NOT NULL,
        numero_cuenta_bancaria VARCHAR(50) NOT NULL,
        dias_atencion VARCHAR(100) NOT NULL,
        horario_atencion VARCHAR(100) NOT NULL,
        contrata_boleta VARCHAR(10) NOT NULL,
        competencia_actual VARCHAR(100) NOT NULL,
        fecha_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
[03-Jul-2025 02:17:47 UTC] Tabla verificada/creada correctamente
[03-Jul-2025 02:17:47 UTC] SQL Verificación: SELECT id FROM tb_inteletgroup_prospectos WHERE rut_cliente = ? con parámetro: ********-4
[03-Jul-2025 02:17:47 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method mysqli_stmt::get_result() in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:199
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 199
[03-Jul-2025 02:17:47 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 02:17:47 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 02:17:47 UTC] REQUEST_METHOD: POST
[03-Jul-2025 02:17:47 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 7
    [rol] => admin
    [nombre_usuario] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [proyecto] => inteletGroup
    [auth_token] => 0344554dbabbfae8df12cbdea2e81006c5e13d13f3f592f7feea111860f21ef8
    [session_time] => **********
    [last_activity] => **********
)

[03-Jul-2025 02:17:47 UTC] POST: Array
(
    [nombre_ejecutivo] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [rut_cliente] => ********-4
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [usuario_id] => 7
)

[03-Jul-2025 02:17:47 UTC] GET: Array
(
)

[03-Jul-2025 02:17:47 UTC] Inicio procesamiento: 02:17:47
[03-Jul-2025 02:17:47 UTC] DATOS FORMULARIO: usuario_id=7, nombre=CATHERINE DEL CARMEN PINCHEIRA BRITO, rut=********-4
[03-Jul-2025 02:17:47 UTC] Datos recibidos - RUT: ********-4, Razón Social: EMPRESA EJEMPLO LTDA
[03-Jul-2025 02:17:47 UTC] Creando tabla si no existe: tb_inteletgroup_prospectos
[03-Jul-2025 02:17:47 UTC] SQL de creación: CREATE TABLE IF NOT EXISTS tb_inteletgroup_prospectos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        nombre_ejecutivo VARCHAR(255) NOT NULL,
        rut_cliente VARCHAR(20) NOT NULL,
        razon_social VARCHAR(255) NOT NULL,
        rubro VARCHAR(255) NOT NULL,
        direccion_comercial TEXT NOT NULL,
        telefono_celular VARCHAR(20) NOT NULL,
        email VARCHAR(255) NOT NULL,
        numero_pos VARCHAR(50),
        tipo_cuenta VARCHAR(50) NOT NULL,
        numero_cuenta_bancaria VARCHAR(50) NOT NULL,
        dias_atencion VARCHAR(100) NOT NULL,
        horario_atencion VARCHAR(100) NOT NULL,
        contrata_boleta VARCHAR(10) NOT NULL,
        competencia_actual VARCHAR(100) NOT NULL,
        fecha_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
[03-Jul-2025 02:17:47 UTC] Tabla verificada/creada correctamente
[03-Jul-2025 02:17:47 UTC] SQL Verificación: SELECT id FROM tb_inteletgroup_prospectos WHERE rut_cliente = ? con parámetro: ********-4
[03-Jul-2025 02:17:47 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method mysqli_stmt::get_result() in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:199
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 199
[03-Jul-2025 04:24:58 UTC] [2025-07-03 04:24:58] Error de autenticación - Usuario no identificado - IP: ************
[03-Jul-2025 04:24:58 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 04:25:06 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 04:25:17 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 04:25:17 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 04:25:20 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 04:25:32 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 04:25:32 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 04:25:32 UTC] REQUEST_METHOD: POST
[03-Jul-2025 04:25:32 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 7
    [rol] => admin
    [nombre_usuario] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [proyecto] => inteletGroup
    [auth_token] => fed06501899b36d543ea0e9e7e6bb06d2186e66a0ed05c105a7214991c304e06
    [session_time] => 1751516717
    [last_activity] => 1751516720
)

[03-Jul-2025 04:25:32 UTC] POST: Array
(
    [nombre_ejecutivo] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [rut_cliente] => ********-9
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [documentos] => Array
        (
            [0] => [object File]
        )

    [usuario_id] => 7
    [timestamp] => *************
    [bypass_sw] => 1
)

[03-Jul-2025 04:25:32 UTC] GET: Array
(
    [t] => *************
    [r] => 17285bydx
)

[03-Jul-2025 04:25:32 UTC] Inicio procesamiento: 04:25:32
[03-Jul-2025 04:25:32 UTC] DATOS FORMULARIO: usuario_id=7, nombre=CATHERINE DEL CARMEN PINCHEIRA BRITO, rut=********-9
[03-Jul-2025 04:25:32 UTC] Datos recibidos - RUT: ********-9, Razón Social: EMPRESA EJEMPLO LTDA
[03-Jul-2025 04:25:32 UTC] Creando tabla si no existe: tb_inteletgroup_prospectos
[03-Jul-2025 04:25:32 UTC] SQL de creación: CREATE TABLE IF NOT EXISTS tb_inteletgroup_prospectos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        nombre_ejecutivo VARCHAR(255) NOT NULL,
        rut_cliente VARCHAR(20) NOT NULL,
        razon_social VARCHAR(255) NOT NULL,
        rubro VARCHAR(255) NOT NULL,
        direccion_comercial TEXT NOT NULL,
        telefono_celular VARCHAR(20) NOT NULL,
        email VARCHAR(255) NOT NULL,
        numero_pos VARCHAR(50),
        tipo_cuenta VARCHAR(50) NOT NULL,
        numero_cuenta_bancaria VARCHAR(50) NOT NULL,
        dias_atencion VARCHAR(100) NOT NULL,
        horario_atencion VARCHAR(100) NOT NULL,
        contrata_boleta VARCHAR(10) NOT NULL,
        competencia_actual VARCHAR(100) NOT NULL,
        fecha_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
[03-Jul-2025 04:25:32 UTC] Tabla verificada/creada correctamente
[03-Jul-2025 04:25:32 UTC] SQL Verificación: SELECT id FROM tb_inteletgroup_prospectos WHERE rut_cliente = ? con parámetro: ********-9
[03-Jul-2025 04:25:32 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method mysqli_stmt::get_result() in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:199
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 199
[03-Jul-2025 04:25:32 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 04:25:32 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 04:25:32 UTC] REQUEST_METHOD: POST
[03-Jul-2025 04:25:32 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 7
    [rol] => admin
    [nombre_usuario] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [proyecto] => inteletGroup
    [auth_token] => fed06501899b36d543ea0e9e7e6bb06d2186e66a0ed05c105a7214991c304e06
    [session_time] => 1751516717
    [last_activity] => 1751516720
)

[03-Jul-2025 04:25:32 UTC] POST: Array
(
    [nombre_ejecutivo] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [rut_cliente] => ********-9
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [documentos] => Array
        (
            [0] => [object File]
        )

    [usuario_id] => 7
    [timestamp] => *************
    [bypass_sw] => 1
)

[03-Jul-2025 04:25:32 UTC] GET: Array
(
    [t] => *************
    [r] => 17285bydx
)

[03-Jul-2025 04:25:32 UTC] Inicio procesamiento: 04:25:32
[03-Jul-2025 04:25:32 UTC] DATOS FORMULARIO: usuario_id=7, nombre=CATHERINE DEL CARMEN PINCHEIRA BRITO, rut=********-9
[03-Jul-2025 04:25:32 UTC] Datos recibidos - RUT: ********-9, Razón Social: EMPRESA EJEMPLO LTDA
[03-Jul-2025 04:25:32 UTC] Creando tabla si no existe: tb_inteletgroup_prospectos
[03-Jul-2025 04:25:32 UTC] SQL de creación: CREATE TABLE IF NOT EXISTS tb_inteletgroup_prospectos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        nombre_ejecutivo VARCHAR(255) NOT NULL,
        rut_cliente VARCHAR(20) NOT NULL,
        razon_social VARCHAR(255) NOT NULL,
        rubro VARCHAR(255) NOT NULL,
        direccion_comercial TEXT NOT NULL,
        telefono_celular VARCHAR(20) NOT NULL,
        email VARCHAR(255) NOT NULL,
        numero_pos VARCHAR(50),
        tipo_cuenta VARCHAR(50) NOT NULL,
        numero_cuenta_bancaria VARCHAR(50) NOT NULL,
        dias_atencion VARCHAR(100) NOT NULL,
        horario_atencion VARCHAR(100) NOT NULL,
        contrata_boleta VARCHAR(10) NOT NULL,
        competencia_actual VARCHAR(100) NOT NULL,
        fecha_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
[03-Jul-2025 04:25:32 UTC] Tabla verificada/creada correctamente
[03-Jul-2025 04:25:32 UTC] SQL Verificación: SELECT id FROM tb_inteletgroup_prospectos WHERE rut_cliente = ? con parámetro: ********-9
[03-Jul-2025 04:25:32 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method mysqli_stmt::get_result() in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:199
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 199
[03-Jul-2025 04:30:12 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 04:30:12 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 04:30:12 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 04:30:12 UTC] REQUEST_METHOD: POST
[03-Jul-2025 04:30:12 UTC] SESSION: Array
(
)

[03-Jul-2025 04:30:12 UTC] POST: Array
(
    [nombre_ejecutivo] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [rut_cliente] => ********-9
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [usuario_id] => 7
)

[03-Jul-2025 04:30:12 UTC] GET: Array
(
)

[03-Jul-2025 04:30:12 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 04:30:12 UTC] REQUEST_METHOD: POST
[03-Jul-2025 04:30:12 UTC] SESSION: Array
(
)

[03-Jul-2025 04:30:12 UTC] POST: Array
(
    [nombre_ejecutivo] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [rut_cliente] => ********-9
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [usuario_id] => 7
)

[03-Jul-2025 04:30:12 UTC] GET: Array
(
)

[03-Jul-2025 04:32:04 UTC] [2025-07-03 04:32:04] Error de autenticación - Usuario no identificado - IP: ************
[03-Jul-2025 04:32:05 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 04:32:28 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 04:32:30 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 04:33:20 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 04:33:20 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 04:33:20 UTC] REQUEST_METHOD: POST
[03-Jul-2025 04:33:20 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 7
    [rol] => admin
    [nombre_usuario] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [proyecto] => inteletGroup
    [auth_token] => 0ccba7d077281d1f2d5d6b3e8ef2b7433b7104370bbb87ae23f90c0328b01516
    [session_time] => **********
    [last_activity] => **********
)

[03-Jul-2025 04:33:20 UTC] POST: Array
(
    [nombre_ejecutivo] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [rut_cliente] => ********-9
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [usuario_id] => 7
    [timestamp] => *************
    [bypass_sw] => 1
)

[03-Jul-2025 04:33:20 UTC] GET: Array
(
    [t] => *************
    [r] => b3orrt23i
)

[03-Jul-2025 04:33:20 UTC] Inicio procesamiento: 04:33:20
[03-Jul-2025 04:33:20 UTC] DATOS FORMULARIO: usuario_id=7, nombre=CATHERINE DEL CARMEN PINCHEIRA BRITO, rut=********-9
[03-Jul-2025 04:33:20 UTC] Datos recibidos - RUT: ********-9, Razón Social: EMPRESA EJEMPLO LTDA
[03-Jul-2025 04:33:20 UTC] Creando tabla si no existe: tb_inteletgroup_prospectos
[03-Jul-2025 04:33:20 UTC] SQL de creación: CREATE TABLE IF NOT EXISTS tb_inteletgroup_prospectos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        nombre_ejecutivo VARCHAR(255) NOT NULL,
        rut_cliente VARCHAR(20) NOT NULL,
        razon_social VARCHAR(255) NOT NULL,
        rubro VARCHAR(255) NOT NULL,
        direccion_comercial TEXT NOT NULL,
        telefono_celular VARCHAR(20) NOT NULL,
        email VARCHAR(255) NOT NULL,
        numero_pos VARCHAR(50),
        tipo_cuenta VARCHAR(50) NOT NULL,
        numero_cuenta_bancaria VARCHAR(50) NOT NULL,
        dias_atencion VARCHAR(100) NOT NULL,
        horario_atencion VARCHAR(100) NOT NULL,
        contrata_boleta VARCHAR(10) NOT NULL,
        competencia_actual VARCHAR(100) NOT NULL,
        fecha_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
[03-Jul-2025 04:33:20 UTC] Tabla verificada/creada correctamente
[03-Jul-2025 04:33:20 UTC] SQL Verificación: SELECT id FROM tb_inteletgroup_prospectos WHERE rut_cliente = ? con parámetro: ********-9
[03-Jul-2025 04:33:20 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method mysqli_stmt::get_result() in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:199
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 199
[03-Jul-2025 04:33:20 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 04:33:20 UTC] === INTELETGROUP PROSPECTO DEBUG ===
[03-Jul-2025 04:33:20 UTC] REQUEST_METHOD: POST
[03-Jul-2025 04:33:20 UTC] SESSION: Array
(
    [usuario] => <EMAIL>
    [usuario_id] => 7
    [rol] => admin
    [nombre_usuario] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [proyecto] => inteletGroup
    [auth_token] => 0ccba7d077281d1f2d5d6b3e8ef2b7433b7104370bbb87ae23f90c0328b01516
    [session_time] => **********
    [last_activity] => **********
)

[03-Jul-2025 04:33:20 UTC] POST: Array
(
    [nombre_ejecutivo] => CATHERINE DEL CARMEN PINCHEIRA BRITO
    [rut_cliente] => ********-9
    [razon_social] => EMPRESA EJEMPLO LTDA
    [rubro] => COMERCIO AL POR MENOR
    [direccion_comercial] => AV PROVIDENCIA 123, PROVIDENCIA, SANTIAGO
    [telefono_celular] => *********
    [email] => <EMAIL>
    [numero_pos] => POS123456
    [tipo_cuenta] => Cuenta Corriente
    [numero_cuenta_bancaria] => ********
    [dias_atencion] => LUNES A VIERNES
    [horario_atencion] => 09:00 - 18:00
    [contrata_boleta] => Si
    [competencia_actual] => Transbank
    [usuario_id] => 7
    [timestamp] => *************
    [bypass_sw] => 1
)

[03-Jul-2025 04:33:20 UTC] GET: Array
(
    [t] => *************
    [r] => b3orrt23i
)

[03-Jul-2025 04:33:20 UTC] Inicio procesamiento: 04:33:20
[03-Jul-2025 04:33:20 UTC] DATOS FORMULARIO: usuario_id=7, nombre=CATHERINE DEL CARMEN PINCHEIRA BRITO, rut=********-9
[03-Jul-2025 04:33:20 UTC] Datos recibidos - RUT: ********-9, Razón Social: EMPRESA EJEMPLO LTDA
[03-Jul-2025 04:33:20 UTC] Creando tabla si no existe: tb_inteletgroup_prospectos
[03-Jul-2025 04:33:20 UTC] SQL de creación: CREATE TABLE IF NOT EXISTS tb_inteletgroup_prospectos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        nombre_ejecutivo VARCHAR(255) NOT NULL,
        rut_cliente VARCHAR(20) NOT NULL,
        razon_social VARCHAR(255) NOT NULL,
        rubro VARCHAR(255) NOT NULL,
        direccion_comercial TEXT NOT NULL,
        telefono_celular VARCHAR(20) NOT NULL,
        email VARCHAR(255) NOT NULL,
        numero_pos VARCHAR(50),
        tipo_cuenta VARCHAR(50) NOT NULL,
        numero_cuenta_bancaria VARCHAR(50) NOT NULL,
        dias_atencion VARCHAR(100) NOT NULL,
        horario_atencion VARCHAR(100) NOT NULL,
        contrata_boleta VARCHAR(10) NOT NULL,
        competencia_actual VARCHAR(100) NOT NULL,
        fecha_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
[03-Jul-2025 04:33:20 UTC] Tabla verificada/creada correctamente
[03-Jul-2025 04:33:20 UTC] SQL Verificación: SELECT id FROM tb_inteletgroup_prospectos WHERE rut_cliente = ? con parámetro: ********-9
[03-Jul-2025 04:33:20 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method mysqli_stmt::get_result() in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php:199
Stack trace:
#0 {main}
  thrown in /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php on line 199
[03-Jul-2025 04:34:20 UTC] Usando configuración de producción para base de datos
[03-Jul-2025 04:35:46 UTC] DB ERROR: Error de conexión (mysqli): Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) (Código: 1045)
[03-Jul-2025 04:35:46 UTC] DB ERROR: Excepción en conexión: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES)
[03-Jul-2025 04:35:46 UTC] PHP Fatal error:  Uncaught Exception: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) in /home/<USER>/public_html/intranet/dist/con_db.php:78
Stack trace:
#0 /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php(26): require_once()
#1 {main}
  thrown in /home/<USER>/public_html/intranet/dist/con_db.php on line 78
[03-Jul-2025 04:36:24 UTC] DB ERROR: Error de conexión (mysqli): Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) (Código: 1045)
[03-Jul-2025 04:36:24 UTC] DB ERROR: Excepción en conexión: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES)
[03-Jul-2025 04:36:24 UTC] PHP Fatal error:  Uncaught Exception: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) in /home/<USER>/public_html/intranet/dist/con_db.php:78
Stack trace:
#0 /home/<USER>/public_html/intranet/dist/form_inteletgroup.php(45): require_once()
#1 {main}
  thrown in /home/<USER>/public_html/intranet/dist/con_db.php on line 78
[03-Jul-2025 04:36:26 UTC] DB ERROR: Error de conexión (mysqli): Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) (Código: 1045)
[03-Jul-2025 04:36:26 UTC] DB ERROR: Excepción en conexión: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES)
[03-Jul-2025 04:36:26 UTC] PHP Fatal error:  Uncaught Exception: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) in /home/<USER>/public_html/intranet/dist/con_db.php:78
Stack trace:
#0 /home/<USER>/public_html/intranet/dist/form_inteletgroup.php(45): require_once()
#1 {main}
  thrown in /home/<USER>/public_html/intranet/dist/con_db.php on line 78
[03-Jul-2025 04:36:48 UTC] DB ERROR: Error de conexión (mysqli): Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) (Código: 1045)
[03-Jul-2025 04:36:48 UTC] DB ERROR: Excepción en conexión: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES)
[03-Jul-2025 04:36:48 UTC] PHP Fatal error:  Uncaught Exception: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) in /home/<USER>/public_html/intranet/dist/con_db.php:78
Stack trace:
#0 /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php(26): require_once()
#1 {main}
  thrown in /home/<USER>/public_html/intranet/dist/con_db.php on line 78
[03-Jul-2025 04:36:54 UTC] DB ERROR: Error de conexión (mysqli): Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) (Código: 1045)
[03-Jul-2025 04:36:54 UTC] DB ERROR: Excepción en conexión: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES)
[03-Jul-2025 04:36:54 UTC] PHP Fatal error:  Uncaught Exception: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) in /home/<USER>/public_html/intranet/dist/con_db.php:78
Stack trace:
#0 /home/<USER>/public_html/intranet/dist/login.php(3): require_once()
#1 {main}
  thrown in /home/<USER>/public_html/intranet/dist/con_db.php on line 78
[03-Jul-2025 04:37:37 UTC] DB ERROR: Error de conexión (mysqli): Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) (Código: 1045)
[03-Jul-2025 04:37:37 UTC] DB ERROR: Excepción en conexión: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES)
[03-Jul-2025 04:37:37 UTC] PHP Fatal error:  Uncaught Exception: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) in /home/<USER>/public_html/intranet/dist/con_db.php:78
Stack trace:
#0 /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php(26): require_once()
#1 {main}
  thrown in /home/<USER>/public_html/intranet/dist/con_db.php on line 78
[03-Jul-2025 04:37:37 UTC] DB ERROR: Error de conexión (mysqli): Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) (Código: 1045)
[03-Jul-2025 04:37:37 UTC] DB ERROR: Excepción en conexión: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES)
[03-Jul-2025 04:37:37 UTC] PHP Fatal error:  Uncaught Exception: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) in /home/<USER>/public_html/intranet/dist/con_db.php:78
Stack trace:
#0 /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php(26): require_once()
#1 {main}
  thrown in /home/<USER>/public_html/intranet/dist/con_db.php on line 78
[03-Jul-2025 04:38:16 UTC] DB ERROR: Error de conexión (mysqli): Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) (Código: 1045)
[03-Jul-2025 04:38:16 UTC] DB ERROR: Excepción en conexión: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES)
[03-Jul-2025 04:38:16 UTC] PHP Fatal error:  Uncaught Exception: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) in /home/<USER>/public_html/intranet/dist/con_db.php:78
Stack trace:
#0 /home/<USER>/public_html/intranet/dist/form_inteletgroup.php(45): require_once()
#1 {main}
  thrown in /home/<USER>/public_html/intranet/dist/con_db.php on line 78
[03-Jul-2025 04:38:25 UTC] DB ERROR: Error de conexión (mysqli): Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) (Código: 1045)
[03-Jul-2025 04:38:25 UTC] DB ERROR: Excepción en conexión: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES)
[03-Jul-2025 04:38:25 UTC] PHP Fatal error:  Uncaught Exception: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) in /home/<USER>/public_html/intranet/dist/con_db.php:78
Stack trace:
#0 /home/<USER>/public_html/intranet/dist/form_inteletgroup.php(45): require_once()
#1 {main}
  thrown in /home/<USER>/public_html/intranet/dist/con_db.php on line 78
[03-Jul-2025 04:39:05 UTC] DB ERROR: Error de conexión (mysqli): Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) (Código: 1045)
[03-Jul-2025 04:39:05 UTC] DB ERROR: Excepción en conexión: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES)
[03-Jul-2025 04:39:05 UTC] PHP Fatal error:  Uncaught Exception: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) in /home/<USER>/public_html/intranet/dist/con_db.php:78
Stack trace:
#0 /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php(26): require_once()
#1 {main}
  thrown in /home/<USER>/public_html/intranet/dist/con_db.php on line 78
[03-Jul-2025 04:39:05 UTC] DB ERROR: Error de conexión (mysqli): Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) (Código: 1045)
[03-Jul-2025 04:39:05 UTC] DB ERROR: Excepción en conexión: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES)
[03-Jul-2025 04:39:05 UTC] PHP Fatal error:  Uncaught Exception: Error de conexión a la base de datos: Access denied for user 'gestarse_ncornejo7_experian'@'mail.gestarservicios.cl' (using password: YES) in /home/<USER>/public_html/intranet/dist/con_db.php:78
Stack trace:
#0 /home/<USER>/public_html/intranet/dist/guardar_inteletgroup_prospecto.php(26): require_once()
#1 {main}
  thrown in /home/<USER>/public_html/intranet/dist/con_db.php on line 78
