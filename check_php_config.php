<?php
// Verificar configuración PHP para carga de archivos
echo "<h2>Configuración PHP para Carga de Archivos</h2>";

$config_items = [
    'file_uploads' => 'Carga de archivos habilitada',
    'upload_max_filesize' => 'Tamaño máximo de archivo',
    'max_file_uploads' => 'Número máximo de archivos',
    'post_max_size' => 'Tamaño máximo POST',
    'max_execution_time' => 'Tiempo máximo de ejecución',
    'memory_limit' => 'Límite de memoria',
    'upload_tmp_dir' => 'Directorio temporal de uploads'
];

echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>Configuración</th><th>Valor</th><th>Descripción</th></tr>";

foreach ($config_items as $key => $description) {
    $value = ini_get($key);
    echo "<tr>";
    echo "<td><strong>$key</strong></td>";
    echo "<td>" . ($value === false ? 'No definido' : htmlspecialchars($value)) . "</td>";
    echo "<td>$description</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>Información adicional:</h3>";
echo "<p><strong>Directorio de trabajo actual:</strong> " . getcwd() . "</p>";
echo "<p><strong>Usuario del servidor web:</strong> " . get_current_user() . "</p>";
echo "<p><strong>Versión de PHP:</strong> " . phpversion() . "</p>";

// Verificar permisos del directorio de uploads
$upload_dir = 'uploads/inteletgroup_prospectos/';
echo "<h3>Verificación del directorio de uploads:</h3>";
echo "<p><strong>Directorio:</strong> $upload_dir</p>";
echo "<p><strong>Existe:</strong> " . (is_dir($upload_dir) ? 'Sí' : 'No') . "</p>";
echo "<p><strong>Es escribible:</strong> " . (is_writable($upload_dir) ? 'Sí' : 'No') . "</p>";
echo "<p><strong>Permisos:</strong> " . (file_exists($upload_dir) ? substr(sprintf('%o', fileperms($upload_dir)), -4) : 'N/A') . "</p>";

// Verificar si el directorio se puede crear
if (!is_dir($upload_dir)) {
    echo "<p><strong>Intentando crear directorio...</strong></p>";
    if (mkdir($upload_dir, 0755, true)) {
        echo "<p style='color: green;'>✓ Directorio creado exitosamente</p>";
    } else {
        echo "<p style='color: red;'>✗ Error al crear directorio</p>";
    }
}
?>
