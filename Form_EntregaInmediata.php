<?php


// Al principio de tu archivo PHP (después de iniciar sesión)
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > 60)) {
  // 1800 segundos (30 minutos) de inactividad
  session_unset();
  session_destroy();
  header("Location: login.php");
  exit();
}

$_SESSION['last_activity'] = time();


// Conexión a la base de datos
$inc = include("con_db.php");
session_start();


$ejecutivo = $_SESSION['usuario'];

$resultado = $conex->query(

  "SELECT * 
      FROM  TB_VENTA_GETNET_INMEDIATA
    where rut_ejecutivo = '" . $ejecutivo . "'
  "
);




?>

<!DOCTYPE html>
<html lang="en">

<head>

  <style>
    .hidden-elements {
      display: none;
    }
  </style>



  <style>
    .h-100 li a,
    .h-100 li span {
      color: white;
    }
  </style>


  <meta http-equiv="Expires" content="0">
  <meta http-equiv="Last-Modified" content="0">
  <meta http-equiv="Cache-Control" content="no-cache, mustrevalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Affan - PWA Mobile HTML Template">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>Seven APP</title>

  <!-- Favicon -->

  <link rel="stylesheet" href="style.css?v=2">

  <link rel="icon" href="img/icons/Logo.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card -->
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>


      </div>
    </div>
  </div>

  <!-- Offcanvas Start -->
  <div class="offcanvas offcanvas-start" id="affanOffcanvas" data-bs-scroll="true" tabindex="-1"
    aria-labelledby="affanOffcanvsLabel">

    <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
      aria-label="Close"></button>

    <div class="offcanvas-body p-0">
      <div class="sidenav-wrapper">
        <!-- Sidenav Profile -->
        <div class="sidenav-profile bg-gradient" style="background-color: #ea4c62 !important;">
          <div class="sidenav-style1"></div>

          <!-- User Thumbnail -->
          <div class="user-profile">
            <img src="img/bg-img/2.jpg" alt="">
          </div>

          <!-- User Info -->
          <div class="user-info">
            <h6 class="user-name mb-0">Bienvenido</h6>
            <span>
              <?php echo $_SESSION['ejecutivo'] ?>
            </span>
          </div>
        </div>

        <!-- Sidenav Nav -->
        <ul class="sidenav-nav ps-0">
          <li>
            <a href="home.html"><i class="bi bi-house-door"></i> Home</a>
          </li>


          <li>
            <a href="login.php"><i class="bi bi-box-arrow-right"></i> Cerrar sesión</a>
          </li>
        </ul>

        <!-- Social Info -->
        <div class="social-info-wrap">
          <a href="#">
            <i class="bi bi-facebook"></i>
          </a>
          <a href="#">
            <i class="bi bi-twitter"></i>
          </a>
          <a href="#">
            <i class="bi bi-linkedin"></i>
          </a>
        </div>

        <!-- Copyright Info -->
        <div class="copyright-info">
          <p>
            <span id="copyrightYear"></span>
            &copy; Made by <a href="#">Kayze Tecnología</a>
          </p>
        </div>
      </div>
    </div>
  </div>


  <!-- Header Area -->
  <div class="header-area" id="headerArea" style="
    background-color: #ea4c62 !important;
">
    <div class="container">
      <!-- Header Content -->
      <div class="header-content position-relative d-flex align-items-center justify-content-between">
        <!-- Back Button -->
        <!-- <div class="back-button">
          <a href="elements.html">
            <i class="bi bi-arrow-left-short"></i>
          </a>
        </div> -->

        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0" style="color: white;"> Ingreso</h6>
        </div>

        <!-- Settings -->
        <div class="logo-wrapper">
          <a href="home.html">
            <img src="img/core-img/logo.png" alt="">
          </a>
        </div>

        <!-- Navbar Toggler -->
        <div class="navbar--toggler" id="affanNavbarToggler" data-bs-toggle="offcanvas" data-bs-target="#affanOffcanvas"
          aria-controls="affanOffcanvas">
          <span style="color: white;" class="d-block"></span>
          <span style="color: white;" class="d-block"></span>
          <span style="color: white;" class="d-block"></span>
        </div>

      </div>
    </div>
  </div>

  <div class="page-content-wrapper py-3" id="elementsSearchList">
    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading">
        <h6>Formulario ingreso venta ENTREGA INMEDIATA</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">


         



          <form id="uploadForm" action="Action_FotoFirma.php" method="POST" enctype="multipart/form-data">



          <div class="form-group">
            <label class="form-label" for="fecha_venta">Fecha de entrega</label>
            <input class="form-control" id="fecha_venta" name="fecha_venta" type="date" placeholder="Fecha de Venta"
              readonly>
          </div>

          <div class="form-group">
              <label class="form-label" for="cliente_firma">¿Cliente Firma? (Si/No)</label>
              <select class="form-control" id="cliente_firma" name="cliente_firma">
                <option value="" selected>Seleccionar</option>
                <option value="Si">Si</option>
                <option value="No">No</option>
              </select>
            </div>

            <label class="form-label" for="rut_comercio">RUT Razon Social</label>
            <input class="form-control" id="rut_comercio" name="rut_comercio" type="text" placeholder="XX.XXX.XXX-X"
              required>
            <span id="rut-validation-msg" class="text-danger"></span>

            <label for="foto">Foto</label>
            <input class="form-control" type="file" id="userfile" name="userfile" onchange="uploadAndRedirect()" required><br>
            <input type="submit" value="Subir Foto y Redireccionar">
          </form>

          <script>
            function uploadAndRedirect() {
              var fotoInput = document.getElementById("userfile");

              if (fotoInput.files.length > 0) {
                var uploadForm = document.getElementById("uploadForm");
                uploadForm.submit();
              }
            }
          </script>



        </div>
      </div>
    </div>
  </div>



  <div class="container">
    <div class="card">
      <div class="card-body">
        <table class="table mb-0 table-striped">
          <thead>
            <tr>

              <th>Fecha de Venta</th>
              <th>Cliente Firma</th>
              <th>Nombre Comercio</th>
              <th>RUT Comercio</th>
              <th>Archivo</th>
            </tr>
          </thead>
          <tbody>
            <?php while ($rowtkpi = mysqli_fetch_array($resultado)) {
              echo '<tr>
             
                <td>' . $rowtkpi['fecha_venta'] . '</td>
                <td>' . $rowtkpi['cliente_firma'] . '</td>
                <td>' . $rowtkpi['nombre_comercio'] . '</td>
                <td>' . $rowtkpi['rut_comercio'] . '</td>
                <td>' . $rowtkpi['archivo'] . '</td>
            </tr>';
            } ?>
          </tbody>

        </table>
      </div>
    </div>
  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav" style="background-color: #ea4c62 !important;">
    <div class="container px-0" style="background-color: #ea4c62 !important;">
      <!-- Footer Content -->
      <div style="background-color: #ea4c62 !important;"
        class="footer-nav position-relative footer-style-nine bg-danger">

        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          <li>
            <a href="Form_EntregaInmediata.php">
              <i class="bi bi-house"></i>
              <span style="color: white;">Home</span>
            </a>
          </li>
          <li>
            <a href="">
              <i class="bi bi-house"></i>
              <span style="color: white;">Graficos</span>
            </a>
          </li>
          <li class="active">
            <a href="Form_EntregaInmediata.php">
              <i class="bi bi-collection"></i>
              <span style="color: white;">Formulario Entrega Inmediata</span>
            </a>
          </li>
          <li>
            <a href="">
              <i class="bi bi-folder2-open"></i>
              <span style="color: white;">Calculadora</span>
            </a>
          </li>
        </ul>

      </div>
    </div>
  </div>

  <!-- 
  <script>
  // Obtener elementos del DOM
  const video = document.getElementById("video");
  const canvas = document.getElementById("canvas");
  const capturedImagePreview = document.getElementById("capturedImagePreview");
  const captureButton = document.getElementById("capture");
  const imageDataInput = document.getElementById("imageData");

  // Acceder a la cámara del dispositivo
  navigator.mediaDevices.getUserMedia({ video: true })
    .then((stream) => {
      video.srcObject = stream;
    })
    .catch((error) => {
      console.error("Error al acceder a la cámara: ", error);
    });

  // Función para tomar la foto y guardarla temporalmente
  captureButton.addEventListener("click", () => {
    canvas.getContext("2d").drawImage(video, 0, 0, canvas.width, canvas.height);
    const imageData = canvas.toDataURL("image/jpeg"); // Obtener datos de la imagen en formato base64
    capturedImagePreview.src = imageData;
    capturedImagePreview.style.display = "block";
    imageDataInput.value = imageData; // Almacenar los datos de la imagen en el campo oculto
  });

  // Función para enviar el formulario con la imagen guardada
  function saveImageAndSubmitForm() {
    const form = document.getElementById("myForm");
    form.submit();
  }
</script> -->

  <script>

    document.getElementById("miFormulario").addEventListener("submit", function (event) {
      if (document.getElementById("enviarFormulario").disabled) {
        event.preventDefault(); // Evitar que el formulario se envíe
        alert("Por favor, corrija los errores antes de enviar el formulario.");
      }
    });


  </script>


  <script>
    // Obtener el elemento de entrada del Nombre Razon Social
    var nombreComercioInput = document.getElementById("nombre_comercio");

    // Asignar la función de cambio a mayúsculas al evento "input"
    nombreComercioInput.addEventListener("input", function () {
      this.value = this.value.toUpperCase();
    });
  </script>


  <script>
    function validarYFormatearRUT(rutInput) {
      var rut = rutInput.value.replace(/\./g, "").replace(/-/g, "");
      var cuerpoRut = rut.slice(0, -1);
      var digitoVerificador = rut.slice(-1).toUpperCase();

      if (cuerpoRut && digitoVerificador) {
        var rutConPuntos = cuerpoRut.replace(/\B(?=(\d{3})+(?!\d))/g, ".");
        rutInput.value = rutConPuntos + "-" + digitoVerificador;

        var suma = 0;
        var multiplo = 2;
        for (var i = cuerpoRut.length - 1; i >= 0; i--) {
          suma += parseInt(cuerpoRut.charAt(i)) * multiplo;
          multiplo = multiplo === 7 ? 2 : multiplo + 1;
        }
        var digitoCalculado = 11 - (suma % 11);
        var digitoEsperado = digitoCalculado === 10 ? "K" : digitoCalculado === 11 ? "0" : digitoCalculado.toString();

        if (digitoEsperado !== digitoVerificador) {
          document.getElementById("rut-validation-msg").textContent = "RUT inválido";
          document.getElementById("enviarFormulario").disabled = true; // Deshabilitar el botón
        } else {
          document.getElementById("rut-validation-msg").textContent = "";
          document.getElementById("enviarFormulario").disabled = false; // Habilitar el botón
        }

      } else {
        document.getElementById("rut-validation-msg").textContent = "RUT inválido";
      }
    }

    var rutInput = document.getElementById("rut_comercio");
    rutInput.addEventListener("input", function () {
      validarYFormatearRUT(rutInput);
    });
  </script>



  <script>
    function handleFirmaFileChange() {
      var firmaInput = document.getElementById("firma");
      var hiddenElements = document.querySelectorAll(".hidden-elements");

      if (firmaInput.files.length > 0) {
        for (var i = 0; i < hiddenElements.length; i++) {
          hiddenElements[i].style.display = "block";
        }
      } else {
        for (var i = 0; i < hiddenElements.length; i++) {
          hiddenElements[i].style.display = "none";
        }
      }
    }
  </script>



  <script>
    // Obtenemos la referencia al elemento input de fecha
    const fechaVentaInput = document.getElementById('fecha_venta');

    // Creamos una nueva fecha con la fecha y hora actual
    const fechaActual = new Date();

    // Formateamos la fecha actual en formato 'YYYY-MM-DD' para asignarla al input
    const fechaFormateada = fechaActual.toISOString().slice(0, 10);

    // Asignamos la fecha formateada al valor del input
    fechaVentaInput.value = fechaFormateada;
  </script>


  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script>
    // Function to perform AJAX validation when the user types in the input field
    function validateSerie() {
      // Get the value entered in the "SERIE EQUIPO" field
      var serie_equipo = $('#serie').val();

      // Perform the AJAX request to validate the data on the server
      $.ajax({
        method: 'POST',
        url: 'validar_serie.php',
        data: { serie_equipo: serie_equipo },
        success: function (response) {
          // The server's response is processed here
          if (response === 'existe') {
            $('#resultado').text('El número de serie existe en la tabla.');
          } if (response === 'error') {
            $('#resultado').text('El número de serie NO existe en la tabla.');
          }
        },
        error: function (xhr, status, error) {
          console.log('Error al realizar la solicitud AJAX: ' + error);
          $('#resultado').text('Error al realizar la solicitud AJAX.');
        }
      });
    }

    // Bind the "input" event to the input field to trigger validation when the user types
    $('#serie').on('input', validateSerie);
  </script>





  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
</body>

</html>