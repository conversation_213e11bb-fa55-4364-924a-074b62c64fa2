<?php
// Conexión a la base de datos


$inc = include("con_db.php");
session_start();

// Obtener los datos del formulario
$orden = $_POST['Orden'];
$categoria = $_POST['Categoria'];
$Obse = $_POST['Obse'];
$usuario = $_POST['usuario'];
$aplica = $_POST['Aplica'];

// Realizar la consulta SQL para insertar los datos en la tabla
$sql = "INSERT INTO TB_MYSQL_SOPORTE_GESTION_CALIDAD 
(orden, categoria,aplica,observacion,usuario,Fecha_reg) 
VALUES ('$orden', '$categoria','$aplica', '$Obse','$usuario',NOW())";

// Ejecutar la consulta
if (mysqli_query($conex, $sql)) {
    echo "<script>alert('Datos guardados correctamente')</script>";

    ?>

<script type="text/javascript">
    window.location.replace("SoporteCalidad.php?usuario=<?php echo $usuario; ?>");
</script>

                                <?php  

} else {
    echo "Error al guardar los datos: " . mysqli_error($conex);
}

// Cerrar la conexión
mysqli_close($conex);
?>