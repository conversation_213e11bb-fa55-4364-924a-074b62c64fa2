<?php
// Configurar encabezados para permitir CORS y especificar el tipo de contenido
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Max-Age: 3600');

// Incluir el archivo de conexión a la base de datos
// Usar dirname para obtener la ruta absoluta al directorio padre
require_once dirname(__FILE__) . '/../con_db.php';

// Iniciar sesión si no está iniciada
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verificar si el usuario está autenticado
if (!isset($_SESSION['usuario_id'])) {
    echo json_encode(['success' => false, 'message' => 'Usuario no autenticado']);
    exit;
}

// Verificar si se proporcionó el RUT
if (!isset($_GET['rut']) || empty($_GET['rut'])) {
    echo json_encode(['success' => false, 'message' => 'RUT no proporcionado']);
    exit;
}

$rut_ejecutivo = trim($_GET['rut']);

try {
    // Registrar información de depuración
    error_log("Intentando obtener bitácora para RUT: " . $rut_ejecutivo);

    // Establecer conexión a la base de datos usando PDO
    try {
        // Configuración de la conexión a la base de datos
        $host = 'localhost';
        $port = 3306;
        $dbname = 'gestarse_experian';
        $username = 'gestarse_ncornejo7_experian';
        $password = 'N1c0l7as17';

        $connection = new PDO(
            "mysql:host=$host;dbname=$dbname;charset=utf8",
            $username,
            $password,
            array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
        );
        error_log("Conexión a la base de datos establecida correctamente");
    } catch (PDOException $e) {
        error_log("Error al conectar a la base de datos: " . $e->getMessage());
        throw new Exception("No se pudo establecer conexión con la base de datos: " . $e->getMessage());
    }

    // Preparar la consulta SQL
    $query = "SELECT b.*, COALESCE(u.nombre_usuario, SUBSTRING_INDEX(u.correo, '@', 1)) AS nombre_usuario
              FROM tb_experian_prospecto_bitacora b
              LEFT JOIN tb_experian_usuarios u ON b.usuario_id = u.id
              WHERE b.rut_ejecutivo = :rut_ejecutivo
              ORDER BY b.fecha_registro DESC";

    $stmt = $connection->prepare($query);

    // Vincular parámetros
    $stmt->bindParam(':rut_ejecutivo', $rut_ejecutivo);

    // Registrar la consulta SQL para depuración
    error_log("Consulta SQL: " . $query);
    error_log("Parámetro RUT: " . $rut_ejecutivo);

    // Ejecutar la consulta
    $stmt->execute();

    error_log("Consulta ejecutada correctamente");

    // Obtener resultados
    $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);

    error_log("Número de registros obtenidos: " . count($registros));

    // Determinar el último estado registrado
    $ultimo_estado = !empty($registros) ? $registros[0]['estado'] : '';

    // Devolver respuesta
    echo json_encode([
        'success' => true,
        'data' => $registros,
        'ultimo_estado' => $ultimo_estado
    ]);

} catch (Exception $e) {
    // Registrar el error con más detalles
    error_log("Error al obtener bitácora: " . $e->getMessage());
    error_log("Traza de la excepción: " . $e->getTraceAsString());

    // Verificar si la tabla existe
    try {
        $checkTable = $connection->query("SHOW TABLES LIKE 'tb_experian_prospecto_bitacora'");
        $tableExists = $checkTable && $checkTable->rowCount() > 0;
        error_log("La tabla tb_experian_prospecto_bitacora " . ($tableExists ? "existe" : "no existe"));
    } catch (Exception $ex) {
        error_log("Error al verificar si la tabla existe: " . $ex->getMessage());
    }

    // Devolver respuesta de error
    echo json_encode([
        'success' => false,
        'message' => 'Error al obtener los registros: ' . $e->getMessage(),
        'debug_info' => [
            'rut' => $rut_ejecutivo,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
}
?>
