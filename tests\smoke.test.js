const { test, expect } = require('@playwright/test');

test('Verificar que la página de login carga correctamente', async ({ page }) => {
  // Navegar a la página de inicio de sesión
  await page.goto('http://localhost/intranet/dist/login.php');
  
  // Verificar que el título de la página es el esperado
  const title = await page.title();
  console.log('Título de la página:', title);
  
  // Verificar que el formulario de login está presente
  await expect(page.locator('input[name="rut"]')).toBeVisible();
  await expect(page.locator('input[name="clave"]')).toBeVisible();
  
  // Tomar una captura de pantalla
  await page.screenshot({ path: 'screenshots/login-page.png' });
});
