<?php
// Script para limpiar datos de prueba de InteletGroup
// USAR CON PRECAUCIÓN - SOLO PARA DESARROLLO

// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    die('Acceso denegado. Solo usuarios de InteletGroup pueden ejecutar este script.');
}

// Incluir archivo de conexión a la base de datos
require_once 'con_db.php';

// Usar la conexión mysqli del archivo con_db.php
$conexion = $mysqli;

// Verificar que la conexión se estableció correctamente
if (!isset($conexion) || $conexion->connect_error) {
    die('Error de conexión a la base de datos: ' . (isset($conexion) ? $conexion->connect_error : 'No se pudo establecer la conexión'));
}

$mensaje = '';
$error = '';

// Procesar formulario
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $accion = $_POST['accion'] ?? '';
    
    try {
        $conexion->begin_transaction();
        
        switch ($accion) {
            case 'limpiar_todo':
                // Eliminar todos los registros de prueba
                $sql1 = "DELETE FROM tb_inteletgroup_bitacora";
                $sql2 = "DELETE FROM tb_inteletgroup_prospectos";
                
                $conexion->query($sql1);
                $conexion->query($sql2);
                
                // Resetear auto_increment
                $conexion->query("ALTER TABLE tb_inteletgroup_bitacora AUTO_INCREMENT = 1");
                $conexion->query("ALTER TABLE tb_inteletgroup_prospectos AUTO_INCREMENT = 1");
                
                $mensaje = "Todos los datos de prueba han sido eliminados.";
                break;
                
            case 'limpiar_recientes':
                // Eliminar solo registros de las últimas 24 horas
                $sql1 = "DELETE FROM tb_inteletgroup_bitacora WHERE fecha_registro >= DATE_SUB(NOW(), INTERVAL 24 HOUR)";
                $sql2 = "DELETE FROM tb_inteletgroup_prospectos WHERE fecha_registro >= DATE_SUB(NOW(), INTERVAL 24 HOUR)";
                
                $result1 = $conexion->query($sql1);
                $result2 = $conexion->query($sql2);
                
                $affected = $conexion->affected_rows;
                $mensaje = "Se eliminaron $affected registros de las últimas 24 horas.";
                break;
                
            case 'limpiar_rut':
                $rut = trim($_POST['rut'] ?? '');
                if (empty($rut)) {
                    throw new Exception('Debe especificar un RUT');
                }
                
                // Eliminar registros específicos por RUT
                $sql1 = "DELETE b FROM tb_inteletgroup_bitacora b 
                        INNER JOIN tb_inteletgroup_prospectos p ON b.prospecto_id = p.id 
                        WHERE p.rut_cliente = ?";
                $sql2 = "DELETE FROM tb_inteletgroup_prospectos WHERE rut_cliente = ?";
                
                $stmt1 = $conexion->prepare($sql1);
                $stmt1->bind_param("s", $rut);
                $stmt1->execute();
                
                $stmt2 = $conexion->prepare($sql2);
                $stmt2->bind_param("s", $rut);
                $stmt2->execute();
                
                $affected = $stmt2->affected_rows;
                $mensaje = "Se eliminaron $affected registros con RUT: $rut";
                break;
                
            default:
                throw new Exception('Acción no válida');
        }
        
        $conexion->commit();
        
    } catch (Exception $e) {
        $conexion->rollback();
        $error = 'Error: ' . $e->getMessage();
    }
}

// Obtener estadísticas actuales
$stats = [];
try {
    $result = $conexion->query("SELECT COUNT(*) as total FROM tb_inteletgroup_prospectos");
    $stats['total_prospectos'] = $result->fetch_assoc()['total'];
    
    $result = $conexion->query("SELECT COUNT(*) as total FROM tb_inteletgroup_bitacora");
    $stats['total_bitacora'] = $result->fetch_assoc()['total'];
    
    $result = $conexion->query("SELECT COUNT(*) as recientes FROM tb_inteletgroup_prospectos WHERE fecha_registro >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $stats['recientes'] = $result->fetch_assoc()['recientes'];
    
} catch (Exception $e) {
    $error = 'Error al obtener estadísticas: ' . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Limpiar Datos de Prueba - InteletGroup</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h4 class="mb-0">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            Limpiar Datos de Prueba - InteletGroup
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if ($mensaje): ?>
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle me-2"></i>
                                <?php echo htmlspecialchars($mensaje); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-circle me-2"></i>
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="alert alert-warning">
                            <strong>¡ATENCIÓN!</strong> Este script eliminará datos de la base de datos. 
                            Usar solo en ambiente de desarrollo.
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h5><?php echo $stats['total_prospectos'] ?? 0; ?></h5>
                                        <small>Total Prospectos</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-secondary text-white">
                                    <div class="card-body text-center">
                                        <h5><?php echo $stats['total_bitacora'] ?? 0; ?></h5>
                                        <small>Total Bitácora</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-warning text-dark">
                                    <div class="card-body text-center">
                                        <h5><?php echo $stats['recientes'] ?? 0; ?></h5>
                                        <small>Últimas 24h</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <form method="POST" onsubmit="return confirm('¿Está seguro de que desea continuar?');">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <button type="submit" name="accion" value="limpiar_recientes" class="btn btn-warning w-100">
                                        <i class="bi bi-clock me-2"></i>
                                        Limpiar Últimas 24h
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button type="submit" name="accion" value="limpiar_todo" class="btn btn-danger w-100">
                                        <i class="bi bi-trash me-2"></i>
                                        Limpiar Todo
                                    </button>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="row">
                                <div class="col-md-8">
                                    <input type="text" name="rut" class="form-control" placeholder="RUT específico (ej: 12345678-9)" pattern="^\d{7,8}-[\dkK]$">
                                </div>
                                <div class="col-md-4">
                                    <button type="submit" name="accion" value="limpiar_rut" class="btn btn-outline-danger w-100">
                                        <i class="bi bi-person-x me-2"></i>
                                        Eliminar RUT
                                    </button>
                                </div>
                            </div>
                        </form>
                        
                        <hr>
                        
                        <div class="text-center">
                            <a href="form_inteletgroup.php" class="btn btn-primary">
                                <i class="bi bi-arrow-left me-2"></i>
                                Volver al Panel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
