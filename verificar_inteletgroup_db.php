<?php
// Script para verificar y crear las tablas de InteletGroup
header('Content-Type: application/json');

// Incluir conexión
require_once __DIR__ . '/con_db.php';

try {
    // Usar la conexión mysqli del archivo con_db.php
    $conexion = $mysqli;
    
    echo json_encode([
        'step' => 'Conexión establecida',
        'success' => true,
        'server_info' => $conexion->server_info,
        'host_info' => $conexion->host_info
    ]);
    echo "\n";
    
    // Verificar si la tabla existe
    $check_table = "SHOW TABLES LIKE 'tb_inteletgroup_prospectos'";
    $result = $conexion->query($check_table);
    
    echo json_encode([
        'step' => 'Verificación de tabla existente',
        'table_exists' => $result->num_rows > 0,
        'num_rows' => $result->num_rows
    ]);
    echo "\n";
    
    // Crear tabla de prospectos
    $create_prospectos_sql = "
    CREATE TABLE IF NOT EXISTS tb_inteletgroup_prospectos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        nombre_ejecutivo VARCHAR(255) NOT NULL,
        rut_cliente VARCHAR(20) NOT NULL UNIQUE,
        razon_social VARCHAR(255) NOT NULL,
        rubro VARCHAR(255) NOT NULL,
        direccion_comercial TEXT NOT NULL,
        telefono_celular VARCHAR(20) NOT NULL,
        email VARCHAR(255) NOT NULL,
        numero_pos VARCHAR(50),
        tipo_cuenta VARCHAR(50) NOT NULL,
        numero_cuenta_bancaria VARCHAR(50) NOT NULL,
        dias_atencion VARCHAR(100) NOT NULL,
        horario_atencion VARCHAR(100) NOT NULL,
        contrata_boleta VARCHAR(10) NOT NULL,
        competencia_actual VARCHAR(100) NOT NULL,
        fecha_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_usuario_id (usuario_id),
        INDEX idx_rut_cliente (rut_cliente)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conexion->query($create_prospectos_sql)) {
        echo json_encode([
            'step' => 'Creación tabla prospectos',
            'success' => true,
            'message' => 'Tabla tb_inteletgroup_prospectos creada/verificada exitosamente'
        ]);
    } else {
        echo json_encode([
            'step' => 'Creación tabla prospectos',
            'success' => false,
            'error' => $conexion->error,
            'errno' => $conexion->errno
        ]);
    }
    echo "\n";
    
    // Crear tabla de bitácora
    $create_bitacora_sql = "
    CREATE TABLE IF NOT EXISTS tb_inteletgroup_bitacora (
        id INT AUTO_INCREMENT PRIMARY KEY,
        prospecto_id INT NOT NULL,
        usuario_id INT NOT NULL,
        accion VARCHAR(100) NOT NULL,
        descripcion TEXT,
        fecha_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_prospecto_id (prospecto_id),
        INDEX idx_usuario_id (usuario_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conexion->query($create_bitacora_sql)) {
        echo json_encode([
            'step' => 'Creación tabla bitácora',
            'success' => true,
            'message' => 'Tabla tb_inteletgroup_bitacora creada/verificada exitosamente'
        ]);
    } else {
        echo json_encode([
            'step' => 'Creación tabla bitácora',
            'success' => false,
            'error' => $conexion->error,
            'errno' => $conexion->errno
        ]);
    }
    echo "\n";
    
    // Verificar tablas creadas
    $tables_check = "SHOW TABLES LIKE 'tb_inteletgroup_%'";
    $tables_result = $conexion->query($tables_check);
    
    $tables = [];
    while ($row = $tables_result->fetch_array()) {
        $tables[] = $row[0];
    }
    
    echo json_encode([
        'step' => 'Verificación final',
        'success' => true,
        'tables_created' => $tables,
        'total_tables' => count($tables)
    ]);
    echo "\n";
    
    // Verificar estructura de la tabla principal
    if (in_array('tb_inteletgroup_prospectos', $tables)) {
        $describe_result = $conexion->query("DESCRIBE tb_inteletgroup_prospectos");
        $columns = [];
        while ($row = $describe_result->fetch_assoc()) {
            $columns[] = $row;
        }
        
        echo json_encode([
            'step' => 'Estructura tabla prospectos',
            'success' => true,
            'columns' => $columns
        ]);
        echo "\n";
    }
    
} catch (Exception $e) {
    echo json_encode([
        'step' => 'Error general',
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
