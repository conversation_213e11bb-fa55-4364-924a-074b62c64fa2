<?php
// Test script para verificar la prevención de duplicados
header('Content-Type: application/json');

// Simular datos de prueba
$test_data = [
    'rut_cliente' => '12345678-9',
    'razon_social' => 'TEST DUPLICADOS SA',
    'email' => '<EMAIL>',
    'telefono_celular' => '987654321'
];

// Configuración de base de datos
$host = '*************';
$port = 3306;
$dbname = 'gestarse_experian';
$username = 'gestarse_ncornejo7_experian';
$password = 'N1c0l7as17';

try {
    // Conectar a la base de datos
    $db = new mysqli($host, $username, $password, $dbname, $port);
    
    if ($db->connect_error) {
        throw new Exception("Connection failed: " . $db->connect_error);
    }
    
    // Función de verificación de duplicados (copiada del archivo original)
    function verificar_envio_duplicado_reciente($db, $usuario_id, $datos) {
        $sql = "SELECT id FROM tb_inteletgroup_prospectos 
                WHERE usuario_id = ? 
                AND rut_cliente = ? 
                AND razon_social = ?
                AND email = ?
                AND telefono_celular = ?
                AND fecha_registro >= DATE_SUB(NOW(), INTERVAL 5 SECOND)
                LIMIT 1";
        
        $stmt = $db->prepare($sql);
        if (!$stmt) {
            return false;
        }
        
        $stmt->bind_param("issss", $usuario_id, $datos['rut_cliente'], $datos['razon_social'], $datos['email'], $datos['telefono_celular']);
        $stmt->execute();
        $stmt->store_result();
        $existe_duplicado = $stmt->num_rows > 0;
        $stmt->close();
        
        return $existe_duplicado;
    }
    
    // Probar la función de verificación
    $usuario_id = 7; // Usuario de prueba
    $es_duplicado = verificar_envio_duplicado_reciente($db, $usuario_id, $test_data);
    
    // Obtener los últimos registros para análisis
    $sql_recent = "SELECT id, rut_cliente, razon_social, fecha_registro, usuario_id 
                   FROM tb_inteletgroup_prospectos 
                   WHERE usuario_id = ? 
                   ORDER BY fecha_registro DESC 
                   LIMIT 5";
    
    $stmt = $db->prepare($sql_recent);
    $stmt->bind_param("i", $usuario_id);
    $stmt->execute();
    
    $recent_records = [];
    $id = $rut = $razon = $fecha = $uid = null;
    $stmt->bind_result($id, $rut, $razon, $fecha, $uid);
    
    while ($stmt->fetch()) {
        $recent_records[] = [
            'id' => $id,
            'rut_cliente' => $rut,
            'razon_social' => $razon,
            'fecha_registro' => $fecha,
            'usuario_id' => $uid
        ];
    }
    
    $stmt->close();
    $db->close();
    
    echo json_encode([
        'success' => true,
        'test_data' => $test_data,
        'usuario_id' => $usuario_id,
        'es_duplicado' => $es_duplicado,
        'recent_records' => $recent_records,
        'message' => 'Test de prevención de duplicados completado'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
