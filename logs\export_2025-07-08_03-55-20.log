[2025-07-08 03:55:20] [INFO] Iniciando proceso de exportación
[2025-07-08 03:55:20] [INFO] Usuario autenticado: 8
[2025-07-08 03:55:21] [INFO] Conexión a base de datos establecida
[2025-07-08 03:55:21] [INFO] Filtros recibidos: ejecutivo=todos, periodo=año, fecha_inicio=2025-01-01, fecha_fin=2025-12-31
[2025-07-08 03:55:21] [INFO] Fechas calculadas: inicio=2025-01-01, fin=2025-12-31
[2025-07-08 03:55:21] [INFO] Cláusula WHERE construida: 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?
[2025-07-08 03:55:21] [INFO] Consulta SQL preparada:      SELECT         p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,         p.email, p.telefono_celular, p.direccion_comercial, p.fecha_registro,         COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario     FROM tb_inteletgroup_prospectos p     LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id     WHERE 1=1 AND DATE(p.fecha_registro) BETWEEN ? AND ?     ORDER BY p.fecha_registro DESC
[2025-07-08 03:55:21] [INFO] Consulta preparada correctamente
[2025-07-08 03:55:21] [INFO] Parámetros vinculados a la consulta
[2025-07-08 03:55:21] [INFO] Consulta ejecutada correctamente
[2025-07-08 03:55:21] [INFO] Variables vinculadas para resultados
[2025-07-08 03:55:21] [INFO] Se encontraron 3 prospectos
