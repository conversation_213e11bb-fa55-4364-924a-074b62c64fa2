<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Affan - PWA Mobile HTML Template">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>Affan - PWA Mobile HTML Template</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/favicon.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">
  
  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card -->
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">
      <!-- Header Content -->
      <div class="header-content position-relative d-flex align-items-center justify-content-between">
        <!-- Back Button -->
        <div class="back-button">
          <a href="elements.html">
            <i class="bi bi-arrow-left-short"></i>
          </a>
        </div>

        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0">Button</h6>
        </div>

        <!-- Settings -->
        <div class="setting-wrapper">
          <div class="setting-trigger-btn" id="settingTriggerBtn">
            <i class="bi bi-gear"></i>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="page-content-wrapper py-3">

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading">
        <h6>Default Button</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="direction-rtl">
            <a class="btn m-1 btn-primary" href="#">Primary</a>
            <a class="btn m-1 btn-secondary" href="#">Secondary</a>
            <a class="btn m-1 btn-success" href="#">Success</a>
            <a class="btn m-1 btn-danger" href="#">Danger</a>
            <a class="btn m-1 btn-warning" href="#">Warning</a>
            <a class="btn m-1 btn-info" href="#">Info</a>
            <a class="btn m-1 btn-light" href="#">Light</a>
            <a class="btn m-1 btn-dark" href="#">Dark</a>
            <a class="btn m-1 btn-link" href="#">Link</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Outline Button</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="direction-rtl">
            <a class="btn m-1 btn-outline-primary" href="#">Primary</a>
            <a class="btn m-1 btn-outline-secondary" href="#">Secondary</a>
            <a class="btn m-1 btn-outline-success" href="#">Success</a>
            <a class="btn m-1 btn-outline-danger" href="#">Danger</a>
            <a class="btn m-1 btn-outline-warning" href="#">Warning</a>
            <a class="btn m-1 btn-outline-info" href="#">Info</a>
            <a class="btn m-1 btn-outline-light" href="#">Light</a>
            <a class="btn m-1 btn-outline-dark" href="#">Dark</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Round Button</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="direction-rtl">
            <a class="btn m-1 rounded-pill btn-primary" href="#">Primary</a>
            <a class="btn m-1 rounded-pill btn-secondary" href="#">Secondary</a>
            <a class="btn m-1 rounded-pill btn-success" href="#">Success</a>
            <a class="btn m-1 rounded-pill btn-danger" href="#">Danger</a>
            <a class="btn m-1 rounded-pill btn-outline-warning" href="#">Warning</a>
            <a class="btn m-1 rounded-pill btn-outline-info" href="#">Info</a>
            <a class="btn m-1 rounded-pill btn-outline-light" href="#">Light</a>
            <a class="btn m-1 rounded-pill btn-outline-dark" href="#">Dark</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Creative Button</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="direction-rtl">
            <a class="btn m-1 btn-creative btn-primary" href="#">Primary</a>
            <a class="btn m-1 btn-creative btn-secondary" href="#">Secondary</a>
            <a class="btn m-1 btn-creative btn-success" href="#">Success</a>
            <a class="btn m-1 btn-creative btn-danger" href="#">Danger</a>
            <a class="btn m-1 btn-creative btn-warning" href="#">Warning</a>
            <a class="btn m-1 btn-creative btn-info" href="#">Info</a>
            <a class="btn m-1 btn-creative btn-light" href="#">Light</a>
            <a class="btn m-1 btn-creative btn-dark" href="#">Dark</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Button with icon</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="direction-rtl">
            <a class="btn m-1 btn-primary" href="#">
              <i class="bi bi-arrow-down"></i> Download
            </a>

            <a class="btn m-1 btn-success" href="#">
              <i class="bi bi-arrow-repeat"></i> Refresh Now
            </a>

            <a class="btn m-1 btn-danger" href="#">
              <i class="bi bi-caret-right"></i> Play Now
            </a>

            <a class="btn m-1 btn-warning" href="#">
              <i class="bi bi-bookmark"></i> Bookmark
            </a>

            <a class="btn m-1 btn-info" href="#">
              <i class="bi bi-cursor"></i> Send Now
            </a>

            <a class="btn m-1 btn-light" href="#">
              <i class="bi bi-check2-circle"></i> Active Now
            </a>

            <a class="btn m-1 btn-dark" href="#">
              <i class="bi bi-trash"></i> Delete Item
            </a>

            <a class="btn m-1 btn-primary" href="#">
              <i class="bi bi-bag"></i> Add to Cart
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Social Button</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="direction-rtl">
            <a class="btn m-1 btn-creative btn-primary" href="#">
              <i class="bi bi-facebook me-2"></i>Facebook
            </a>
            <a class="btn m-1 btn-creative btn-success" href="#">
              <i class="bi bi-whatsapp me-2"></i>WhatsApp
            </a>
            <a class="btn m-1 btn-creative btn-danger" href="#">
              <i class="bi bi-youtube me-2"></i>YouTube
            </a>
            <a class="btn m-1 btn-creative btn-warning" href="#">
              <i class="bi bi-instagram me-2"></i>Instagram
            </a>
            <a class="btn m-1 btn-creative btn-info" href="#">
              <i class="bi bi-twitter me-2"></i>Twitter
            </a>
            <a class="btn m-1 btn-creative btn-danger" href="#">
              <i class="bi bi-telegram me-2"></i>Telegram
            </a>

            <a class="btn m-1 btn-creative btn-primary" href="#">
              <i class="bi bi-facebook"></i>
            </a>
            <a class="btn m-1 btn-creative btn-success" href="#">
              <i class="bi bi-whatsapp"></i>
            </a>
            <a class="btn m-1 btn-creative btn-danger" href="#">
              <i class="bi bi-youtube"></i>
            </a>
            <a class="btn m-1 btn-creative btn-warning" href="#">
              <i class="bi bi-instagram"></i>
            </a>
            <a class="btn m-1 btn-creative btn-info" href="#">
              <i class="bi bi-twitter"></i>
            </a>
            <a class="btn m-1 btn-creative btn-danger" href="#">
              <i class="bi bi-telegram"></i>
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Circle Button</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body d-flex flex-wrap circle-btn-wrapper">
          <a class="btn m-1 btn-circle btn-primary" href="#">
            <i class="bi bi-bag-check"></i>
          </a>

          <a class="btn m-1 btn-circle btn-success" href="#">
            <i class="bi bi-bookmark-star"></i>
          </a>

          <a class="btn m-1 btn-circle btn-danger" href="#">
            <i class="bi bi-bookmark-star"></i>
          </a>

          <a class="btn m-1 btn-circle btn-danger" href="#">
            <i class="bi bi-gear"></i>
          </a>

          <a class="btn m-1 btn-circle btn-primary" href="#">
            <i class="bi bi-eye"></i>
          </a>

          <a class="btn m-1 btn-circle btn-success" href="#">
            <i class="bi bi-chat-dots"></i>
          </a>

          <a class="btn m-1 btn-circle btn-danger" href="#">
            <i class="bi bi-check2"></i>
          </a>

          <a class="btn m-1 btn-circle btn-info" href="#">
            <i class="bi bi-cursor-fill"></i>
          </a>

          <a class="btn m-1 btn-circle btn-danger" href="#">
            <i class="bi bi-easel"></i>
          </a>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Large Button</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="direction-rtl">
            <a class="btn m-1 btn-lg btn-primary" href="#">Primary</a>
            <a class="btn m-1 btn-lg btn-secondary" href="#">Secondary</a>
            <a class="btn m-1 btn-lg btn-success" href="#">Success</a>
            <a class="btn m-1 btn-lg btn-danger" href="#">Danger</a>
            <a class="btn m-1 btn-lg btn-warning" href="#">Warning</a>
            <a class="btn m-1 btn-lg btn-info" href="#">Info</a>
            <a class="btn m-1 btn-lg btn-light" href="#">Light</a>
            <a class="btn m-1 btn-lg btn-dark" href="#">Dark</a>
            <a class="btn m-1 btn-lg btn-link" href="#">Link</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Small Button</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="direction-rtl">
            <a class="btn m-1 btn-sm btn-primary" href="#">Primary</a>
            <a class="btn m-1 btn-sm btn-secondary" href="#">Secondary</a>
            <a class="btn m-1 btn-sm btn-success" href="#">Success</a>
            <a class="btn m-1 btn-sm btn-danger" href="#">Danger</a>
            <a class="btn m-1 btn-sm btn-warning" href="#">Warning</a>
            <a class="btn m-1 btn-sm btn-info" href="#">Info</a>
            <a class="btn m-1 btn-sm btn-light" href="#">Light</a>
            <a class="btn m-1 btn-sm btn-dark" href="#">Dark</a>
            <a class="btn m-1 btn-sm btn-link" href="#">Link</a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          <li class="active">
            <a href="home.html">
              <i class="bi bi-house"></i>
              <span>Home</span>
            </a>
          </li>

          <li>
            <a href="pages.html">
              <i class="bi bi-collection"></i>
              <span>Pages</span>
            </a>
          </li>

          <li>
            <a href="elements.html">
              <i class="bi bi-folder2-open"></i>
              <span>Elements</span>
            </a>
          </li>

          <li>
            <a href="chat-users.html">
              <i class="bi bi-chat-dots"></i>
              <span>Chat</span>
            </a>
          </li>

          <li>
            <a href="settings.html">
              <i class="bi bi-gear"></i>
              <span>Settings</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
</body>

</html>