/* Estilos para tablas ordenables */
.sortable-table {
    width: 100%;
    border-collapse: collapse;
}

/* Estilo para encabezados ordenables */
.sortable {
    cursor: pointer;
    position: relative;
    padding-right: 20px !important;
}

/* Icono de ordenamiento (flecha) */
.sort-icon {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 14px;
}

/* Estilos para encabezados ordenados */
.sorted-asc .sort-icon,
.asc .sort-icon {
    color: #333;
}

.sorted-desc .sort-icon,
.desc .sort-icon {
    color: #333;
}

/* Cambiar el contenido del icono según la dirección */
.sorted-asc .sort-icon::after,
.asc .sort-icon::after {
    content: '↑';
}

.sorted-desc .sort-icon::after,
.desc .sort-icon::after {
    content: '↓';
}

/* Ocultar el contenido original del icono cuando está ordenado */
.sorted-asc .sort-icon,
.sorted-desc .sort-icon,
.asc .sort-icon,
.desc .sort-icon {
    font-size: 0;
}

/* Mostrar el nuevo contenido con el tamaño correcto */
.sorted-asc .sort-icon::after,
.sorted-desc .sort-icon::after,
.asc .sort-icon::after,
.desc .sort-icon::after {
    font-size: 14px;
    display: inline-block;
}

/* Resaltar filas al pasar el mouse */
.sortable-table tbody tr:hover,
#user-table tbody tr:hover,
#ejecutivos-table tbody tr:hover {
    background-color: #f5f5f5;
}

/* Estilo para filas alternas */
.sortable-table tbody tr:nth-child(even),
#user-table tbody tr:nth-child(even),
#ejecutivos-table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

/* Estilos específicos para las tablas del proyecto */
#user-table th.sortable,
#ejecutivos-table th.sortable {
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    padding-right: 25px !important;
}

#user-table th.asc,
#user-table th.desc,
#ejecutivos-table th.asc,
#ejecutivos-table th.desc {
    background-color: #e0e0e0;
}

/* Asegurar que los iconos sean visibles */
#user-table th .sort-icon,
#ejecutivos-table th .sort-icon {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
}
