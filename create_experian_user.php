<?php
/**
 * Script para crear un usuario de prueba con proyecto Experian
 */

require_once("con_db.php");

echo "<h2>Crear Usuario de Prueba - Proyecto Experian</h2>";

try {
    // Verificar si ya existe un usuario con proyecto experian
    $sql_check = "SELECT * FROM tb_experian_usuarios WHERE proyecto = 'experian'";
    $result_check = $mysqli->query($sql_check);
    
    if ($result_check->num_rows > 0) {
        echo "<h3>Usuarios Experian Existentes:</h3>";
        echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background-color: #e3f2fd;'>";
        echo "<th>ID</th><th>Correo</th><th>Nombre Usuario</th><th>Rol</th><th>Proyecto</th>";
        echo "</tr>";
        
        while ($row = $result_check->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['correo']) . "</td>";
            echo "<td>" . htmlspecialchars($row['nombre_usuario'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($row['rol']) . "</td>";
            echo "<td><strong>" . htmlspecialchars($row['proyecto']) . "</strong></td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p style='color: green;'>✅ Ya existen usuarios con proyecto Experian.</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ No se encontraron usuarios con proyecto Experian.</p>";
    }
    
    // Formulario para crear usuario de prueba
    if (isset($_POST['create_experian_user'])) {
        // Datos del usuario de prueba
        $correo = '<EMAIL>';
        $clave = password_hash('123456', PASSWORD_DEFAULT);
        $rol = 'ejecutivos';
        $nombre_usuario = 'Usuario Experian Test';
        $proyecto = 'experian';
        $rut_ejecutivo = '11111111-1';
        
        // Verificar si el usuario ya existe
        $sql_exists = "SELECT id FROM tb_experian_usuarios WHERE correo = ?";
        $stmt_exists = $mysqli->prepare($sql_exists);
        $stmt_exists->bind_param("s", $correo);
        $stmt_exists->execute();
        $result_exists = $stmt_exists->get_result();
        
        if ($result_exists->num_rows > 0) {
            echo "<p style='color: red;'>❌ El usuario $correo ya existe.</p>";
        } else {
            // Crear el usuario
            $sql_insert = "INSERT INTO tb_experian_usuarios (correo, clave, rol, nombre_usuario, proyecto, rut_ejecutivo) VALUES (?, ?, ?, ?, ?, ?)";
            $stmt_insert = $mysqli->prepare($sql_insert);
            $stmt_insert->bind_param("ssssss", $correo, $clave, $rol, $nombre_usuario, $proyecto, $rut_ejecutivo);
            
            if ($stmt_insert->execute()) {
                echo "<div style='background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0;'>";
                echo "<h3 style='color: #155724;'>✅ Usuario Experian creado exitosamente!</h3>";
                echo "<p><strong>Credenciales de prueba:</strong></p>";
                echo "<ul>";
                echo "<li><strong>Correo:</strong> $correo</li>";
                echo "<li><strong>Contraseña:</strong> 123456</li>";
                echo "<li><strong>Proyecto:</strong> $proyecto</li>";
                echo "<li><strong>Rol:</strong> $rol</li>";
                echo "</ul>";
                echo "<p><strong>Redirección esperada:</strong> form_experian2.php</p>";
                echo "</div>";
                
                echo "<p><a href='login.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Ir al Login para Probar</a></p>";
            } else {
                echo "<p style='color: red;'>❌ Error creando usuario: " . $stmt_insert->error . "</p>";
            }
        }
    } else {
        echo "<hr>";
        echo "<h3>Crear Usuario de Prueba para Proyecto Experian:</h3>";
        echo "<form method='POST'>";
        echo "<div style='background-color: #f8f9fa; padding: 20px; border: 1px solid #dee2e6; border-radius: 5px;'>";
        echo "<p><strong>Se creará un usuario con las siguientes características:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Correo:</strong> <EMAIL></li>";
        echo "<li><strong>Contraseña:</strong> 123456</li>";
        echo "<li><strong>Proyecto:</strong> experian</li>";
        echo "<li><strong>Rol:</strong> ejecutivos</li>";
        echo "<li><strong>Redirección esperada:</strong> form_experian2.php</li>";
        echo "</ul>";
        echo "<button type='submit' name='create_experian_user' style='background-color: #28a745; color: white; padding: 10px 20px; border: none; cursor: pointer; border-radius: 5px;'>Crear Usuario Experian</button>";
        echo "</div>";
        echo "</form>";
    }
    
    // Mostrar resumen de todos los proyectos
    echo "<hr>";
    echo "<h3>Resumen de Usuarios por Proyecto:</h3>";
    $sql_summary = "SELECT proyecto, COUNT(*) as total FROM tb_experian_usuarios GROUP BY proyecto";
    $result_summary = $mysqli->query($sql_summary);
    
    echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse; width: 50%;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>Proyecto</th><th>Total Usuarios</th><th>Redirección</th>";
    echo "</tr>";
    
    while ($row = $result_summary->fetch_assoc()) {
        $proyecto = $row['proyecto'] ?? 'NULL';
        $total = $row['total'];
        
        $redirectUrl = '';
        $bgColor = '';
        if ($proyecto === 'experian') {
            $redirectUrl = 'form_experian2.php';
            $bgColor = '#e3f2fd'; // Azul claro
        } elseif ($proyecto === 'inteletGroup') {
            $redirectUrl = 'form_inteletgroup.php';
            $bgColor = '#e8f5e8'; // Verde claro
        } else {
            $redirectUrl = 'form_experian2.php (por defecto)';
            $bgColor = '#fff3e0'; // Naranja claro
        }
        
        echo "<tr style='background-color: $bgColor;'>";
        echo "<td><strong>" . htmlspecialchars($proyecto) . "</strong></td>";
        echo "<td>$total</td>";
        echo "<td>" . htmlspecialchars($redirectUrl) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<div style='color: red; background-color: #ffebee; padding: 15px; border-left: 4px solid #f44336;'>";
    echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
} finally {
    if (isset($mysqli)) {
        $mysqli->close();
    }
}

echo "<hr>";
echo "<p><em>Fecha: " . date('Y-m-d H:i:s') . "</em></p>";
?>
