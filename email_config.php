<?php
/**
 * Configuración y funciones para el envío de emails
 * Sistema de notificaciones para registro de prospectos
 */

// Configuración de email
define('SMTP_HOST', 'smtp.gmail.com'); // Cambiar según proveedor
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>'); // Cambiar por email real
define('SMTP_PASSWORD', 'password'); // Cambiar por contraseña real
define('SMTP_ENCRYPTION', 'tls');

define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Sistema de Prospectos - InteletGroup');
define('TO_EMAIL', '<EMAIL>'); // Email de destino para notificaciones

/**
 * Función para enviar email usando configuración SMTP
 */
function enviarEmailSMTP($to, $subject, $body, $isHTML = false, $attachments = []) {
    // Si PHPMailer está disponible, usarlo
    if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
        return enviarConPHPMailer($to, $subject, $body, $isHTML, $attachments);
    } else {
        // Fallback a mail() nativo
        return enviarConMailNativo($to, $subject, $body, $isHTML);
    }
}

/**
 * Enviar email con PHPMailer (recomendado)
 */
function enviarConPHPMailer($to, $subject, $body, $isHTML = false, $attachments = []) {
    try {
        require_once 'vendor/autoload.php'; // Si se instala PHPMailer via Composer
        
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        
        // Configuración del servidor
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = SMTP_ENCRYPTION;
        $mail->Port = SMTP_PORT;
        $mail->CharSet = 'UTF-8';
        
        // Remitente y destinatario
        $mail->setFrom(FROM_EMAIL, FROM_NAME);
        $mail->addAddress($to);
        
        // Contenido
        $mail->isHTML($isHTML);
        $mail->Subject = $subject;
        $mail->Body = $body;
        
        // Adjuntos
        foreach ($attachments as $attachment) {
            if (file_exists($attachment)) {
                $mail->addAttachment($attachment);
            }
        }
        
        $mail->send();
        return true;
        
    } catch (Exception $e) {
        error_log("Error enviando email con PHPMailer: " . $e->getMessage());
        return false;
    }
}

/**
 * Enviar email con función mail() nativa (fallback)
 */
function enviarConMailNativo($to, $subject, $body, $isHTML = false) {
    $headers = "From: " . FROM_NAME . " <" . FROM_EMAIL . ">\r\n";
    $headers .= "Reply-To: " . FROM_EMAIL . "\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
    
    if ($isHTML) {
        $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    } else {
        $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
    }
    
    return mail($to, $subject, $body, $headers);
}

/**
 * Generar email HTML para notificación de prospecto
 */
function generarEmailProspecto($datos) {
    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Nuevo Prospecto Registrado</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
            .content { background: #f9f9f9; padding: 20px; }
            .field { margin-bottom: 15px; }
            .label { font-weight: bold; color: #555; }
            .value { margin-left: 10px; }
            .footer { background: #333; color: white; padding: 15px; text-align: center; font-size: 12px; }
            .highlight { background: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin: 15px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Nuevo Prospecto Registrado</h1>
                <p>Sistema de Gestión de Prospectos - InteletGroup</p>
            </div>
            
            <div class="content">
                <div class="highlight">
                    <strong>RUT Cliente:</strong> ' . htmlspecialchars($datos['rut_ejecutivo']) . '<br>
                    <strong>Razón Social:</strong> ' . htmlspecialchars($datos['razon_social']) . '
                </div>
                
                <h3>Información del Cliente</h3>
                <div class="field">
                    <span class="label">Ejecutivo Responsable:</span>
                    <span class="value">' . htmlspecialchars($datos['nombre_ejecutivo']) . '</span>
                </div>
                <div class="field">
                    <span class="label">Rubro:</span>
                    <span class="value">' . htmlspecialchars($datos['rubro'] ?: 'No especificado') . '</span>
                </div>
                <div class="field">
                    <span class="label">Dirección Comercial:</span>
                    <span class="value">' . htmlspecialchars($datos['direccion_comercial'] ?: 'No especificada') . '</span>
                </div>
                
                <h3>Información de Contacto</h3>
                <div class="field">
                    <span class="label">Teléfono:</span>
                    <span class="value">' . htmlspecialchars($datos['telefono']) . '</span>
                </div>
                <div class="field">
                    <span class="label">Email:</span>
                    <span class="value">' . htmlspecialchars($datos['email'] ?: 'No proporcionado') . '</span>
                </div>
                
                <h3>Información Comercial</h3>
                <div class="field">
                    <span class="label">N° de POS:</span>
                    <span class="value">' . htmlspecialchars($datos['num_pos'] ?: 'No especificado') . '</span>
                </div>
                <div class="field">
                    <span class="label">Tipo de Cuenta:</span>
                    <span class="value">' . htmlspecialchars($datos['tipo_cuenta'] ?: 'No especificado') . '</span>
                </div>
                <div class="field">
                    <span class="label">Competencia Actual:</span>
                    <span class="value">' . htmlspecialchars($datos['competencia_actual'] ?: 'No especificada') . '</span>
                </div>
                
                <h3>Horarios</h3>
                <div class="field">
                    <span class="label">Días de Atención:</span>
                    <span class="value">' . htmlspecialchars($datos['dias_atencion'] ?: 'No especificado') . '</span>
                </div>
                <div class="field">
                    <span class="label">Horario de Atención:</span>
                    <span class="value">' . htmlspecialchars($datos['horario_atencion'] ?: 'No especificado') . '</span>
                </div>
                
                ' . (!empty($datos['observaciones']) ? '
                <h3>Observaciones</h3>
                <div style="background: white; padding: 15px; border-radius: 5px; border-left: 4px solid #667eea;">
                    ' . nl2br(htmlspecialchars($datos['observaciones'])) . '
                </div>
                ' : '') . '
                
                <div class="field" style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd;">
                    <span class="label">Fecha de Registro:</span>
                    <span class="value">' . date('d/m/Y H:i:s') . '</span>
                </div>
            </div>
            
            <div class="footer">
                <p>Este email fue generado automáticamente por el Sistema de Gestión de Prospectos</p>
                <p>InteletGroup - ' . date('Y') . '</p>
            </div>
        </div>
    </body>
    </html>';
    
    return $html;
}

/**
 * Enviar notificación de nuevo prospecto
 */
function enviarNotificacionProspecto($datos) {
    try {
        $subject = "Nuevo Prospecto: " . $datos['rut_ejecutivo'];
        $htmlBody = generarEmailProspecto($datos);
        
        // Generar versión texto plano
        $textBody = "Nuevo prospecto registrado:\n\n";
        $textBody .= "RUT Cliente: " . $datos['rut_ejecutivo'] . "\n";
        $textBody .= "Razón Social: " . $datos['razon_social'] . "\n";
        $textBody .= "Ejecutivo: " . $datos['nombre_ejecutivo'] . "\n";
        $textBody .= "Teléfono: " . $datos['telefono'] . "\n";
        $textBody .= "Email: " . ($datos['email'] ?: 'No proporcionado') . "\n";
        $textBody .= "Rubro: " . ($datos['rubro'] ?: 'No especificado') . "\n";
        $textBody .= "Fecha: " . date('d/m/Y H:i:s') . "\n";
        
        if (!empty($datos['observaciones'])) {
            $textBody .= "\nObservaciones:\n" . $datos['observaciones'] . "\n";
        }
        
        // Intentar enviar HTML primero, fallback a texto plano
        $enviado = enviarEmailSMTP(TO_EMAIL, $subject, $htmlBody, true);
        
        if (!$enviado) {
            $enviado = enviarEmailSMTP(TO_EMAIL, $subject, $textBody, false);
        }
        
        if ($enviado) {
            error_log("Notificación de prospecto enviada exitosamente: " . $datos['rut_ejecutivo']);
        } else {
            error_log("Error enviando notificación de prospecto: " . $datos['rut_ejecutivo']);
        }
        
        return $enviado;
        
    } catch (Exception $e) {
        error_log("Error en enviarNotificacionProspecto: " . $e->getMessage());
        return false;
    }
}

/**
 * Validar configuración de email
 */
function validarConfiguracionEmail() {
    $errores = [];
    
    if (SMTP_USERNAME === '<EMAIL>') {
        $errores[] = 'Configurar SMTP_USERNAME con email real';
    }
    
    if (SMTP_PASSWORD === 'password') {
        $errores[] = 'Configurar SMTP_PASSWORD con contraseña real';
    }
    
    if (TO_EMAIL === '<EMAIL>') {
        $errores[] = 'Configurar TO_EMAIL con email de destino real';
    }
    
    return $errores;
}
?>
