<?php
// Script para verificar la estructura de la tabla InteletGroup
require_once 'con_db.php';

header('Content-Type: application/json; charset=utf-8');

try {
    // Verificar la estructura de la tabla
    $sql = "SHOW CREATE TABLE tb_inteletgroup_prospectos";
    $result = $mysqli->query($sql);
    
    if (!$result) {
        throw new Exception("Error al obtener estructura de tabla: " . $mysqli->error);
    }
    
    $row = $result->fetch_assoc();
    $create_table = $row['Create Table'];
    
    // Verificar si existe la restricción UNIQUE
    $has_unique = strpos($create_table, 'UNIQUE KEY') !== false;
    $has_rut_unique = strpos($create_table, 'UNIQUE KEY `rut_cliente`') !== false;
    
    // Obtener información de índices
    $indexes_sql = "SHOW INDEX FROM tb_inteletgroup_prospectos WHERE Column_name = 'rut_cliente'";
    $indexes_result = $mysqli->query($indexes_sql);
    
    $indexes = [];
    while ($index_row = $indexes_result->fetch_assoc()) {
        $indexes[] = $index_row;
    }
    
    echo json_encode([
        'success' => true,
        'table_structure' => $create_table,
        'has_unique_constraint' => $has_unique,
        'has_rut_unique' => $has_rut_unique,
        'rut_indexes' => $indexes,
        'message' => $has_rut_unique ? 'PROBLEMA: La restricción UNIQUE aún existe' : 'OK: No hay restricción UNIQUE en rut_cliente'
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}
?>
