-- =====================================================
-- REORGANIZACIÓN COMPLETA DE TABLA DE DOCUMENTOS
-- =====================================================
-- Este script limpia y reorganiza la tabla tb_inteletgroup_tipos_documento
-- para eliminar duplicados y establecer la estructura correcta

-- 1. ELIMINAR REGISTROS DUPLICADOS Y PROBLEMÁTICOS
DELETE FROM tb_inteletgroup_tipos_documento WHERE id IN (19, 21);

-- 2. ACTUALIZAR REGISTROS EXISTENTES PARA CORREGIR INCONSISTENCIAS

-- Corregir ID 7: Carpeta Tributaria debe ser OBLIGATORIO para Persona Natural
UPDATE tb_inteletgroup_tipos_documento 
SET es_obligatorio = 1 
WHERE id = 7;

-- Cambiar IDs 7, 8, 9, 10 de tipo 'Natural' a tipo 'Ambos'
UPDATE tb_inteletgroup_tipos_documento 
SET tipo_persona = 'Ambos' 
WHERE id IN (7, 8, 9, 10);

-- Actualizar códigos para reflejar que son tipo 'Ambos'
UPDATE tb_inteletgroup_tipos_documento 
SET codigo = 'AMBOS_CARPETA_TRIBUTARIA' 
WHERE id = 7;

UPDATE tb_inteletgroup_tipos_documento 
SET codigo = 'AMBOS_MANDATO_PAC' 
WHERE id = 8;

UPDATE tb_inteletgroup_tipos_documento 
SET codigo = 'AMBOS_PODER_NOTARIAL' 
WHERE id = 9;

UPDATE tb_inteletgroup_tipos_documento 
SET codigo = 'AMBOS_CARTA_ACEPTACION' 
WHERE id = 10;

-- 3. ACTUALIZAR NOMBRES Y DESCRIPCIONES PARA MAYOR CLARIDAD

-- ID 8: Mandato PAC (mantener nombre original)
UPDATE tb_inteletgroup_tipos_documento 
SET nombre = 'Mandato PAC',
    descripcion = 'Para clientes con cuenta de abono distinta a BCI donde el titular es el establecimiento'
WHERE id = 8;

-- ID 9: Poder Notarial (mantener nombre original)
UPDATE tb_inteletgroup_tipos_documento 
SET nombre = 'Poder Notarial',
    descripcion = 'Para clientes con cuenta de abono distinta a BCI, donde el titular es distinto al establecimiento'
WHERE id = 9;

-- ID 10: Carta de Aceptación (mantener nombre original)
UPDATE tb_inteletgroup_tipos_documento 
SET nombre = 'Carta de Aceptación',
    descripcion = 'Excepción: Clientes con facturación inferior a 100UF, Carta de Aceptación a la comisión garantizada'
WHERE id = 10;

-- 4. ACTUALIZAR ID 20: Cambiar nombre para evitar confusión
UPDATE tb_inteletgroup_tipos_documento 
SET codigo = 'AMBOS_CUENTAS_TERCEROS',
    nombre = 'Cuentas de abono de terceros',
    descripcion = 'Rut titular distinto al del establecimiento - Poder Simple: Cuenta de tercero de único Socio (EIRL)',
    orden = 21
WHERE id = 20;

-- 5. ACTUALIZAR ID 22: Ajustar orden
UPDATE tb_inteletgroup_tipos_documento 
SET orden = 22
WHERE id = 22;

-- 6. REORGANIZAR ÓRDENES PARA PERSONA JURÍDICA (hacer obligatorios los que corresponden)
UPDATE tb_inteletgroup_tipos_documento 
SET es_obligatorio = 1 
WHERE id = 17; -- Carpeta Tributaria para Jurídica

UPDATE tb_inteletgroup_tipos_documento 
SET es_obligatorio = 1 
WHERE id = 18; -- E-Rut para Jurídica

-- =====================================================
-- VERIFICACIÓN FINAL
-- =====================================================
-- Ejecutar estas consultas para verificar el resultado:

-- SELECT 'PERSONA NATURAL - OBLIGATORIOS' as categoria;
-- SELECT id, codigo, nombre, tipo_persona, es_obligatorio, orden 
-- FROM tb_inteletgroup_tipos_documento 
-- WHERE tipo_persona = 'Natural' AND es_obligatorio = 1 
-- ORDER BY orden;

-- SELECT 'PERSONA JURÍDICA - OBLIGATORIOS' as categoria;
-- SELECT id, codigo, nombre, tipo_persona, es_obligatorio, orden 
-- FROM tb_inteletgroup_tipos_documento 
-- WHERE tipo_persona = 'Juridica' AND es_obligatorio = 1 
-- ORDER BY orden;

-- SELECT 'DOCUMENTOS AMBOS - OPCIONALES' as categoria;
-- SELECT id, codigo, nombre, tipo_persona, es_obligatorio, orden 
-- FROM tb_inteletgroup_tipos_documento 
-- WHERE tipo_persona = 'Ambos' 
-- ORDER BY orden;

-- =====================================================
-- RESULTADO ESPERADO:
-- =====================================================
-- PERSONA NATURAL: 6 obligatorios + 6 opcionales (tipo Ambos) = 12 total
-- PERSONA JURÍDICA: 8 obligatorios + 6 opcionales (tipo Ambos) = 14 total
-- =====================================================
