<?php
// Archivo de debug para verificar el error en inteletgroup_documentos_enhanced.php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><title>Debug Error</title></head><body>";
echo "<h1>Debug de Error 500</h1>";

// Verificar errores de sintaxis en el archivo
$file = 'inteletgroup_documentos_enhanced.php';
echo "<h2>Verificando sintaxis de $file</h2>";

// Usar la función de PHP para verificar sintaxis
$output = [];
$return_var = 0;
exec("php -l $file 2>&1", $output, $return_var);

echo "<pre>";
echo "Resultado: " . ($return_var === 0 ? "✓ Sin errores de sintaxis" : "✗ Errores encontrados") . "\n";
echo "Output:\n";
foreach ($output as $line) {
    echo htmlspecialchars($line) . "\n";
}
echo "</pre>";

// Verificar archivos requeridos
echo "<h2>Verificando archivos requeridos</h2>";
$required_files = ['cache_utils.php', 'con_db.php'];
foreach ($required_files as $req_file) {
    echo $req_file . ": " . (file_exists($req_file) ? "✓ Existe" : "✗ No existe") . "<br>";
}

// Verificar logs de error
echo "<h2>Últimas líneas del error log</h2>";
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    $lines = array_slice(file($error_log), -20);
    echo "<pre>";
    foreach ($lines as $line) {
        if (stripos($line, 'inteletgroup') !== false) {
            echo htmlspecialchars($line);
        }
    }
    echo "</pre>";
} else {
    echo "No se puede acceder al log de errores<br>";
}

echo "</body></html>";
?>