// Esperar a que el DOM esté completamente cargado
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado completamente');

    // Función para comprobar si jQuery está disponible
    if (typeof jQuery === 'undefined') {
        console.error('jQuery no está disponible. Usando JavaScript nativo.');

        // Implementación con JavaScript nativo...
        // Todo el código de la versión sin jQuery
        
    } else {
        console.log('jQuery está disponible, versión: ' + jQuery.fn.jquery);

        $(document).ready(function() {
            // Todo el código de la versión con jQuery
            
            // Al final del documento ready dentro del else:
            
            // Botón para abrir modal de bitácora
            $(document).on('click', '.btn-bitacora', function() {
                const rut = $(this).data('rut');
                const nombre = $(this).data('nombre');
                const razon = $(this).data('razon');

                console.log('Botón de bitácora clickeado para RUT:', rut);

                // Establecer información en el modal
                $('#bitacora_rut').val(rut);
                $('#bitacora-info').html('<strong>Cliente:</strong> ' + razon + ' | <strong>Ejecutivo:</strong> ' + nombre + ' | <strong>RUT:</strong> ' + rut);

                // Cargar registros de bitácora
                cargarBitacora(rut);

                // Mostrar el modal
                $('#bitacoraModal').css('display', 'block');
            });
        }); // Fin de document ready
        
        // Función para cargar los registros de bitácora - Definida globalmente para que esté disponible en todo el ámbito
        window.cargarBitacora = function(rut) {
            console.log('Cargando bitácora para RUT:', rut);

            $.ajax({
                url: 'endpoints/obtener_bitacora.php',
                type: 'POST',
                data: {
                    rut: rut
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // Actualizar tabla de bitácora
                        if (response.data.length === 0) {
                            $('#bitacora-table tbody').html('<tr><td colspan="3" class="no-data">No hay registros disponibles</td></tr>');
                        } else {
                            let html = '';
                            $.each(response.data, function(index, registro) {
                                html += '<tr>';
                                html += '<td>' + registro.fecha_registro + '</td>';
                                html += '<td>' + registro.estado + '</td>';
                                html += '<td>' + registro.observaciones + '</td>';
                                html += '</tr>';
                            });
                            $('#bitacora-table tbody').html(html);
                        }
                    } else {
                        $('#bitacora-table tbody').html('<tr><td colspan="3" class="error-data">Error: ' + response.message + '</td></tr>');
                        console.error('Error al cargar bitácora:', response.message);
                    }
                },
                error: function(xhr, status, error) {
                    $('#bitacora-table tbody').html('<tr><td colspan="3" class="error-data">Error de conexión</td></tr>');
                    console.error('Error AJAX al cargar bitácora:', {
                        status: status,
                        error: error,
                        response: xhr.responseText
                    });
                }
            });
        }; // Fin de la función cargarBitacora
        
    } // Fin del else (jQuery está disponible)
    
}); // Fin del addEventListener DOMContentLoaded

// Código extraído de form_experian2.php

// Monitor de errores para mostrar detalles en el panel especial
window.addEventListener('error', function(event) {
    var errorMonitor = document.getElementById('error-monitor');
    var errorMessage = document.getElementById('error-message');
    var errorDetails = document.getElementById('error-details');
    
    if (errorMonitor && errorMessage && errorDetails) {
        errorMonitor.style.display = 'block';
        errorMessage.textContent = event.message || 'Error desconocido';
        
        // Crear detalles del error
        var details = [];
        details.push('Archivo: ' + (event.filename || 'desconocido'));
        details.push('Línea: ' + (event.lineno || '?') + ', Columna: ' + (event.colno || '?'));
        if (event.error && event.error.stack) {
            details.push('\nStack Trace:\n' + event.error.stack);
        }
        
        errorDetails.textContent = details.join('\n');
    }
});

// Mostrar loader durante peticiones AJAX
$(document).ajaxStart(function() {
    $('#global-loader').fadeIn(300);
});

$(document).ajaxStop(function() {
    $('#global-loader').fadeOut(300);
});

// Capturar errores AJAX también
$(document).ajaxError(function(event, jqXHR, ajaxSettings, thrownError) {
    var errorMonitor = document.getElementById('error-monitor');
    var errorMessage = document.getElementById('error-message');
    var errorDetails = document.getElementById('error-details');
    
    if (errorMonitor && errorMessage && errorDetails) {
        errorMonitor.style.display = 'block';
        errorMessage.textContent = 'Error en solicitud AJAX: ' + (thrownError || jqXHR.statusText || 'Error desconocido');
        
        // Crear detalles del error
        var details = [];
        details.push('URL: ' + ajaxSettings.url);
        details.push('Tipo: ' + ajaxSettings.type);
        details.push('Código de estado: ' + (jqXHR.status || 'desconocido'));
        
        if (jqXHR.responseText) {
            try {
                // Intentar formatear como JSON
                var jsonResponse = JSON.parse(jqXHR.responseText);
                details.push('\nRespuesta del servidor (JSON):\n' + JSON.stringify(jsonResponse, null, 2));
            } catch (e) {
                // Si no es JSON, mostrar como texto
                details.push('\nRespuesta del servidor:\n' + jqXHR.responseText.substring(0, 1000));
                if (jqXHR.responseText.length > 1000) details.push('... (respuesta truncada)');
            }
        }
        
        errorDetails.textContent = details.join('\n');
    }
});

// Define user permissions for JavaScript
// Esta parte requerirá un cambio en el PHP para establecer esta variable
window.userIsAdmin = false; // Este valor debe ser establecido en el PHP

// Arreglo para botones de navegación entre secciones del formulario
document.addEventListener('DOMContentLoaded', function() {
    // Verificar que los botones existan
    var nextButtons = document.querySelectorAll('#form-tab .btn-next');
    var prevButtons = document.querySelectorAll('#form-tab .btn-prev');
    var stepIndicators = document.querySelectorAll('#form-tab .step-indicator');
    
    console.log('Inicializando navegación de formulario - Botones siguiente:', nextButtons.length);
    
    // Función para mostrar una sección específica
    function showFormSection(sectionNumber) {
        console.log('Cambiando a sección:', sectionNumber);
        
        // Ocultar todas las secciones
        document.querySelectorAll('#form-tab .section-container').forEach(function(section) {
            section.classList.remove('active');
        });
        
        // Mostrar la sección seleccionada
        var targetSection = document.querySelector('#form-tab #section' + sectionNumber);
        if (targetSection) {
            targetSection.classList.add('active');
        }
        
        // Actualizar indicadores
        document.querySelectorAll('#form-tab .step-indicator').forEach(function(indicator) {
            indicator.classList.remove('active');
            if (indicator.getAttribute('data-step') === sectionNumber.toString()) {
                indicator.classList.add('active');
            }
        });
        
        // Actualizar barra de progreso
        var progressFill = document.querySelector('#form-tab .progress-line .fill');
        if (progressFill) {
            var percent = ((sectionNumber - 1) / 3) * 100; // Asumiendo 4 secciones (0%, 33%, 66%, 100%)
            progressFill.style.width = percent + '%';
        }
    }
    
    // Agregar eventos a botones de siguiente
    nextButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            var currentSection = parseInt(this.getAttribute('data-next') || this.getAttribute('data-section'));
            var nextSection = currentSection + 1;
            
            // Validaciones específicas para cada sección
            var canAdvance = true;
            
            // Ejemplo de validación para sección 1
            if (currentSection === 1) {
                // Validar código región seleccionado
                var regionSelect = document.querySelector('#seccion1 [name="codigo_region"]');
                if (regionSelect && regionSelect.value === "") {
                    alert('Debe seleccionar una región');
                    canAdvance = false;
                }
            }
            
            if (canAdvance) {
                showFormSection(nextSection);
            }
        });
    });
    
    // Agregar eventos a botones de anterior
    prevButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            var currentSection = parseInt(this.getAttribute('data-prev') || this.getAttribute('data-section'));
            var prevSection = currentSection - 1;
            
            if (prevSection >= 1) {
                showFormSection(prevSection);
            }
        });
    });
    
    // Agregar eventos a indicadores de paso
    stepIndicators.forEach(function(indicator) {
        indicator.addEventListener('click', function() {
            var targetSection = parseInt(this.getAttribute('data-step'));
            showFormSection(targetSection);
        });
    });
    
    // Iniciar en sección 1
    showFormSection(1);
});

// Esperar a que el DOM esté completamente cargado
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado - Configurando eventos del footer directamente');

    // Cargar la tabla de prospectos automáticamente para todos los usuarios
    if (typeof cargarEjecutivos === 'function') {
        console.log('Cargando tabla de prospectos automáticamente...');
        setTimeout(function() {
            cargarEjecutivos();
        }, 100);
    }

    // Cerrar modales al hacer clic en X
    var closeModalBtns = document.querySelectorAll('.modal .close-modal');
    closeModalBtns.forEach(function(btn) {
        btn.addEventListener('click', function() {
            var modal = this.closest('.modal');
            if (modal) {
                modal.style.display = 'none';
            }
        });
    });

    // Cerrar modal al hacer clic fuera de su contenido
    window.addEventListener('click', function(event) {
        document.querySelectorAll('.modal').forEach(function(modal) {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });
    });

    // Botón para agregar nueva observación a bitácora
    var bitacoraSubmit = document.getElementById('bitacora-submit');
    if (bitacoraSubmit) {
        bitacoraSubmit.addEventListener('click', function() {
            var rut = document.getElementById('bitacora_rut').value;
            var observaciones = document.getElementById('bitacora_observaciones').value;
            var estado = document.getElementById('bitacora_estado').value;
            
            if (!rut || !observaciones || !estado) {
                alert('Todos los campos son obligatorios');
                return;
            }
            
            // Enviar datos a través de AJAX
            $.ajax({
                url: 'endpoints/agregar_bitacora.php',
                type: 'POST',
                data: {
                    rut: rut,
                    observaciones: observaciones,
                    estado: estado
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // Limpiar campo de observaciones
                        document.getElementById('bitacora_observaciones').value = '';
                        
                        // Recargar la tabla de bitácora
                        if (typeof cargarBitacora === 'function') {
                            cargarBitacora(rut);
                        } else {
                            console.log('Función cargarBitacora no disponible, implementando alternativa');
                            // Implementar alternativa aquí si es necesario
                        }
                        
                        // Notificar al usuario
                        showNotification('Éxito', 'Registro de bitácora agregado correctamente', 'success');
                    } else {
                        showNotification('Error', response.message, 'danger');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error al agregar registro de bitácora:', {
                        status: status,
                        error: error,
                        response: xhr.responseText
                    });
                    showNotification('Error', 'No se pudo conectar con el servidor', 'danger');
                }
            });
        });
    }

    // Función para mostrar notificaciones
    window.showNotification = function(title, message, type) {
        var notification = document.createElement('div');
        notification.className = 'custom-toast ' + (type || 'info');
        
        var html = '<div class="toast-header">';
        html += '<strong>' + title + '</strong>';
        html += '<button type="button" class="toast-close">&times;</button>';
        html += '</div>';
        html += '<div class="toast-body">' + message + '</div>';
        
        notification.innerHTML = html;
        
        document.body.appendChild(notification);
        
        // Mostrar con animación
        setTimeout(function() {
            notification.classList.add('show');
            
            // Auto-ocultar después de 5 segundos
            setTimeout(function() {
                notification.classList.remove('show');
                setTimeout(function() {
                    document.body.removeChild(notification);
                }, 300); // Tiempo para que termine la animación de salida
            }, 5000);
        }, 10);
        
        // Agregar evento para cerrar manualmente
        notification.querySelector('.toast-close').addEventListener('click', function() {
            notification.classList.remove('show');
            setTimeout(function() {
                document.body.removeChild(notification);
            }, 300);
        });
    };

    // Inicializar datepickers
    if ($.fn.datepicker) {
        $('.datepicker').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            language: 'es'
        });
    }

    // Inicializar tooltips
    if ($.fn.tooltip) {
        $('[data-toggle="tooltip"]').tooltip();
    }

    // Implementación de validaciones de formulario
    window.validarFormularioVenta = function() {
        var formulario = document.getElementById('formExperian');
        
        // Implementar validaciones personalizadas aquí
        var ejecutivo = document.querySelector('[name="ejecutivo"]');
        var region = document.querySelector('[name="codigo_region"]');
        
        if (ejecutivo && ejecutivo.value === "") {
            showNotification('Error', 'Debe seleccionar un ejecutivo', 'danger');
            ejecutivo.focus();
            return false;
        }
        
        if (region && region.value === "") {
            showNotification('Error', 'Debe seleccionar una región', 'danger');
            region.focus();
            return false;
        }
        
        // Validar más campos aquí según sea necesario
        
        return true;
    };

    // Botón para importar datos desde Excel
    if (document.getElementById('importExcel')) {
        document.getElementById('importExcel').addEventListener('change', function(e) {
            var files = e.target.files;
            if (files.length === 0) return;
            
            var file = files[0];
            var reader = new FileReader();
            
            // Mostrar loader
            var loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'loading-overlay';
            loadingOverlay.innerHTML = '<div class="spinner"></div><div class="loading-text">Procesando archivo Excel...</div>';
            document.body.appendChild(loadingOverlay);
            
            reader.onload = function(event) {
                var data = new Uint8Array(event.target.result);
                var workbook = XLSX.read(data, {type: 'array'});
                
                // Obtener la primera hoja
                var firstSheet = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheet];
                
                // Convertir a JSON
                var jsonData = XLSX.utils.sheet_to_json(worksheet, {header: 1, raw: false, defval: ""});
                
                // Procesar datos y cargarlos en la tabla
                procesarDatosExcel(jsonData);
                
                // Quitar el overlay
                $('.loading-overlay').remove();
            };
            
            reader.readAsArrayBuffer(file);
        });
    }

    // Función para procesar datos de Excel
    function procesarDatosExcel(data) {
        if (!data || data.length <= 1) {
            showNotification('Error', 'El archivo Excel no contiene datos válidos', 'danger');
            return;
        }
        
        // Aquí procesar los encabezados y datos
        var headers = data[0];
        var rows = data.slice(1);
        
        // Aquí implementar lógica de carga de datos
        console.log('Datos de Excel procesados:', {headers: headers, totalRows: rows.length});
        
        // Ejemplo: mostrar datos en alguna tabla
        var tableBody = document.querySelector('#tabla-importacion tbody');
        if (tableBody) {
            var html = '';
            rows.forEach(function(row) {
                html += '<tr>';
                row.forEach(function(cell) {
                    html += '<td>' + (cell || '&nbsp;') + '</td>';
                });
                html += '</tr>';
            });
            tableBody.innerHTML = html;
            
            showNotification('Éxito', 'Se importaron ' + rows.length + ' registros', 'success');
        }
    }
});

// Reemplazar el alert para mostrar notificaciones estilizadas en formularios
$(document).ready(function() {
    // Reemplazar la función de manejo de formulario para venta (formulario principal)
    var originalFormExperian = $('#formExperian').off('submit');
    $('#formExperian').on('submit', function(e) {
        e.preventDefault();
        
        // Validación del formulario
        if (typeof validarFormularioVenta === 'function' && !validarFormularioVenta()) {
            return false;
        }
        
        // Mostrar loader
        var loadingOverlay = $('<div class="loading-overlay"><div class="spinner"></div><div class="loading-text">Procesando...</div></div>');
        $('body').append(loadingOverlay);
        
        // Enviar formulario usando AJAX
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: $(this).serialize(),
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showNotification('Éxito', response.message || 'Operación completada correctamente', 'success');
                    
                    // Resetear formulario si fue exitoso
                    $('#formExperian')[0].reset();
                    
                    // Si hay función de recarga, llamarla
                    if (typeof cargarEjecutivos === 'function') {
                        setTimeout(cargarEjecutivos, 1000);
                    }
                    
                    // Si hay redireccionamiento, hacerlo
                    if (response.redirect) {
                        setTimeout(function() {
                            window.location.href = response.redirect;
                        }, 2000);
                    }
                } else {
                    showNotification('Error', response.message || 'Ha ocurrido un error', 'danger');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error de envío:', {
                    status: status,
                    error: error,
                    response: xhr.responseText
                });
                
                showNotification('Error', 'Error en la comunicación con el servidor', 'danger');
            },
            complete: function() {
                // Quitar overlay de carga
                $('.loading-overlay').remove();
            }
        });
    });
});