/**
 * Sistema de notificaciones para formularios
 * Este script proporciona funciones para mostrar notificaciones estilizadas
 * en lugar de los alerts nativos del navegador.
 * 
 * También incluye captura global de errores de consola para mostrarlos como notificaciones.
 */

// Capturar errores de consola y mostrarlos como notificaciones
window.addEventListener('error', function(event) {
    // Extraer información del error
    const errorMsg = event.message || 'Error desconocido';
    const source = event.filename ? `Archivo: ${event.filename}` : '';
    const lineCol = event.lineno ? `Línea: ${event.lineno}, Columna: ${event.colno}` : '';
    
    // Crear mensaje formateado
    const fullMessage = `${errorMsg}<br>${source}<br>${lineCol}`;
    
    // Mostrar como notificación de error
    if (typeof mostrarMensaje === 'function') {
        mostrarMensaje(fullMessage, 'error', 8000);
    }
    
    // No prevenir el comportamiento por defecto para que también se muestre en consola
    // return true;
});

// Variable para rastrear notificaciones actuales
var activeNotifications = 0;
var notificationTimeout = null;

/**
 * Muestra un mensaje de notificación estilizado
 * @param {string} message - El mensaje a mostrar
 * @param {string} type - El tipo de mensaje (success, error, info)
 * @param {number} duration - Duración en ms antes de desaparecer (0 para quedarse hasta que se cierre manualmente)
 */
function mostrarMensaje(message, type = 'info', duration = 5000) {
    // Validar parámetros
    if (!message) return;
    type = ['success', 'error', 'info'].includes(type) ? type : 'info';
    
    // Incrementar contador de notificaciones
    activeNotifications++;

    // Crear elementos HTML para la notificación
    var notification = document.createElement('div');
    notification.className = 'notification ' + type;
    notification.id = 'notification-' + Date.now();
    
    // Íconos según tipo
    var icon = '';
    switch (type) {
        case 'success': icon = '<i class="fas fa-check-circle notification-icon"></i>'; break;
        case 'error': icon = '<i class="fas fa-exclamation-circle notification-icon"></i>'; break;
        case 'info': icon = '<i class="fas fa-info-circle notification-icon"></i>'; break;
    }
    
    // Contenido HTML
    notification.innerHTML = 
        icon + 
        '<div class="notification-message">' + message + '</div>' +
        '<div class="notification-close">&times;</div>';
    
    // Agregar al DOM
    document.body.appendChild(notification);
    
    // Ajustar posición vertical según número de notificaciones activas
    var topPosition = 20 + ((activeNotifications - 1) * 80);
    notification.style.top = topPosition + 'px';
    
    // Mostrar con animación
    setTimeout(function() {
        notification.classList.add('visible');
    }, 10);
    
    // Configurar cierre automático
    if (duration > 0) {
        notification.timeout = setTimeout(function() {
            cerrarNotificacion(notification.id);
        }, duration);
    }
    
    // Configurar evento para cerrar manualmente
    notification.querySelector('.notification-close').addEventListener('click', function() {
        cerrarNotificacion(notification.id);
    });
    
    return notification.id;
}

/**
 * Cierra una notificación por su ID
 * @param {string} id - ID de la notificación a cerrar
 */
function cerrarNotificacion(id) {
    var notification = document.getElementById(id);
    if (!notification) return;
    
    // Limpiar timeout si existe
    if (notification.timeout) {
        clearTimeout(notification.timeout);
    }
    
    // Animar salida
    notification.classList.add('removing');
    notification.classList.remove('visible');
    
    // Eliminar después de la animación
    setTimeout(function() {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
            activeNotifications--;
            
            // Reordenar notificaciones restantes
            document.querySelectorAll('.notification').forEach(function(notif, index) {
                notif.style.top = (20 + (index * 80)) + 'px';
            });
        }
    }, 300);
}

/**
 * Cierra todas las notificaciones activas
 */
function cerrarTodasNotificaciones() {
    document.querySelectorAll('.notification').forEach(function(notification) {
        if (notification.id) {
            cerrarNotificacion(notification.id);
        }
    });
}

/**
 * Muestra errores AJAX de manera amigable
 * @param {object} xhr - Objeto XMLHttpRequest
 * @param {string} status - Estado de la solicitud
 * @param {string} error - Mensaje de error
 */
function mostrarErrorAjax(xhr, status, error) {
    // Interpretar la respuesta del servidor
    let mensaje = 'Error en la solicitud: ';
    
    try {
        // Si hay respuesta JSON
        if (xhr.responseText && xhr.responseText.trim().startsWith('{')) {
            const respuesta = JSON.parse(xhr.responseText);
            if (respuesta.message) {
                mensaje += respuesta.message;
            } else {
                mensaje += (error || status || 'Error desconocido');
            }
        } else if (xhr.responseText) {
            // Si es una respuesta de texto
            // Extraer solo la parte relevante del mensaje de error HTML (si es HTML)
            if (xhr.responseText.includes('<body>')) {
                const bodyContent = xhr.responseText.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
                if (bodyContent && bodyContent[1]) {
                    mensaje += 'Error del servidor: ' + bodyContent[1].replace(/<[^>]*>/g, ' ').trim().substring(0, 150);
                    if (bodyContent[1].length > 150) mensaje += '...';
                } else {
                    mensaje += xhr.responseText.substring(0, 150);
                    if (xhr.responseText.length > 150) mensaje += '...';
                }
            } else {
                mensaje += xhr.responseText.substring(0, 150);
                if (xhr.responseText.length > 150) mensaje += '...';
            }
        } else {
            // Si no hay texto en la respuesta
            mensaje += (error || status || 'Error desconocido');
        }
    } catch (e) {
        mensaje += 'Error al procesar la respuesta del servidor';
        console.error('Error al procesar respuesta del servidor:', e);
    }
    
    // Añadir el código de estado HTTP
    if (xhr.status) {
        mensaje += ` (Código: ${xhr.status})`;
    }
    
    // Mostrar el mensaje de error
    mostrarMensaje(mensaje, 'error', 8000);
    
    // También loguearlo en consola
    console.error('Error AJAX:', {
        status: status,
        error: error,
        response: xhr.responseText,
        statusText: xhr.statusText
    });
}

// Configurar interceptor global para AJAX (jQuery)
if (typeof $ !== 'undefined' && $.ajaxSetup) {
    $.ajaxSetup({
        error: function(xhr, status, error) {
            mostrarErrorAjax(xhr, status, error);
        }
    });
}

// Asegurarse de que la función esté disponible globalmente
window.mostrarMensaje = mostrarMensaje;
window.cerrarNotificacion = cerrarNotificacion;
window.cerrarTodasNotificaciones = cerrarTodasNotificaciones;
window.mostrarErrorAjax = mostrarErrorAjax;