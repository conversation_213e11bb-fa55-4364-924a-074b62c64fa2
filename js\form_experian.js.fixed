// Esperar a que el DOM esté completamente cargado
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado completamente');

    // Función para comprobar si jQuery está disponible
    if (typeof jQuery === 'undefined') {
        console.error('jQuery no está disponible. Usando JavaScript nativo.');

        // Implementación con JavaScript nativo...
        // Todo el código de la versión sin jQuery
        
    } else {
        console.log('jQuery está disponible, versión: ' + jQuery.fn.jquery);

        $(document).ready(function() {
            // Todo el código de la versión con jQuery
            
            // Al final del documento ready dentro del else:
            
            // Botón para abrir modal de bitácora
            $(document).on('click', '.btn-bitacora', function() {
                const rut = $(this).data('rut');
                const nombre = $(this).data('nombre');
                const razon = $(this).data('razon');

                console.log('Botón de bitácora clickeado para RUT:', rut);

                // Establecer información en el modal
                $('#bitacora_rut').val(rut);
                $('#bitacora-info').html('<strong>Cliente:</strong> ' + razon + ' | <strong>Ejecutivo:</strong> ' + nombre + ' | <strong>RUT:</strong> ' + rut);

                // Cargar registros de bitácora
                cargarBitacora(rut);

                // Mostrar el modal
                $('#bitacoraModal').css('display', 'block');
            });
        }); // Fin de document ready
        
        // Función para cargar los registros de bitácora - Definida globalmente para que esté disponible en todo el ámbito
        window.cargarBitacora = function(rut) {
            console.log('Cargando bitácora para RUT:', rut);

            $.ajax({
                url: 'endpoints/obtener_bitacora.php',
                type: 'POST',
                data: {
                    rut: rut
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // Actualizar tabla de bitácora
                        if (response.data.length === 0) {
                            $('#bitacora-table tbody').html('<tr><td colspan="3" class="no-data">No hay registros disponibles</td></tr>');
                        } else {
                            let html = '';
                            $.each(response.data, function(index, registro) {
                                html += '<tr>';
                                html += '<td>' + registro.fecha_registro + '</td>';
                                html += '<td>' + registro.estado + '</td>';
                                html += '<td>' + registro.observaciones + '</td>';
                                html += '</tr>';
                            });
                            $('#bitacora-table tbody').html(html);
                        }
                    } else {
                        $('#bitacora-table tbody').html('<tr><td colspan="3" class="error-data">Error: ' + response.message + '</td></tr>');
                        console.error('Error al cargar bitácora:', response.message);
                    }
                },
                error: function(xhr, status, error) {
                    $('#bitacora-table tbody').html('<tr><td colspan="3" class="error-data">Error de conexión</td></tr>');
                    console.error('Error AJAX al cargar bitácora:', {
                        status: status,
                        error: error,
                        response: xhr.responseText
                    });
                }
            });
        }; // Fin de la función cargarBitacora
        
    } // Fin del else (jQuery está disponible)
    
}); // Fin del addEventListener DOMContentLoaded