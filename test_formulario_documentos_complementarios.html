<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Documentos Complementarios en Formulario</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
</head>
<body>
    <div class="container mt-5">
        <h2>🧪 Test - Documentos Complementarios en Formulario</h2>
        <p class="text-muted">Esta página simula el comportamiento del formulario de registro de prospectos con los nuevos documentos complementarios.</p>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Selector de Tipo de Persona</h5>
                    </div>
                    <div class="card-body">
                        <label for="tipo_persona_test" class="form-label">Tipo de Persona</label>
                        <select class="form-select" id="tipo_persona_test">
                            <option value="">Seleccionar...</option>
                            <option value="Natural">Persona Natural</option>
                            <option value="Juridica">Persona Jurídica</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Información</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Documentos esperados:</strong></p>
                        <ul>
                            <li><strong>Natural:</strong> 10 obligatorios + 7 opcionales = 17 total</li>
                            <li><strong>Jurídica:</strong> 8 obligatorios + 4 opcionales = 12 total</li>
                        </ul>
                        <p><strong>Nuevos documentos complementarios:</strong></p>
                        <ul class="text-success">
                            <li>✅ Mandato PAC (Complementario)</li>
                            <li>✅ Cuentas de abono de terceros</li>
                            <li>✅ Poder Notarial para cuentas de terceros</li>
                            <li>✅ Sociedad de Hecho - Declaración Jurada Notarial</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Checklist de Documentos Dinámico</h5>
                    </div>
                    <div class="card-body">
                        <div id="document-checklist-container">
                            <p class="text-muted">Seleccione un tipo de persona para ver los documentos requeridos.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Log de Pruebas</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-log" class="bg-light p-3" style="height: 200px; overflow-y: auto; font-family: monospace; font-size: 0.9rem;">
                            <div class="text-muted">Esperando selección de tipo de persona...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/inteletgroup-prospect.js"></script>
    
    <script>
        // Variables globales simuladas
        window.uploadedFiles = {};
        
        // Función para agregar logs
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'success' ? 'text-success' : type === 'error' ? 'text-danger' : 'text-info';
            
            const logEntry = document.createElement('div');
            logEntry.className = colorClass;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // Event listener para el selector de tipo de persona
        document.getElementById('tipo_persona_test').addEventListener('change', function() {
            const tipoPersona = this.value;
            addLog(`Tipo de persona seleccionado: ${tipoPersona}`);
            
            if (tipoPersona) {
                // Verificar que DOCUMENT_TYPES esté disponible
                if (typeof DOCUMENT_TYPES !== 'undefined') {
                    const documents = DOCUMENT_TYPES[tipoPersona] || [];
                    addLog(`Documentos encontrados: ${documents.length}`, 'success');
                    
                    // Contar documentos por tipo
                    const obligatorios = documents.filter(d => d.required).length;
                    const opcionales = documents.filter(d => !d.required).length;
                    const complementarios = documents.filter(d => d.id.startsWith('AMBOS_')).length;
                    
                    addLog(`- Obligatorios: ${obligatorios}`);
                    addLog(`- Opcionales: ${opcionales}`);
                    addLog(`- Complementarios (AMBOS_): ${complementarios}`, 'success');
                    
                    // Listar documentos complementarios
                    const docsComplementarios = documents.filter(d => d.id.startsWith('AMBOS_'));
                    if (docsComplementarios.length > 0) {
                        addLog('Documentos complementarios encontrados:', 'success');
                        docsComplementarios.forEach(doc => {
                            addLog(`  • ${doc.name} (${doc.id})`, 'success');
                        });
                    }
                    
                    // Llamar a la función original del formulario
                    if (typeof loadDocumentChecklist === 'function') {
                        loadDocumentChecklist(tipoPersona);
                        addLog('Checklist cargado exitosamente', 'success');
                    } else {
                        addLog('ERROR: Función loadDocumentChecklist no encontrada', 'error');
                    }
                } else {
                    addLog('ERROR: DOCUMENT_TYPES no está definido', 'error');
                }
            } else {
                document.getElementById('document-checklist-container').innerHTML = 
                    '<p class="text-muted">Seleccione un tipo de persona para ver los documentos requeridos.</p>';
                addLog('Checklist limpiado');
            }
        });
        
        // Log inicial
        addLog('Test iniciado - Verificando documentos complementarios');
        addLog('Verificando disponibilidad de DOCUMENT_TYPES...');
        
        // Verificar que el script se haya cargado correctamente
        setTimeout(() => {
            if (typeof DOCUMENT_TYPES !== 'undefined') {
                addLog('✅ DOCUMENT_TYPES cargado correctamente', 'success');
                addLog(`Tipos disponibles: ${Object.keys(DOCUMENT_TYPES).join(', ')}`, 'success');
            } else {
                addLog('❌ ERROR: DOCUMENT_TYPES no está disponible', 'error');
                addLog('Verifique que el archivo js/inteletgroup-prospect.js se haya cargado', 'error');
            }
        }, 1000);
    </script>
</body>
</html>
