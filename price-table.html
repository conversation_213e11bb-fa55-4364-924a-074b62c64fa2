<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Affan - PWA Mobile HTML Template">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>Affan - PWA Mobile HTML Template</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/favicon.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card -->
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">
      <!-- Header Content -->
      <div class="header-content position-relative d-flex align-items-center justify-content-between">
        <!-- Back Button -->
        <div class="back-button">
          <a href="elements.html">
            <i class="bi bi-arrow-left-short"></i>
          </a>
        </div>

        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0">Price Table</h6>
        </div>

        <!-- Settings -->
        <div class="setting-wrapper">
          <div class="setting-trigger-btn" id="settingTriggerBtn">
            <i class="bi bi-gear"></i>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="page-content-wrapper py-3">
    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading">
        <h6>Price Table 01</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <!-- Price Table One -->
          <div class="price-table-one">
            <ul class="nav nav-tabs border-bottom-0 mb-3 align-items-center justify-content-center" id="priceTab"
              role="tablist">
              <li class="nav-item" role="presentation">
                <a class="nav-link shadow" id="priceTabOne" data-bs-toggle="tab" href="#priceTab_One" role="tab"
                  aria-controls="priceTab_One" aria-selected="false">
                  <i class="bi bi-egg"></i>
                </a>
              </li>

              <li class="nav-item" role="presentation">
                <a class="nav-link active shadow" id="priceTabTwo" data-bs-toggle="tab" href="#priceTab_Two" role="tab"
                  aria-controls="priceTab_Two" aria-selected="true">
                  <i class="bi bi-lightning"></i>
                </a>
              </li>

              <li class="nav-item" role="presentation">
                <a class="nav-link shadow" id="priceTabThree" data-bs-toggle="tab" href="#priceTab_Three" role="tab"
                  aria-controls="priceTab_Three" aria-selected="false">
                  <i class="bi bi-cpu"></i>
                </a>
              </li>
            </ul>

            <div class="tab-content" id="priceTabContent">
              <div class="tab-pane fade" id="priceTab_One" role="tabpanel" aria-labelledby="priceTabOne">
                <!-- Single Price Table -->
                <div class="single-price-content shadow-sm">
                  <div class="price">
                    <span class="text-white mb-2">Intro</span>
                    <h2 class="display-3">$3.19</h2>
                    <span class="badge bg-light text-dark rounded-pill">Save -29%</span>
                  </div>
                  <!-- Pricing Desc -->
                  <div class="pricing-desc">
                    <ul class="ps-0">
                      <li><i class="bi bi-check-lg me-2"></i>3 Month Usage</li>
                      <li><i class="bi bi-check-lg me-2"></i>Lifetime Updates</li>
                      <li><i class="bi bi-check-lg me-2"></i>2 Website License</li>
                      <li class="times"><i class="bi bi-x-lg me-2"></i>Free Support</li>
                      <li class="times"><i class="bi bi-x-lg me-2"></i>Download New Release</li>
                    </ul>
                  </div>
                  <!-- Purchase -->
                  <div class="purchase">
                    <a class="btn btn-warning btn-lg btn-creative w-100" href="#">Choose Plan</a>
                    <small class="d-block text-white mt-2 ms-1">No credit card required*</small>
                  </div>
                </div>
              </div>

              <div class="tab-pane fade show active" id="priceTab_Two" role="tabpanel" aria-labelledby="priceTabTwo">
                <!-- Single Price Table -->
                <div class="single-price-content shadow-sm">
                  <div class="price">
                    <span class="text-white mb-2">Popular</span>
                    <h2 class="display-3">$19.19</h2>
                    <span class="badge bg-light text-dark rounded-pill">Save -29%</span>
                  </div>
                  <!-- Pricing Desc -->
                  <div class="pricing-desc">
                    <ul class="ps-0">
                      <li><i class="bi bi-check-lg me-2"></i>6 Month Usage</li>
                      <li><i class="bi bi-check-lg me-2"></i>Lifetime Updates</li>
                      <li><i class="bi bi-check-lg me-2"></i>10 Website License</li>
                      <li><i class="bi bi-check-lg me-2"></i>Free Support</li>
                      <li class="times"><i class="bi bi-x-lg me-2"></i>Download New Release</li>
                    </ul>
                  </div>
                  <!-- Purchase -->
                  <div class="purchase">
                    <a class="btn btn-light btn-lg btn-creative" href="#">Choose Plan</a>
                  </div>
                </div>
              </div>

              <div class="tab-pane fade" id="priceTab_Three" role="tabpanel" aria-labelledby="priceTabThree">
                <!-- Single Price Table -->
                <div class="single-price-content shadow-sm">
                  <div class="price">
                    <span class="text-white mb-2">Pro</span>
                    <h2 class="display-3">$49.99</h2>
                    <span class="badge bg-light text-dark rounded-pill">Save -29%</span>
                  </div>
                  <!-- Pricing Desc -->
                  <div class="pricing-desc">
                    <ul class="ps-0">
                      <li><i class="bi bi-check-lg me-2"></i>12 Month Usage</li>
                      <li><i class="bi bi-check-lg me-2"></i>Lifetime Updates</li>
                      <li><i class="bi bi-check-lg me-2"></i>Unlimited Website License</li>
                      <li><i class="bi bi-check-lg me-2"></i>Free Support</li>
                      <li><i class="bi bi-check-lg me-2"></i>Download New Release</li>
                    </ul>
                  </div>
                  <!-- Purchase -->
                  <div class="purchase">
                    <a class="btn btn-primary btn-lg btn-creative" href="#">Choose Plan</a>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Price Table 02</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <!-- Price Table Two -->
          <div class="price-table-two d-flex align-items-center">

            <!-- Single Price Table -->
            <div class="single-price-table active-effect">
              <div class="text">
                <h6 class="fz-14">Basic</h6>
                <span class="badge bg-primary rounded-pill">Save 7%</span>
              </div>
              <div class="price">
                <h3>$9</h3>
                <span class="fz-12">per month</span>
              </div>
              <div class="purchase">
                <div class="form-check mb-0">
                  <input class="form-check-input form-check-warning mx-0 shadow" type="radio" name="exampleRadio"
                    id="choosePlan1">
                  <label class="form-check-label" for="choosePlan1"></label>
                </div>
              </div>
            </div>

            <!-- Single Price Table -->
            <div class="single-price-table active-effect active">
              <div class="text">
                <h6 class="fz-14">Standard</h6>
                <span class="badge bg-primary rounded-pill">Save 16%</span>
              </div>
              <div class="price">
                <h3>$59</h3>
                <span class="fz-12">per month</span>
              </div>
              <div class="purchase">
                <div class="form-check">
                  <input class="form-check-input form-check-warning mx-0 shadow" type="radio" name="exampleRadio"
                    id="choosePlan2" checked>
                  <label class="form-check-label" for="choosePlan2"></label>
                </div>
              </div>
            </div>

            <!-- Single Price Table -->
            <div class="single-price-table active-effect">
              <div class="text">
                <h6 class="fz-14">Premium</h6>
                <span class="badge bg-primary rounded-pill">Save 23%</span>
              </div>
              <div class="price">
                <h3>$99</h3>
                <span class="fz-12">per month</span>
              </div>
              <div class="purchase">
                <div class="form-check">
                  <input class="form-check-input form-check-warning mx-0 shadow" type="radio" name="exampleRadio"
                    id="choosePlan3">
                  <label class="form-check-label" for="choosePlan3"></label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          <li class="active">
            <a href="home.html">
              <i class="bi bi-house"></i>
              <span>Home</span>
            </a>
          </li>

          <li>
            <a href="pages.html">
              <i class="bi bi-collection"></i>
              <span>Pages</span>
            </a>
          </li>

          <li>
            <a href="elements.html">
              <i class="bi bi-folder2-open"></i>
              <span>Elements</span>
            </a>
          </li>

          <li>
            <a href="chat-users.html">
              <i class="bi bi-chat-dots"></i>
              <span>Chat</span>
            </a>
          </li>

          <li>
            <a href="settings.html">
              <i class="bi bi-gear"></i>
              <span>Settings</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
</body>

</html>