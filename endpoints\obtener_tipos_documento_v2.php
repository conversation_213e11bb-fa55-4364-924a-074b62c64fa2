<?php
/**
 * Endpoint para obtener tipos de documentos - Versión simplificada y funcional
 */

// Primero, asegurar que no haya salida antes de los headers
ob_start();

// Headers CORS
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar OPTIONS para CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Solo aceptar POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Método no permitido']);
    exit();
}

try {
    error_log("obtener_tipos_documento_v2.php: Iniciando endpoint v2");

    // Obtener datos del request
    $input = json_decode(file_get_contents('php://input'), true);
    error_log("obtener_tipos_documento_v2.php: Input recibido: " . json_encode($input));

    if (!$input || !isset($input['tipo_persona'])) {
        error_log("obtener_tipos_documento_v2.php: Error - tipo_persona no proporcionado");
        throw new Exception('tipo_persona es requerido');
    }

    $tipo_persona = $input['tipo_persona'];
    error_log("obtener_tipos_documento_v2.php: Tipo de persona: " . $tipo_persona);

    // Validar tipo de persona
    if (!in_array($tipo_persona, ['Natural', 'Juridica'])) {
        error_log("obtener_tipos_documento_v2.php: Error - Tipo de persona inválido: " . $tipo_persona);
        throw new Exception('Tipo de persona inválido');
    }
    
    // Incluir conexión a base de datos
    $db_path = dirname(__DIR__) . '/con_db.php';
    error_log("obtener_tipos_documento_v2.php: Ruta de conexión: " . $db_path);

    if (!file_exists($db_path)) {
        error_log("obtener_tipos_documento_v2.php: Error - Archivo de conexión no encontrado");
        throw new Exception('Archivo de conexión no encontrado');
    }

    // Suprimir cualquier salida del archivo de conexión
    ob_start();
    require_once $db_path;
    ob_end_clean();

    error_log("obtener_tipos_documento_v2.php: Conexión incluida");
    error_log("obtener_tipos_documento_v2.php: conn disponible: " . (isset($conn) ? 'Sí' : 'No'));
    error_log("obtener_tipos_documento_v2.php: mysqli disponible: " . (isset($mysqli) ? 'Sí' : 'No'));

    // Verificar conexión
    if (!isset($conn) && !isset($mysqli)) {
        error_log("obtener_tipos_documento_v2.php: Error - No se pudo establecer conexión");
        throw new Exception('No se pudo establecer conexión con la base de datos');
    }

    $db = isset($conn) ? $conn : $mysqli;
    error_log("obtener_tipos_documento_v2.php: Usando conexión: " . (isset($conn) ? 'conn' : 'mysqli'));
    
    // Consulta SQL
    $sql = "SELECT
                id,
                codigo,
                nombre,
                descripcion,
                es_obligatorio,
                orden,
                tipo_persona
            FROM tb_inteletgroup_tipos_documento
            WHERE (tipo_persona = ? OR tipo_persona = 'Ambos')
            AND estado = 'Activo'
            ORDER BY es_obligatorio DESC, orden ASC";

    error_log("obtener_tipos_documento_v2.php: Preparando consulta SQL");

    $stmt = $db->prepare($sql);
    if (!$stmt) {
        error_log("obtener_tipos_documento_v2.php: Error preparando consulta: " . $db->error);
        throw new Exception('Error preparando consulta: ' . $db->error);
    }

    error_log("obtener_tipos_documento_v2.php: Consulta preparada exitosamente");

    $stmt->bind_param("s", $tipo_persona);
    error_log("obtener_tipos_documento_v2.php: Parámetros vinculados");

    if (!$stmt->execute()) {
        error_log("obtener_tipos_documento_v2.php: Error ejecutando consulta: " . $stmt->error);
        throw new Exception('Error ejecutando consulta: ' . $stmt->error);
    }

    error_log("obtener_tipos_documento_v2.php: Consulta ejecutada exitosamente");

    // Usar bind_result en lugar de get_result para compatibilidad con PHP 7.3
    error_log("obtener_tipos_documento_v2.php: Vinculando resultados");
    $stmt->bind_result($id, $codigo, $nombre, $descripcion, $es_obligatorio, $orden, $tipo_persona_result);

    $documentos = [];
    $contador = 0;
    while ($stmt->fetch()) {
        $contador++;
        $documento = [
            'id' => (int)$id,
            'codigo' => $codigo,
            'nombre' => $nombre,
            'descripcion' => $descripcion,
            'es_obligatorio' => (int)$es_obligatorio,
            'orden' => (int)$orden,
            'tipo_persona' => $tipo_persona_result
        ];
        $documentos[] = $documento;
        error_log("obtener_tipos_documento_v2.php: Documento $contador: " . json_encode($documento));
    }

    error_log("obtener_tipos_documento_v2.php: Total documentos encontrados: " . count($documentos));

    $stmt->close();

    // Limpiar cualquier salida anterior
    ob_end_clean();

    // Enviar respuesta exitosa
    $response = [
        'success' => true,
        'documentos' => $documentos,
        'total' => count($documentos)
    ];

    error_log("obtener_tipos_documento_v2.php: Enviando respuesta: " . json_encode($response));
    echo json_encode($response);
    
} catch (Exception $e) {
    // Limpiar buffer si hay error
    ob_end_clean();
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

// Cerrar conexión si existe
if (isset($db)) {
    $db->close();
}
?>