<!-- Modal para Registro de Prospectos -->
<div id="prospectModal" class="modal" style="display: none;">
    <div class="modal-content prospect-modal">
        <div class="modal-header">
            <h2>Registro de Nuevo Prospecto</h2>
            <span class="close" onclick="cerrarModalProspecto()">&times;</span>
        </div>
        
        <div class="modal-body">
            <!-- Resumen de errores -->
            <div id="errorSummary" class="error-summary" style="display: none;">
                <h4>Por favor corrija los siguientes errores:</h4>
                <ul id="errorList"></ul>
            </div>
            
            <!-- Indicador de carga -->
            <div id="loadingIndicator" class="loading-indicator" style="display: none;">
                <div class="spinner"></div>
                <p>Guardando prospecto...</p>
            </div>
            
            <form id="prospectForm" enctype="multipart/form-data">
                <!-- Información del Ejecutivo -->
                <div class="form-section">
                    <h3>Información del Ejecutivo</h3>
                    
                    <div class="form-group">
                        <label for="nombre_ejecutivo">Nombre de Ejecutivo *</label>
                        <input type="text" id="nombre_ejecutivo" name="nombre_ejecutivo" 
                               class="form-control" readonly>
                        <small class="form-text">Auto-poblado desde el usuario logueado</small>
                    </div>
                </div>
                
                <!-- Información del Cliente -->
                <div class="form-section">
                    <h3>Información del Cliente</h3>
                    
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="rut_ejecutivo">RUT Cliente *</label>
                            <input type="text" id="rut_ejecutivo" name="rut_ejecutivo" 
                                   class="form-control" placeholder="12345678-9" maxlength="12">
                            <small class="form-text">Sin puntos ni guiones</small>
                            <div class="error-message"></div>
                        </div>
                        
                        <div class="form-group col-md-6">
                            <label for="razon_social">Razón Social *</label>
                            <input type="text" id="razon_social" name="razon_social" 
                                   class="form-control" placeholder="EMPRESA EJEMPLO LTDA" style="text-transform: uppercase;">
                            <small class="form-text">Solo mayúsculas, sin acentos ni caracteres especiales</small>
                            <div class="error-message"></div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="rubro">Rubro</label>
                            <input type="text" id="rubro" name="rubro" class="form-control" 
                                   placeholder="Ej: Retail, Restaurante, Servicios">
                            <div class="error-message"></div>
                        </div>
                        
                        <div class="form-group col-md-6">
                            <label for="direccion_comercial">Dirección Comercial</label>
                            <input type="text" id="direccion_comercial" name="direccion_comercial" 
                                   class="form-control" placeholder="Dirección del local comercial">
                            <div class="error-message"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Información de Contacto -->
                <div class="form-section">
                    <h3>Información de Contacto</h3>
                    
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="telefono">Teléfono Celular *</label>
                            <input type="tel" id="telefono" name="telefono" class="form-control" 
                                   placeholder="987654321" pattern="[0-9]{9,}">
                            <small class="form-text">Mínimo 9 dígitos, solo números</small>
                            <div class="error-message"></div>
                        </div>
                        
                        <div class="form-group col-md-6">
                            <label for="email">Email</label>
                            <input type="email" id="email" name="email" class="form-control" 
                                   placeholder="<EMAIL>">
                            <div class="error-message"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Información Bancaria y Comercial -->
                <div class="form-section">
                    <h3>Información Bancaria y Comercial</h3>
                    
                    <div class="form-row">
                        <div class="form-group col-md-4">
                            <label for="num_pos">N° de POS</label>
                            <input type="number" id="num_pos" name="num_pos" class="form-control" 
                                   placeholder="1" min="1">
                            <div class="error-message"></div>
                        </div>
                        
                        <div class="form-group col-md-4">
                            <label for="tipo_cuenta">Tipo de cuenta</label>
                            <select id="tipo_cuenta" name="tipo_cuenta" class="form-control">
                                <option value="">Seleccionar...</option>
                                <option value="Cuenta Vista">Cuenta Vista</option>
                                <option value="Cuenta Corriente">Cuenta Corriente</option>
                            </select>
                            <div class="error-message"></div>
                        </div>
                        
                        <div class="form-group col-md-4">
                            <label for="num_cuenta_bancaria">N° de Cuenta Bancaria</label>
                            <input type="text" id="num_cuenta_bancaria" name="num_cuenta_bancaria" 
                                   class="form-control" placeholder="12345678">
                            <div class="error-message"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Horarios y Atención -->
                <div class="form-section">
                    <h3>Horarios y Atención</h3>
                    
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="dias_atencion">Días de atención</label>
                            <input type="text" id="dias_atencion" name="dias_atencion" 
                                   class="form-control" placeholder="Lunes a Viernes">
                            <div class="error-message"></div>
                        </div>
                        
                        <div class="form-group col-md-6">
                            <label for="horario_atencion">Horario de atención</label>
                            <input type="text" id="horario_atencion" name="horario_atencion" 
                                   class="form-control" placeholder="09:00 - 18:00">
                            <div class="error-message"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Información Comercial -->
                <div class="form-section">
                    <h3>Información Comercial</h3>
                    
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="contrata_boleta">Contrata Boleta</label>
                            <select id="contrata_boleta" name="contrata_boleta" class="form-control">
                                <option value="">Seleccionar...</option>
                                <option value="Sí">Sí</option>
                                <option value="No">No</option>
                            </select>
                            <div class="error-message"></div>
                        </div>
                        
                        <div class="form-group col-md-6">
                            <label for="competencia_actual">Competencia Actual</label>
                            <select id="competencia_actual" name="competencia_actual" class="form-control">
                                <option value="">Seleccionar...</option>
                                <option value="Transbank">Transbank</option>
                                <option value="Getnet">Getnet</option>
                                <option value="Compra Aquí (Bco Estado)">Compra Aquí (Bco Estado)</option>
                                <option value="Klap">Klap</option>
                                <option value="SumUp">SumUp</option>
                                <option value="Tuu">Tuu</option>
                                <option value="Ya Ganaste">Ya Ganaste</option>
                                <option value="Mercado Pago">Mercado Pago</option>
                            </select>
                            <div class="error-message"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Documentación y Observaciones -->
                <div class="form-section">
                    <h3>Documentación y Observaciones</h3>
                    
                    <div class="form-group">
                        <label for="archivo_documentacion">Documentación de Soporte</label>
                        <input type="file" id="archivo_documentacion" name="archivo_documentacion" 
                               class="form-control" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.zip,.rar">
                        <small class="form-text">Formatos permitidos: PDF, DOC, DOCX, JPG, PNG, ZIP, RAR (máx. 10MB)</small>
                        <div class="error-message"></div>
                    </div>
                    
                    <div class="form-group">
                        <label for="observaciones">Observaciones</label>
                        <textarea id="observaciones" name="observaciones" class="form-control" 
                                  rows="3" placeholder="Observaciones adicionales..."></textarea>
                        <div class="error-message"></div>
                    </div>
                </div>
                
                <!-- Botones de acción -->
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="cerrarModalProspecto()">Cancelar</button>
                    <button type="button" class="btn btn-warning" onclick="llenarFormularioTest()">Llenar Test</button>
                    <button type="submit" class="btn btn-primary">Guardar Prospecto</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Botón para abrir el modal -->
<button type="button" class="btn btn-success" onclick="abrirModalProspecto()">
    <i class="fas fa-plus"></i> Nuevo Prospecto
</button>
