<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Affan - PWA Mobile HTML Template">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>Affan - PWA Mobile HTML Template</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/favicon.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card -->
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">
      <!-- Header Content -->
      <div class="header-content position-relative d-flex align-items-center justify-content-between">
        <!-- Back Button -->
        <div class="back-button">
          <a href="pages.html">
            <i class="bi bi-arrow-left-short"></i>
          </a>
        </div>

        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0">Select Language</h6>
        </div>

        <!-- Settings -->
        <div class="setting-wrapper">
          <div class="setting-trigger-btn" id="settingTriggerBtn">
            <i class="bi bi-gear"></i>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Page Content Wrapper -->
  <div class="page-content-wrapper py-3">
    <div class="container">
      <!-- Language Area -->
      <div class="card">
        <div class="card-body">
          <div class="language-area-wrapper">
            <ul class="ps-0 language-lists">
              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="English" checked>
                  <label class="form-check-label" for="English">English</label>
                </div>
              </li>

              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="Afrikaans">
                  <label class="form-check-label" for="Afrikaans">Afrikaans</label>
                </div>
              </li>

              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="Albanian">
                  <label class="form-check-label" for="Albanian">Albanian</label>
                </div>
              </li>

              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="Amharic">
                  <label class="form-check-label" for="Amharic">Amharic</label>
                </div>
              </li>

              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="Arabic">
                  <label class="form-check-label" for="Arabic">Arabic</label>
                </div>
              </li>

              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="Armenian">
                  <label class="form-check-label" for="Armenian">Armenian</label>
                </div>
              </li>

              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="Azerbaijani">
                  <label class="form-check-label" for="Azerbaijani">Azerbaijani</label>
                </div>
              </li>

              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="Basque">
                  <label class="form-check-label" for="Basque">Basque</label>
                </div>
              </li>

              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="Belarusian">
                  <label class="form-check-label" for="Belarusian">Belarusian</label>
                </div>
              </li>

              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="Bengali">
                  <label class="form-check-label" for="Bengali">Bengali</label>
                </div>
              </li>

              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="Bosnian">
                  <label class="form-check-label" for="Bosnian">Bosnian</label>
                </div>
              </li>

              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="Bulgarian">
                  <label class="form-check-label" for="Bulgarian">Bulgarian</label>
                </div>
              </li>

              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="Catalan">
                  <label class="form-check-label" for="Catalan">Catalan</label>
                </div>
              </li>

              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="Cebuano">
                  <label class="form-check-label" for="Cebuano">Cebuano</label>
                </div>
              </li>

              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="Chichewa">
                  <label class="form-check-label" for="Chichewa">Chichewa</label>
                </div>
              </li>

              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="Chinese">
                  <label class="form-check-label" for="Chinese">Chinese</label>
                </div>
              </li>

              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="Corsican">
                  <label class="form-check-label" for="Corsican">Corsican</label>
                </div>
              </li>

              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="Croatian">
                  <label class="form-check-label" for="Croatian">Croatian</label>
                </div>
              </li>

              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="Czech">
                  <label class="form-check-label" for="Czech">Czech</label>
                </div>
              </li>

              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="Danish">
                  <label class="form-check-label" for="Danish">Danish</label>
                </div>
              </li>

              <li>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="languageRadio" id="Dutch">
                  <label class="form-check-label" for="Dutch">Dutch</label>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          <li class="active">
            <a href="home.html">
              <i class="bi bi-house"></i>
              <span>Home</span>
            </a>
          </li>

          <li>
            <a href="pages.html">
              <i class="bi bi-collection"></i>
              <span>Pages</span>
            </a>
          </li>

          <li>
            <a href="elements.html">
              <i class="bi bi-folder2-open"></i>
              <span>Elements</span>
            </a>
          </li>

          <li>
            <a href="chat-users.html">
              <i class="bi bi-chat-dots"></i>
              <span>Chat</span>
            </a>
          </li>

          <li>
            <a href="settings.html">
              <i class="bi bi-gear"></i>
              <span>Settings</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
</body>

</html>