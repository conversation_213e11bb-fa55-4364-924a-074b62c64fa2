<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Aumentar límites para evitar timeouts
ini_set('max_execution_time', 300);
ini_set('memory_limit', '256M');

// Log para debugging
error_log("=== INTELETGROUP_EXPORT_XLSX_SIMPLE.PHP INICIADO ===");

// Iniciar sesión
session_start();

// Verificar autenticación y permisos de administrador
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    die("Error: Usuario no autenticado o sin permisos para InteletGroup.");
}

// Prevenir caché del navegador
header("Cache-Control: no-cache, no-store, must-revalidate");
header("Pragma: no-cache");
header("Expires: 0");

// Incluir archivos necesarios
require_once 'con_db.php';

// Verificar conexión a base de datos
if (!isset($mysqli) || $mysqli->connect_error) {
    die("Error de conexión a la base de datos");
}

// Obtener parámetros de filtros (desde POST o GET)
$filtro_ejecutivo = $_POST['ejecutivo'] ?? $_GET['ejecutivo'] ?? 'todos';
$filtro_periodo = $_POST['periodo'] ?? $_GET['periodo'] ?? 'año';
$filtro_fecha_inicio = $_POST['fecha_inicio'] ?? $_GET['fecha_inicio'] ?? date('Y-01-01');
$filtro_fecha_fin = $_POST['fecha_fin'] ?? $_GET['fecha_fin'] ?? date('Y-12-31');

// Calcular fechas según el periodo seleccionado
switch($filtro_periodo) {
    case 'hoy':
        $filtro_fecha_inicio = date('Y-m-d');
        $filtro_fecha_fin = date('Y-m-d');
        break;
    case 'semana':
        $filtro_fecha_inicio = date('Y-m-d', strtotime('monday this week'));
        $filtro_fecha_fin = date('Y-m-d', strtotime('sunday this week'));
        break;
    case 'mes_actual':
        $filtro_fecha_inicio = date('Y-m-01');
        $filtro_fecha_fin = date('Y-m-t');
        break;
    case 'trimestre':
        $trimestre = ceil(date('n') / 3);
        $filtro_fecha_inicio = date('Y-') . sprintf('%02d', ($trimestre - 1) * 3 + 1) . '-01';
        $filtro_fecha_fin = date('Y-m-t', strtotime($filtro_fecha_inicio . ' +2 months'));
        break;
    case 'año':
        $filtro_fecha_inicio = date('Y-01-01');
        $filtro_fecha_fin = date('Y-12-31');
        break;
}

// Construir condición WHERE para filtros
$where_conditions = ["1=1"];
$params = [];
$types = "";

if ($filtro_ejecutivo !== 'todos') {
    $where_conditions[] = "p.usuario_id = ?";
    $params[] = $filtro_ejecutivo;
    $types .= "i";
}

$where_conditions[] = "DATE(p.fecha_registro) BETWEEN ? AND ?";
$params[] = $filtro_fecha_inicio;
$params[] = $filtro_fecha_fin;
$types .= "ss";

$where_clause = implode(" AND ", $where_conditions);

// Consulta simplificada para obtener solo las columnas que se muestran en el dashboard
$query = "
    SELECT
        p.id, 
        p.razon_social, 
        p.rut_cliente, 
        p.tipo_persona,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        p.fecha_registro
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE (u.proyecto = 'inteletGroup' OR u.proyecto IS NULL) AND " . $where_clause . "
    ORDER BY p.fecha_registro DESC";

$stmt = $mysqli->prepare($query);
if (!$stmt) {
    die("Error preparando consulta: " . $mysqli->error);
}

if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}

if (!$stmt->execute()) {
    die("Error ejecutando consulta: " . $stmt->error);
}

// Obtener resultados usando bind_result para compatibilidad con PHP 7.3
$id = $razon_social = $rut_cliente = $tipo_persona = $ejecutivo_nombre_usuario = $fecha_registro = null;
$stmt->bind_result($id, $razon_social, $rut_cliente, $tipo_persona, $ejecutivo_nombre_usuario, $fecha_registro);

$prospectos = [];
while ($stmt->fetch()) {
    $prospectos[] = [
        'ID' => $id,
        'Razón Social' => $razon_social,
        'RUT' => $rut_cliente,
        'Tipo' => $tipo_persona,
        'Ejecutivo' => $ejecutivo_nombre_usuario,
        'Documentos' => '0/0 (Total: 0)', // Simplificado por ahora
        'Completitud' => '0%', // Simplificado por ahora
        'Fecha Registro' => date('d/m/Y', strtotime($fecha_registro))
    ];
}

$stmt->close();

// Verificar si hay datos para exportar
if (empty($prospectos)) {
    die("No hay datos para exportar con los filtros seleccionados");
}

// Generar nombre de archivo descriptivo
$fecha_actual = date('Y-m-d_H-i-s');
$periodo_texto = $filtro_periodo;
$ejecutivo_texto = $filtro_ejecutivo === 'todos' ? 'todos' : 'ejecutivo_' . $filtro_ejecutivo;
$nombre_archivo = "prospectos_inteletgroup_{$periodo_texto}_{$ejecutivo_texto}_{$fecha_actual}.csv";

// Por simplicidad, generar CSV con extensión .csv
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $nombre_archivo . '"');
header('Cache-Control: max-age=0');

$output = fopen('php://output', 'w');

// BOM para UTF-8
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

if (!empty($prospectos)) {
    // Escribir encabezados
    fputcsv($output, array_keys($prospectos[0]));
    
    // Escribir datos
    foreach ($prospectos as $fila) {
        fputcsv($output, $fila);
    }
}

fclose($output);
exit;
?>
