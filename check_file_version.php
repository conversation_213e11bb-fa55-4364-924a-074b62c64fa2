<?php
// =================================================================
// Script: Verificar versión del archivo descargar_documento.php
// =================================================================

session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    die("❌ No autorizado");
}

echo "<h1>🔍 Verificación de Archivo descargar_documento.php</h1>";
echo "<hr>";

$file_path = __DIR__ . '/descargar_documento.php';

if (!file_exists($file_path)) {
    die("❌ Archivo descargar_documento.php no encontrado");
}

echo "<p>✅ Archivo encontrado: $file_path</p>";

// Leer el contenido
$content = file_get_contents($file_path);
$file_size = filesize($file_path);
$last_modified = date('Y-m-d H:i:s', filemtime($file_path));

echo "<p><strong>Tamaño:</strong> $file_size bytes</p>";
echo "<p><strong>Última modificación:</strong> $last_modified</p>";

// Verificar si tiene las correcciones
$has_text_plain = strpos($content, "'text/plain'") !== false;
$has_logging = strpos($content, 'error_log("=== INICIO DESCARGA DOCUMENTO ===")') !== false;
$has_improved_mime = strpos($content, "'text/csv'") !== false;

echo "<h2>🔍 Verificación de Correcciones</h2>";
echo "<p><strong>Tiene 'text/plain':</strong> " . ($has_text_plain ? '✅ SÍ' : '❌ NO') . "</p>";
echo "<p><strong>Tiene logging mejorado:</strong> " . ($has_logging ? '✅ SÍ' : '❌ NO') . "</p>";
echo "<p><strong>Tiene tipos MIME adicionales:</strong> " . ($has_improved_mime ? '✅ SÍ' : '❌ NO') . "</p>";

// Mostrar la sección de tipos permitidos
echo "<h2>📋 Tipos MIME Permitidos Actuales</h2>";
$start_pos = strpos($content, '$allowed_types = [');
if ($start_pos !== false) {
    $end_pos = strpos($content, '];', $start_pos);
    if ($end_pos !== false) {
        $allowed_section = substr($content, $start_pos, $end_pos - $start_pos + 2);
        echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
        echo htmlspecialchars($allowed_section);
        echo "</pre>";
    }
} else {
    echo "<p>❌ No se encontró la sección de tipos permitidos</p>";
}

// Verificar si hay errores de sintaxis
echo "<h2>🔧 Verificación de Sintaxis</h2>";
$syntax_check = shell_exec("php -l $file_path 2>&1");
if (strpos($syntax_check, 'No syntax errors') !== false) {
    echo "<p>✅ Sin errores de sintaxis</p>";
} else {
    echo "<p>❌ Errores de sintaxis encontrados:</p>";
    echo "<pre style='background: #ffe6e6; padding: 10px; border: 1px solid #ff9999;'>";
    echo htmlspecialchars($syntax_check);
    echo "</pre>";
}

echo "<hr>";
echo "<h2>🧪 Pruebas Rápidas</h2>";
echo "<p><a href='descargar_documento.php?id=4&action=view' target='_blank'>👁️ Probar archivo TXT (ID 4)</a></p>";
echo "<p><a href='descargar_documento.php?id=3&action=view' target='_blank'>👁️ Probar archivo PDF (ID 3)</a></p>";
echo "<p><a href='test_document_download.php'>🔙 Volver a Pruebas</a></p>";
?>
