<?php
// Script para visualizar logs (solo para depuración)

// Verificar autenticación
session_start();
if (\!isset($_SESSION['usuario_id'])) {
    header('HTTP/1.1 403 Forbidden');
    echo "Acceso denegado";
    exit;
}

// Solo permitir acceso a archivos de log específicos
$allowed_logs = ['export_errors.log', 'db_errors.log'];
$file = isset($_GET['file']) ? $_GET['file'] : '';

if (\!in_array($file, $allowed_logs)) {
    header('HTTP/1.1 403 Forbidden');
    echo "Archivo no permitido";
    exit;
}

$log_path = __DIR__ . '/logs/' . $file;

if (\!file_exists($log_path)) {
    echo "El archivo de log no existe o está vacío.";
    exit;
}

header('Content-Type: text/plain; charset=utf-8');
readfile($log_path);
