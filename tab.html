<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Affan - PWA Mobile HTML Template">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>Affan - PWA Mobile HTML Template</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/favicon.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card -->
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">
      <!-- Header Content -->
      <div class="header-content position-relative d-flex align-items-center justify-content-between">
        <!-- Back Button -->
        <div class="back-button">
          <a href="elements.html">
            <i class="bi bi-arrow-left-short"></i>
          </a>
        </div>

        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0">Tabs</h6>
        </div>

        <!-- Settings -->
        <div class="setting-wrapper">
          <div class="setting-trigger-btn" id="settingTriggerBtn">
            <i class="bi bi-gear"></i>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="page-content-wrapper py-3">
    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading">
        <h6>Standard Tab</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="standard-tab">
            <ul class="nav rounded-lg mb-2 p-2 shadow-sm" id="affanTabs1" role="tablist">
              <li class="nav-item" role="presentation">
                <button class="btn active" id="bootstrap-tab" data-bs-toggle="tab" data-bs-target="#bootstrap"
                  type="button" role="tab" aria-controls="bootstrap" aria-selected="true">RTL</button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="btn" id="pwa-tab" data-bs-toggle="tab" data-bs-target="#pwa" type="button" role="tab"
                  aria-controls="pwa" aria-selected="false">PWA</button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="btn" id="dark-tab" data-bs-toggle="tab" data-bs-target="#dark" type="button" role="tab"
                  aria-controls="dark" aria-selected="false">Dark</button>
              </li>
            </ul>

            <div class="tab-content rounded-lg p-3 shadow-sm" id="affanTabs1Content">
              <div class="tab-pane fade show active" id="bootstrap" role="tabpanel" aria-labelledby="bootstrap-tab">
                <h6>RTL Ready</h6>
                <p class="mb-0">Lorem ipsum, dolor sit amet consectetur adipisicing elit.</p>
              </div>

              <div class="tab-pane fade" id="pwa" role="tabpanel" aria-labelledby="pwa-tab">
                <h6>PWA Ready</h6>
                <p class="mb-0">Lorem ipsum, dolor sit amet consectetur adipisicing elit.</p>
              </div>

              <div class="tab-pane fade" id="dark" role="tabpanel" aria-labelledby="dark-tab">
                <h6>Dark Mode</h6>
                <p class="mb-0">Lorem ipsum, dolor sit amet consectetur adipisicing elit.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Colorful Tab</h6>
      </div>
    </div>

    <div class="container">
      <div class="card bg-primary bg-gradient">
        <div class="card-body">
          <div class="colorful-tab">
            <ul class="nav p-1 mb-3 shadow-sm" id="affanTab3" role="tablist">
              <li class="nav-item" role="presentation">
                <button class="btn btn-primary active" id="creative-tab" data-bs-toggle="tab" data-bs-target="#creative"
                  type="button" role="tab" aria-controls="creative" aria-selected="true">Creative</button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="btn btn-primary" id="modern-tab" data-bs-toggle="tab" data-bs-target="#modern"
                  type="button" role="tab" aria-controls="modern" aria-selected="false">Modern</button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="btn btn-primary" id="latest-tab" data-bs-toggle="tab" data-bs-target="#latest"
                  type="button" role="tab" aria-controls="latest" aria-selected="false">Latest</button>
              </li>
            </ul>

            <div class="tab-content shadow-sm p-3" id="affanTab3Content">
              <div class="tab-pane fade show active" id="creative" role="tabpanel" aria-labelledby="creative-tab">
                <h6 class="text-white">Creative design.</h6>
                <p class="mb-0 text-white">Lorem ipsum, dolor sit amet consectetur adipisicing elit.</p>
              </div>
              <div class="tab-pane fade" id="modern" role="tabpanel" aria-labelledby="modern-tab">
                <h6 class="text-white">Modern trends.</h6>
                <p class="mb-0 text-white">Lorem ipsum, dolor sit amet consectetur adipisicing elit.</p>
              </div>
              <div class="tab-pane fade" id="latest" role="tabpanel" aria-labelledby="latest-tab">
                <h6 class="text-white">Latest technology.</h6>
                <p class="mb-0 text-white">Lorem ipsum, dolor sit amet consectetur adipisicing elit.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Minimal Tab</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="minimal-tab">
            <ul class="nav nav-tabs mb-3" id="affanTab2" role="tablist">
              <li class="nav-item" role="presentation">
                <button class="btn active" id="sass-tab" data-bs-toggle="tab" data-bs-target="#sass" type="button"
                  role="tab" aria-controls="sass" aria-selected="true">Sass</button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="btn" id="npm-tab" data-bs-toggle="tab" data-bs-target="#npm" type="button" role="tab"
                  aria-controls="npm" aria-selected="false">NPM</button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="btn" id="gulp-tab" data-bs-toggle="tab" data-bs-target="#gulp" type="button" role="tab"
                  aria-controls="gulp" aria-selected="false">Gulp</button>
              </li>
            </ul>

            <div class="tab-content rounded-lg p-3" id="affanTab2Content">
              <div class="tab-pane fade show active" id="sass" role="tabpanel" aria-labelledby="sass-tab">
                <h6>Built with SASS</h6>
                <p class="mb-0">Lorem ipsum, dolor sit amet consectetur adipisicing elit.</p>
              </div>
              <div class="tab-pane fade" id="npm" role="tabpanel" aria-labelledby="npm-tab">
                <h6>Built with NPM</h6>
                <p class="mb-0">Lorem ipsum, dolor sit amet consectetur adipisicing elit.</p>
              </div>
              <div class="tab-pane fade" id="gulp" role="tabpanel" aria-labelledby="gulp-tab">
                <h6>Built with Gulp js</h6>
                <p class="mb-0">Lorem ipsum, dolor sit amet consectetur adipisicing elit.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Bootstrap Tab</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <ul class="nav nav-tabs" id="bootstrapTab" role="tablist">
            <li class="nav-item me-2" role="presentation">
              <button class="nav-link active" id="home-tab" data-bs-toggle="tab" data-bs-target="#home" type="button"
                role="tab" aria-controls="home" aria-selected="true">Home</button>
            </li>
            <li class="nav-item me-2" role="presentation">
              <button class="nav-link" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button"
                role="tab" aria-controls="profile" aria-selected="false">Profile</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button"
                role="tab" aria-controls="contact" aria-selected="false">Contact</button>
            </li>
          </ul>

          <div class="tab-content p-3 border-top-0" id="bootstrapTabContent">
            <div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">
              <h6>I'm tab one!</h6>
              <p class="mb-0">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Nam, impedit natus itaque fuga
                aperiam qui eos ut.</p>
            </div>
            <div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">
              <h6>I'm tab two!</h6>
              <p class="mb-0">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Nam, impedit natus itaque fuga
                aperiam qui eos ut.</p>
            </div>
            <div class="tab-pane fade" id="contact" role="tabpanel" aria-labelledby="contact-tab">
              <h6>I'm tab three!</h6>
              <p class="mb-0">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Nam, impedit natus itaque fuga
                aperiam qui eos ut.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          <li class="active">
            <a href="home.html">
              <i class="bi bi-house"></i>
              <span>Home</span>
            </a>
          </li>

          <li>
            <a href="pages.html">
              <i class="bi bi-collection"></i>
              <span>Pages</span>
            </a>
          </li>

          <li>
            <a href="elements.html">
              <i class="bi bi-folder2-open"></i>
              <span>Elements</span>
            </a>
          </li>

          <li>
            <a href="chat-users.html">
              <i class="bi bi-chat-dots"></i>
              <span>Chat</span>
            </a>
          </li>

          <li>
            <a href="settings.html">
              <i class="bi bi-gear"></i>
              <span>Settings</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
</body>

</html>