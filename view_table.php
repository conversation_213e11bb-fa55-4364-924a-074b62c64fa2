<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Iniciar sesión
session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || empty($_SESSION['usuario_id'])) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Usuario no autenticado'
    ]);
    exit;
}

// Verificar si el usuario tiene permisos
if ($_SESSION['usuario_id'] != 4) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'No tiene permisos para ver esta información'
    ]);
    exit;
}

// Incluir el archivo de conexión
require_once 'con_db.php';

// Establecer cabeceras para JSON
header('Content-Type: application/json');

try {
    // Preparar la consulta incluyendo los nuevos campos de documentos
    $sql = "SELECT id, tipo_cliente, rut, razon_social, 
            nombre_representante1, rut_representante1, nombre_representante2, rut_representante2,
            nombre_representante3, rut_representante3, sistema_creacion, fecha_creacion, notaria,
            actividad_economica, fecha_constitucion, direccion, comuna, pagina_web, email, telefono,
            clasificacion_sii, contacto_nombre, contacto_rut, contacto_telefono, contacto_email,
            contacto_backup_nombre, contacto_backup_rut, contacto_backup_telefono, contacto_backup_email,
            morosos_plan, morosos_consultas, morosos_uf, morosos_descuento, morosos_nuevo_valor,
            advanced_plan, advanced_consultas, advanced_uf, advanced_descuento, advanced_nuevo_valor,
            key_user_nombre, key_user_rut, key_user_email, key_user_telefono,
            key_user_backup_nombre, key_user_backup_rut, key_user_backup_email, key_user_backup_telefono,
            ruta_ci, ruta_erut, ruta_extracto, ruta_ci_frente, ruta_ci_detras, 
            ruta_carpeta_tributaria, ruta_consulta_terceros,
            fecha_creacion_registro, id_usuario 
            FROM form_experian 
            ORDER BY id DESC";
    
    // Para servidores sin mysqlnd, usamos bind_result y fetch en lugar de get_result
    $stmt = $mysqli->prepare($sql);
    
    if (!$stmt) {
        throw new Exception("Error en la preparación de la consulta: " . $mysqli->error);
    }
    
    // Ejecutar la consulta
    if (!$stmt->execute()) {
        throw new Exception("Error al ejecutar la consulta: " . $stmt->error);
    }
    
    // Obtener resultado usando bind_result en lugar de get_result
    
    // Primero, obtenemos los metadatos para determinar el número de columnas
    $meta = $stmt->result_metadata();
    
    if (!$meta) {
        throw new Exception("Error al obtener metadatos de los resultados");
    }
    
    // Crear un array para almacenar las referencias a las columnas
    $fields = array();
    $row = array();
    
    // Almacenar referencias a $row en $fields
    while ($field = $meta->fetch_field()) {
        $fields[$field->name] = &$row[$field->name];
    }
    
    // Llamar a bind_result con los parámetros
    call_user_func_array(array($stmt, 'bind_result'), $fields);
    
    // Recolectar los resultados
    $data = array();
    
    while ($stmt->fetch()) {
        $rowData = array();
        
        // Copiar los valores a un nuevo array (para evitar problemas con referencias)
        foreach ($row as $key => $val) {
            $rowData[$key] = $val;
            
            // Formatear fechas para mejor legibilidad
            if (in_array($key, ['fecha_creacion', 'fecha_constitucion']) && !empty($val)) {
                $rowData[$key] = date('d/m/Y', strtotime($val));
            } else if ($key === 'fecha_creacion_registro' && !empty($val)) {
                $rowData[$key] = date('d/m/Y H:i:s', strtotime($val));
            }
            
            // Convertir las rutas de documentos en enlaces si existen
            if (strpos($key, 'ruta_') === 0 && !empty($val)) {
                $nombreArchivo = basename($val);
                $rowData[$key] = '<a href="'.$val.'" target="_blank">'.$nombreArchivo.'</a>';
            }
        }
        
        $data[] = $rowData;
    }
    
    // Cerrar statement y liberar los metadatos
    $meta->free();
    $stmt->close();
    
    // Devolver datos en formato JSON
    echo json_encode([
        'success' => true,
        'data' => $data
    ]);
    
} catch (Exception $e) {
    // En caso de error
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
    
    // Registrar el error en el log
    error_log('Error en view_table.php: ' . $e->getMessage());
}
?>
