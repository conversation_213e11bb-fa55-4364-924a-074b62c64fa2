<?php
// Configurar encabezados para permitir CORS y especificar el tipo de contenido
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Max-Age: 3600');

// Iniciar sesión si no está iniciada
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verificar si el usuario está autenticado
if (!isset($_SESSION['usuario_id'])) {
    echo json_encode([
        'success' => false, 
        'message' => 'Usuario no autenticado',
        'debug' => [
            'session_status' => session_status(),
            'session_data' => $_SESSION
        ]
    ]);
    exit;
}

// Devolver información de depuración
echo json_encode([
    'success' => true,
    'message' => 'Endpoint de depuración funcionando correctamente',
    'debug' => [
        'method' => $_SERVER['REQUEST_METHOD'],
        'uri' => $_SERVER['REQUEST_URI'],
        'timestamp' => date('Y-m-d H:i:s'),
        'session_id' => session_id(),
        'usuario_id' => $_SESSION['usuario_id'],
        'get_params' => $_GET,
        'server_info' => [
            'php_version' => phpversion(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'],
            'document_root' => $_SERVER['DOCUMENT_ROOT']
        ]
    ]
]);
exit;
?>
