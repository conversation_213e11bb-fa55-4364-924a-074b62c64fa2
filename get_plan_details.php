<?php
require_once 'con_db.php';

// Log de depuración
error_log("Solicitud recibida en get_plan_details.php");
error_log("POST data: " . print_r($_POST, true));

if (!isset($_POST['plan'])) {
    error_log("Error: No se especificó el plan");
    echo json_encode(['success' => false, 'message' => 'No se especificó el plan']);
    exit;
}

$plan = $_POST['plan'];
error_log("Plan seleccionado: " . $plan);

try {
    if (!isset($mysqli)) {
        error_log("Error: Variable mysqli no está definida");
        throw new Exception("Error: No se pudo establecer la conexión a la base de datos");
    }

    $stmt = $mysqli->prepare("SELECT plan_consumo, valor_uf FROM tb_experian_publicacion_morosos WHERE plan = ? AND vigente = 'SI' LIMIT 1");
    if (!$stmt) {
        error_log("Error en la preparación de la consulta: " . $mysqli->error);
        throw new Exception("Error en la preparación: " . $mysqli->error);
    }

    $stmt->bind_param("s", $plan);
    if (!$stmt->execute()) {
        error_log("Error en la ejecución de la consulta: " . $stmt->error);
        throw new Exception("Error en la ejecución: " . $stmt->error);
    }

    // Vincular las variables de resultado
    $plan_consumo = null;
    $valor_uf = null;
    $stmt->bind_result($plan_consumo, $valor_uf);
    
    if ($stmt->fetch()) {
        error_log("Datos encontrados - Plan consumo: $plan_consumo, Valor UF: $valor_uf");
        echo json_encode([
            'success' => true,
            'data' => [
                'plan_consumo' => $plan_consumo,
                'uf_mensual' => $valor_uf // Mantenemos el nombre uf_mensual en la respuesta para mantener compatibilidad
            ]
        ]);
    } else {
        error_log("No se encontraron datos para el plan: $plan");
        echo json_encode(['success' => false, 'message' => 'Plan no encontrado']);
    }

} catch (Exception $e) {
    error_log("Error en get_plan_details.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error en la base de datos: ' . $e->getMessage()]);
} finally {
    if (isset($stmt)) {
        $stmt->close();
    }
}

$mysqli->close();