<?php
// =================================================================
// Script de Corrección: Arreglar descargar_documento.php
// Descripción: Aplica las correcciones necesarias al archivo
// =================================================================

// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    die("❌ No autorizado");
}

echo "<h1>🔧 Corrección de descargar_documento.php</h1>";
echo "<hr>";

$file_path = __DIR__ . '/descargar_documento.php';

if (!file_exists($file_path)) {
    die("❌ Archivo descargar_documento.php no encontrado");
}

echo "<p>✅ Archivo encontrado: $file_path</p>";

// Leer el contenido actual
$content = file_get_contents($file_path);

if ($content === false) {
    die("❌ Error al leer el archivo");
}

echo "<p>✅ Archivo leído exitosamente (" . strlen($content) . " bytes)</p>";

// Verificar si ya tiene las correcciones
if (strpos($content, 'text/plain') !== false) {
    echo "<p>⚠️ El archivo ya parece tener las correcciones aplicadas</p>";
} else {
    echo "<p>🔄 Aplicando correcciones...</p>";
    
    // Buscar y reemplazar la lista de tipos permitidos
    $old_allowed_types = "'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'";

    $new_allowed_types = "'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/csv',
    'application/rtf',
    'application/zip',
    'application/x-zip-compressed'";

    // Aplicar el reemplazo
    $new_content = str_replace($old_allowed_types, $new_allowed_types, $content);
    
    if ($new_content === $content) {
        echo "<p>⚠️ No se pudo aplicar la corrección automáticamente</p>";
        echo "<p>Mostrando contenido actual para revisión manual:</p>";
        echo "<textarea style='width: 100%; height: 200px;'>" . htmlspecialchars(substr($content, 0, 2000)) . "</textarea>";
    } else {
        // Crear backup
        $backup_path = $file_path . '.backup.' . date('Y-m-d_H-i-s');
        if (file_put_contents($backup_path, $content)) {
            echo "<p>✅ Backup creado: $backup_path</p>";
            
            // Aplicar la corrección
            if (file_put_contents($file_path, $new_content)) {
                echo "<p>✅ Corrección aplicada exitosamente</p>";
                echo "<p>🎉 El archivo descargar_documento.php ha sido corregido</p>";
            } else {
                echo "<p>❌ Error al escribir el archivo corregido</p>";
            }
        } else {
            echo "<p>❌ Error al crear backup</p>";
        }
    }
}

echo "<hr>";
echo "<h2>🧪 Probar Correcciones</h2>";
echo "<p><a href='descargar_documento.php?id=4&action=view' target='_blank'>👁️ Probar archivo TXT</a></p>";
echo "<p><a href='descargar_documento.php?id=3&action=view' target='_blank'>👁️ Probar archivo PDF</a></p>";
echo "<p><a href='inteletgroup_documentos.php'>🔙 Volver a Gestión de Documentos</a></p>";
?>
