<?php
session_start();
header('Content-Type: application/json');

echo json_encode([
    'session_id' => session_id(),
    'session_data' => $_SESSION,
    'usuario_id' => $_SESSION['usuario_id'] ?? 'NO_SET',
    'proyecto' => $_SESSION['proyecto'] ?? 'NO_SET',
    'nombre_usuario' => $_SESSION['nombre_usuario'] ?? 'NO_SET',
    'session_status' => session_status(),
    'cookie_params' => session_get_cookie_params(),
    'server_request_method' => $_SERVER['REQUEST_METHOD'] ?? 'NO_SET',
    'server_http_x_requested_with' => $_SERVER['HTTP_X_REQUESTED_WITH'] ?? 'NO_SET'
], JSON_PRETTY_PRINT);
?>
