<?php
// Iniciar output buffering para capturar cualquier salida no deseada
ob_start();

// Headers para prevenir caché
header('Content-Type: application/json');
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");
header("Expires: Sat, 26 Jul 1997 05:00:00 GMT");

// Configuración para debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // No mostrar errores directamente al usuario

// Función para registrar errores en archivo de log
function log_password_action($message, $level = 'INFO', $usuario_id = null) {
    $log_file = dirname(__FILE__) . '/logs/password_actions.log';
    $log_dir = dirname($log_file);
    
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $date = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
    $usuario_info = $usuario_id ? "[Usuario: $usuario_id]" : '';
    $log_message = "[$date] [$level] [$ip] $usuario_info [UA: $user_agent] $message" . PHP_EOL;
    error_log($log_message, 3, $log_file);
}

try {
    require_once("con_db.php");
    
    // Verificar que la solicitud sea POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Método no permitido: " . $_SERVER['REQUEST_METHOD']);
    }
    
    // Verificar la acción solicitada
    if (!isset($_POST['action'])) {
        throw new Exception("Acción no especificada");
    }
    
    $action = $_POST['action'];
    
    switch ($action) {
        case 'create_password':
            createPassword($mysqli);
            break;

        case 'change_password':
            session_start();
            changePassword($mysqli);
            break;

        case 'get_available_users':
            getAvailableUsers($mysqli);
            break;

        default:
            throw new Exception("Acción no reconocida: $action");
    }
    
} catch (Exception $e) {
    log_password_action("ERROR: " . $e->getMessage(), 'ERROR');
    
    // Limpiar cualquier salida no deseada antes de enviar JSON
    ob_clean();
    
    echo json_encode([
        'success' => false,
        'message' => 'Error al procesar la solicitud: ' . $e->getMessage()
    ]);
} finally {
    // Cerrar recursos
    if (isset($mysqli)) {
        $mysqli->close();
    }
    
    // Finalizar output buffering
    if (ob_get_level()) {
        ob_end_flush();
    }
}

/**
 * Función para crear una nueva contraseña
 */
function createPassword($mysqli) {
    log_password_action("Iniciando proceso de creación de contraseña", 'INFO');
    
    // Validar campos requeridos
    if (!isset($_POST['user_id']) || !isset($_POST['email']) || !isset($_POST['password']) || !isset($_POST['confirm_password'])) {
        throw new Exception("Campos requeridos faltantes");
    }

    $userId = intval($_POST['user_id']);
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    $confirmPassword = $_POST['confirm_password'];
    
    // Validar formato de email
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception("Formato de correo electrónico inválido");
    }
    
    // Validar que las contraseñas coincidan
    if ($password !== $confirmPassword) {
        throw new Exception("Las contraseñas no coinciden");
    }
    
    // Validar requisitos de seguridad de la contraseña
    $passwordErrors = validatePasswordRequirements($password);
    if (!empty($passwordErrors)) {
        throw new Exception("La contraseña no cumple los requisitos: " . implode(", ", $passwordErrors));
    }
    
    // Verificar si el usuario existe
    $stmt = $mysqli->prepare("SELECT id, correo, clave FROM tb_experian_usuarios WHERE id = ?");
    if (!$stmt) {
        throw new Exception("Error en la preparación de la consulta: " . $mysqli->error);
    }

    $stmt->bind_param("i", $userId);
    $stmt->execute();

    // Usar bind_result para compatibilidad con PHP 7.3.33
    $dbUserId = null;
    $userEmail = null;
    $userClave = null;
    $stmt->bind_result($dbUserId, $userEmail, $userClave);

    if (!$stmt->fetch()) {
        $stmt->close();
        throw new Exception("No existe el usuario seleccionado");
    }

    $user = array(
        'id' => $dbUserId,
        'correo' => $userEmail,
        'clave' => $userClave
    );
    
    // Permitir crear/actualizar contraseña independientemente de si ya tiene una
    // (Comentario: Se permite cambiar la contraseña sin restricciones)
    
    $stmt->close();
    
    // Iniciar transacción
    $mysqli->begin_transaction();
    
    try {
        // Generar hash de la contraseña
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);
        
        // Actualizar la contraseña y el correo en tb_experian_usuarios
        $updateStmt = $mysqli->prepare("UPDATE tb_experian_usuarios SET clave = ?, correo = ? WHERE id = ?");
        if (!$updateStmt) {
            throw new Exception("Error en la preparación de la actualización: " . $mysqli->error);
        }

        // Usar la contraseña sin hash para mantener compatibilidad con el sistema actual
        $updateStmt->bind_param("ssi", $password, $email, $userId);
        if (!$updateStmt->execute()) {
            throw new Exception("Error al actualizar la contraseña: " . $updateStmt->error);
        }
        $updateStmt->close();
        
        // Insertar en el historial de contraseñas
        $historyStmt = $mysqli->prepare("INSERT INTO tb_password_history (usuario_id, password_hash, created_by_ip, user_agent, is_active) VALUES (?, ?, ?, ?, 1)");
        if (!$historyStmt) {
            throw new Exception("Error en la preparación del historial: " . $mysqli->error);
        }
        
        $ip = $_SERVER['REMOTE_ADDR'] ?? null;
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        
        $historyStmt->bind_param("isss", $userId, $passwordHash, $ip, $userAgent);
        if (!$historyStmt->execute()) {
            throw new Exception("Error al insertar en el historial: " . $historyStmt->error);
        }
        $historyStmt->close();
        
        // Registrar en el log de cambios
        $logStmt = $mysqli->prepare("INSERT INTO tb_password_change_log (usuario_id, action, ip_address, user_agent, details) VALUES (?, 'created', ?, ?, 'Contraseña creada por primera vez')");
        if (!$logStmt) {
            throw new Exception("Error en la preparación del log: " . $mysqli->error);
        }
        
        $logStmt->bind_param("iss", $userId, $ip, $userAgent);
        $logStmt->execute();
        $logStmt->close();
        
        // Confirmar transacción
        $mysqli->commit();
        
        log_password_action("Contraseña actualizada exitosamente para usuario: $email", 'INFO', $userId);

        // Limpiar cualquier salida no deseada antes de enviar JSON
        ob_clean();

        echo json_encode([
            'success' => true,
            'message' => 'Contraseña actualizada exitosamente'
        ]);
        
    } catch (Exception $e) {
        // Revertir transacción en caso de error
        $mysqli->rollback();
        throw $e;
    }
}

/**
 * Función para cambiar una contraseña existente
 */
function changePassword($mysqli) {
    // Verificar que el usuario esté autenticado
    if (!isset($_SESSION['usuario_id'])) {
        throw new Exception("Usuario no autenticado");
    }
    
    $userId = $_SESSION['usuario_id'];
    
    log_password_action("Iniciando proceso de cambio de contraseña", 'INFO', $userId);
    
    // Validar campos requeridos
    if (!isset($_POST['current_password']) || !isset($_POST['new_password']) || !isset($_POST['confirm_password'])) {
        throw new Exception("Campos requeridos faltantes");
    }
    
    $currentPassword = $_POST['current_password'];
    $newPassword = $_POST['new_password'];
    $confirmPassword = $_POST['confirm_password'];
    
    // Validar que las contraseñas nuevas coincidan
    if ($newPassword !== $confirmPassword) {
        throw new Exception("Las contraseñas nuevas no coinciden");
    }
    
    // Validar requisitos de seguridad de la nueva contraseña
    $passwordErrors = validatePasswordRequirements($newPassword);
    if (!empty($passwordErrors)) {
        throw new Exception("La nueva contraseña no cumple los requisitos: " . implode(", ", $passwordErrors));
    }
    
    // Verificar la contraseña actual
    $stmt = $mysqli->prepare("SELECT clave FROM tb_experian_usuarios WHERE id = ?");
    if (!$stmt) {
        throw new Exception("Error en la preparación de la consulta: " . $mysqli->error);
    }

    $stmt->bind_param("i", $userId);
    $stmt->execute();

    // Usar bind_result para compatibilidad con PHP 7.3.33
    $userClave = null;
    $stmt->bind_result($userClave);

    if (!$stmt->fetch()) {
        $stmt->close();
        throw new Exception("Usuario no encontrado");
    }

    $user = array('clave' => $userClave);
    $stmt->close();
    
    // Verificar la contraseña actual (compatibilidad con sistema actual)
    if ($currentPassword !== $user['clave']) {
        // Si no coincide directamente, verificar contra el hash en el historial
        $historyStmt = $mysqli->prepare("SELECT password_hash FROM tb_password_history WHERE usuario_id = ? AND is_active = 1 ORDER BY created_at DESC LIMIT 1");
        if ($historyStmt) {
            $historyStmt->bind_param("i", $userId);
            $historyStmt->execute();

            // Usar bind_result para compatibilidad con PHP 7.3.33
            $passwordHash = null;
            $historyStmt->bind_result($passwordHash);

            if ($historyStmt->fetch()) {
                if (!password_verify($currentPassword, $passwordHash)) {
                    $historyStmt->close();
                    throw new Exception("La contraseña actual es incorrecta");
                }
            } else {
                $historyStmt->close();
                throw new Exception("La contraseña actual es incorrecta");
            }
            $historyStmt->close();
        } else {
            throw new Exception("La contraseña actual es incorrecta");
        }
    }
    
    // Verificar que la nueva contraseña no sea igual a las últimas 3 contraseñas
    $checkStmt = $mysqli->prepare("SELECT password_hash FROM tb_password_history WHERE usuario_id = ? ORDER BY created_at DESC LIMIT 3");
    if ($checkStmt) {
        $checkStmt->bind_param("i", $userId);
        $checkStmt->execute();

        // Usar bind_result para compatibilidad con PHP 7.3.33
        $passwordHash = null;
        $checkStmt->bind_result($passwordHash);

        while ($checkStmt->fetch()) {
            if (password_verify($newPassword, $passwordHash)) {
                $checkStmt->close();
                throw new Exception("La nueva contraseña no puede ser igual a las últimas 3 contraseñas utilizadas");
            }
        }
        $checkStmt->close();
    }
    
    // Iniciar transacción
    $mysqli->begin_transaction();
    
    try {
        // Generar hash de la nueva contraseña
        $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
        
        // Actualizar contraseñas anteriores como inactivas
        $deactivateStmt = $mysqli->prepare("UPDATE tb_password_history SET is_active = 0 WHERE usuario_id = ?");
        if (!$deactivateStmt) {
            throw new Exception("Error al desactivar contraseñas anteriores: " . $mysqli->error);
        }
        
        $deactivateStmt->bind_param("i", $userId);
        $deactivateStmt->execute();
        $deactivateStmt->close();
        
        // Actualizar la contraseña en tb_experian_usuarios
        $updateStmt = $mysqli->prepare("UPDATE tb_experian_usuarios SET clave = ? WHERE id = ?");
        if (!$updateStmt) {
            throw new Exception("Error en la preparación de la actualización: " . $mysqli->error);
        }
        
        // Usar la contraseña sin hash para mantener compatibilidad con el sistema actual
        $updateStmt->bind_param("si", $newPassword, $userId);
        if (!$updateStmt->execute()) {
            throw new Exception("Error al actualizar la contraseña: " . $updateStmt->error);
        }
        $updateStmt->close();
        
        // Insertar en el historial de contraseñas
        $historyStmt = $mysqli->prepare("INSERT INTO tb_password_history (usuario_id, password_hash, created_by_ip, user_agent, is_active) VALUES (?, ?, ?, ?, 1)");
        if (!$historyStmt) {
            throw new Exception("Error en la preparación del historial: " . $mysqli->error);
        }
        
        $ip = $_SERVER['REMOTE_ADDR'] ?? null;
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        
        $historyStmt->bind_param("isss", $userId, $passwordHash, $ip, $userAgent);
        if (!$historyStmt->execute()) {
            throw new Exception("Error al insertar en el historial: " . $historyStmt->error);
        }
        $historyStmt->close();
        
        // Registrar en el log de cambios
        $logStmt = $mysqli->prepare("INSERT INTO tb_password_change_log (usuario_id, action, ip_address, user_agent, details) VALUES (?, 'changed', ?, ?, 'Contraseña cambiada por el usuario')");
        if (!$logStmt) {
            throw new Exception("Error en la preparación del log: " . $mysqli->error);
        }
        
        $logStmt->bind_param("iss", $userId, $ip, $userAgent);
        $logStmt->execute();
        $logStmt->close();
        
        // Confirmar transacción
        $mysqli->commit();
        
        log_password_action("Contraseña cambiada exitosamente", 'INFO', $userId);
        
        // Limpiar cualquier salida no deseada antes de enviar JSON
        ob_clean();
        
        echo json_encode([
            'success' => true,
            'message' => 'Contraseña cambiada exitosamente'
        ]);
        
    } catch (Exception $e) {
        // Revertir transacción en caso de error
        $mysqli->rollback();
        throw $e;
    }
}

/**
 * Función para validar los requisitos de seguridad de la contraseña
 */
function validatePasswordRequirements($password) {
    $errors = [];
    
    // Mínimo 8 caracteres
    if (strlen($password) < 8) {
        $errors[] = "mínimo 8 caracteres";
    }
    
    // Al menos 1 letra mayúscula
    if (!preg_match('/[A-Z]/', $password)) {
        $errors[] = "al menos 1 letra mayúscula";
    }
    
    // Al menos 1 letra minúscula
    if (!preg_match('/[a-z]/', $password)) {
        $errors[] = "al menos 1 letra minúscula";
    }
    
    // Al menos 1 número
    if (!preg_match('/[0-9]/', $password)) {
        $errors[] = "al menos 1 número";
    }
    
    // Al menos 1 carácter especial
    if (!preg_match('/[!@#$%^&*]/', $password)) {
        $errors[] = "al menos 1 carácter especial (!@#$%^&*)";
    }
    
    // No debe contener espacios
    if (preg_match('/\s/', $password)) {
        $errors[] = "no debe contener espacios";
    }
    
    return $errors;
}

/**
 * Función para obtener usuarios disponibles para crear contraseña
 */
function getAvailableUsers($mysqli) {
    log_password_action("Obteniendo lista de usuarios disponibles", 'INFO');

    // Obtener solo usuarios del proyecto inteletGroup
    $stmt = $mysqli->prepare("SELECT id, correo, nombre_usuario FROM tb_experian_usuarios WHERE proyecto = 'inteletGroup' ORDER BY nombre_usuario");
    if (!$stmt) {
        throw new Exception("Error en la preparación de la consulta: " . $mysqli->error);
    }

    $stmt->execute();

    // Usar bind_result para compatibilidad con PHP 7.3.33
    $users = array();
    $userId = null;
    $userEmail = null;
    $userName = null;
    $stmt->bind_result($userId, $userEmail, $userName);

    while ($stmt->fetch()) {
        $users[] = array(
            'id' => $userId,
            'correo' => $userEmail,
            'nombre_usuario' => $userName
        );
    }

    $stmt->close();

    log_password_action("Se encontraron " . count($users) . " usuarios disponibles", 'INFO');

    // Limpiar cualquier salida no deseada antes de enviar JSON
    ob_clean();

    echo json_encode([
        'success' => true,
        'users' => $users,
        'message' => 'Usuarios cargados correctamente'
    ]);
}
?>