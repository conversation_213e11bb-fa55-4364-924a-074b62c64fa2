<?php
// Test de procesamiento de archivos sin servidor web
// Este script simula el proceso de carga de archivos

echo "=== TEST DE PROCESAMIENTO DE ARCHIVOS ===\n\n";

// Simular datos de sesión
session_start();
$_SESSION['usuario_id'] = 1;
$_SESSION['nombre_usuario'] = 'Test User';

// Incluir conexión a base de datos
require_once 'con_db.php';

// Simular datos del formulario
$_POST = [
    'nombre_ejecutivo' => 'Test User',
    'rut_cliente' => '********-9',
    'razon_social' => 'EMPRESA TEST LTDA',
    'rubro' => 'COMERCIO',
    'direccion_comercial' => 'Av. Test 123, Santiago',
    'telefono_celular' => '*********',
    'email' => '<EMAIL>',
    'numero_pos' => 'POS123',
    'tipo_cuenta' => 'Cuenta Vista',
    'numero_cuenta_bancaria' => '********',
    'dias_atencion' => 'Lunes a Viernes',
    'horario_atencion' => '09:00 - 18:00',
    'contrata_boleta' => 'Si',
    'competencia_actual' => 'Transbank'
];

// Simular archivos (esto normalmente vendría de $_FILES)
// Para la prueba, vamos a crear archivos temporales
$test_files = [];

// Crear un archivo de prueba temporal
$temp_content = "Este es un archivo de prueba para InteletGroup\nFecha: " . date('Y-m-d H:i:s');
$temp_file = tempnam(sys_get_temp_dir(), 'inteletgroup_test_');
file_put_contents($temp_file, $temp_content);

// Simular la estructura de $_FILES
$_FILES = [
    'documentos' => [
        'name' => ['test_document.txt', 'test_document2.txt'],
        'type' => ['text/plain', 'text/plain'],
        'size' => [strlen($temp_content), strlen($temp_content)],
        'tmp_name' => [$temp_file, $temp_file],
        'error' => [UPLOAD_ERR_OK, UPLOAD_ERR_OK]
    ]
];

echo "1. Datos del formulario:\n";
print_r($_POST);

echo "\n2. Archivos simulados:\n";
print_r($_FILES);

echo "\n3. Verificando directorio de uploads...\n";
$upload_dir = 'uploads/inteletgroup_prospectos/';
if (!is_dir($upload_dir)) {
    echo "Creando directorio: $upload_dir\n";
    if (mkdir($upload_dir, 0755, true)) {
        echo "✓ Directorio creado exitosamente\n";
    } else {
        echo "✗ Error al crear directorio\n";
        exit(1);
    }
} else {
    echo "✓ Directorio ya existe\n";
}

echo "Permisos del directorio: " . substr(sprintf('%o', fileperms($upload_dir)), -4) . "\n";
echo "Es escribible: " . (is_writable($upload_dir) ? 'Sí' : 'No') . "\n";

// Función para procesar archivos (copiada del archivo principal)
function procesar_archivos_adjuntos_test($db, $prospecto_id, $usuario_id, $rut_cliente) {
    $archivos_procesados = 0;
    
    echo "\n4. Procesando archivos adjuntos...\n";
    
    if (!isset($_FILES['documentos'])) {
        echo "✗ No se encontraron archivos 'documentos' en \$_FILES\n";
        return $archivos_procesados;
    }
    
    if (!is_array($_FILES['documentos']['name'])) {
        echo "✗ La estructura de archivos no es la esperada\n";
        return $archivos_procesados;
    }
    
    if (empty($_FILES['documentos']['name'][0])) {
        echo "✗ No se seleccionaron archivos para subir\n";
        return $archivos_procesados;
    }
    
    $upload_dir = 'uploads/inteletgroup_prospectos/';
    $allowed_types = ['text/plain', 'application/pdf', 'image/jpeg', 'image/png'];
    $max_size = 5 * 1024 * 1024; // 5MB
    
    $files = $_FILES['documentos'];
    $file_count = count($files['name']);
    
    echo "Número de archivos a procesar: $file_count\n";
    
    for ($i = 0; $i < $file_count; $i++) {
        echo "\nProcesando archivo $i:\n";
        
        if ($files['error'][$i] !== UPLOAD_ERR_OK) {
            echo "✗ Error en archivo $i: " . $files['error'][$i] . "\n";
            continue;
        }
        
        $file_name = $files['name'][$i];
        $file_type = $files['type'][$i];
        $file_size = $files['size'][$i];
        $file_tmp = $files['tmp_name'][$i];
        
        echo "  Nombre: $file_name\n";
        echo "  Tipo: $file_type\n";
        echo "  Tamaño: $file_size bytes\n";
        echo "  Temporal: $file_tmp\n";
        
        // Validar tipo de archivo (para la prueba, permitimos text/plain)
        if (!in_array($file_type, $allowed_types)) {
            echo "  ✗ Tipo de archivo no permitido: $file_type\n";
            continue;
        }
        
        if ($file_size > $max_size) {
            echo "  ✗ Archivo demasiado grande: $file_size bytes\n";
            continue;
        }
        
        // Generar nombre único
        $extension = pathinfo($file_name, PATHINFO_EXTENSION);
        if (empty($extension)) $extension = 'txt'; // Para archivos de prueba
        $unique_name = $rut_cliente . '_' . time() . '_' . $i . '.' . $extension;
        $file_path = $upload_dir . $unique_name;
        
        echo "  Nombre único: $unique_name\n";
        echo "  Ruta destino: $file_path\n";
        
        // Simular move_uploaded_file copiando el archivo
        if (copy($file_tmp, $file_path)) {
            echo "  ✓ Archivo copiado exitosamente\n";
            
            // Insertar en base de datos
            $stmt = $db->prepare("
                INSERT INTO tb_inteletgroup_documentos (
                    prospecto_id, usuario_id, rut_cliente, nombre_archivo, 
                    nombre_original, tipo_archivo, tamaño_archivo, ruta_archivo
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            if ($stmt) {
                $stmt->bind_param("iissssis", 
                    $prospecto_id, $usuario_id, $rut_cliente, $unique_name,
                    $file_name, $file_type, $file_size, $file_path
                );
                
                if ($stmt->execute()) {
                    echo "  ✓ Registro insertado en base de datos\n";
                    $archivos_procesados++;
                } else {
                    echo "  ✗ Error al insertar en BD: " . $stmt->error . "\n";
                    unlink($file_path);
                }
                $stmt->close();
            } else {
                echo "  ✗ Error al preparar consulta: " . $db->error . "\n";
                unlink($file_path);
            }
        } else {
            echo "  ✗ Error al copiar archivo\n";
        }
    }
    
    return $archivos_procesados;
}

// Ejecutar prueba
try {
    echo "\n=== INICIANDO PRUEBA ===\n";
    
    // Simular inserción de prospecto (usar ID ficticio)
    $prospecto_id = 999; // ID de prueba
    $usuario_id = $_SESSION['usuario_id'];
    $rut_cliente = $_POST['rut_cliente'];
    
    // Procesar archivos
    $archivos_procesados = procesar_archivos_adjuntos_test($mysqli, $prospecto_id, $usuario_id, $rut_cliente);
    
    echo "\n=== RESULTADO ===\n";
    echo "Archivos procesados exitosamente: $archivos_procesados\n";
    
    if ($archivos_procesados > 0) {
        echo "\n5. Verificando archivos guardados:\n";
        $upload_dir = 'uploads/inteletgroup_prospectos/';
        $files = glob($upload_dir . $rut_cliente . '_*');
        foreach ($files as $file) {
            echo "  - " . basename($file) . " (" . filesize($file) . " bytes)\n";
        }
        
        echo "\n6. Verificando registros en base de datos:\n";
        $stmt = $mysqli->prepare("SELECT * FROM tb_inteletgroup_documentos WHERE prospecto_id = ? ORDER BY id DESC LIMIT 5");
        $stmt->bind_param("i", $prospecto_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            echo "  ID: {$row['id']}, Archivo: {$row['nombre_original']}, Ruta: {$row['ruta_archivo']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "✗ Error durante la prueba: " . $e->getMessage() . "\n";
}

// Limpiar archivo temporal
if (file_exists($temp_file)) {
    unlink($temp_file);
}

echo "\n=== FIN DE LA PRUEBA ===\n";
?>
