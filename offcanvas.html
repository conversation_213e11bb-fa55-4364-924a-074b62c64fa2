<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Affan - PWA Mobile HTML Template">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>Affan - PWA Mobile HTML Template</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/favicon.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card -->
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">
      <!-- Header Content -->
      <div class="header-content position-relative d-flex align-items-center justify-content-between">
        <!-- Back Button -->
        <div class="back-button">
          <a href="elements.html">
            <i class="bi bi-arrow-left-short"></i>
          </a>
        </div>

        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0">Offcanvas</h6>
        </div>

        <!-- Settings -->
        <div class="setting-wrapper">
          <div class="setting-trigger-btn" id="settingTriggerBtn">
            <i class="bi bi-gear"></i>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="page-content-wrapper py-3">
    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading">
        <h6>Left offcanvas</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <!-- Trigger Button -->
          <button class="btn btn-primary" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasleft"
            aria-controls="offcanvasLeft">Toggle left offcanvas</button>

          <!-- Offcanvas Wrapper -->
          <div class="offcanvas offcanvas-start" id="offcanvasleft" data-bs-scroll="true" tabindex="-1"
            aria-labelledby="affanOffcanvsLabel">

            <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
              aria-label="Close"></button>

            <div class="offcanvas-body p-0">
              <div class="sidenav-wrapper">
                <!-- Sidenav Profile -->
                <div class="sidenav-profile bg-gradient">
                  <div class="sidenav-style1"></div>

                  <!-- User Thumbnail -->
                  <div class="user-profile">
                    <img src="img/bg-img/2.jpg" alt="">
                  </div>

                  <!-- User Info -->
                  <div class="user-info">
                    <h6 class="user-name mb-0">Affan Islam</h6>
                    <span>CEO, Designing World</span>
                  </div>
                </div>

                <!-- Sidenav Nav -->
                <ul class="sidenav-nav ps-0">
                  <li>
                    <a href="home.html"><i class="bi bi-house-door"></i> Home</a>
                  </li>
                  <li>
                    <a href="elements.html"><i class="bi bi-folder2-open"></i> Elements
                      <span class="badge bg-danger rounded-pill ms-2">220+</span>
                    </a>
                  </li>
                  <li>
                    <a href="pages.html"><i class="bi bi-collection"></i> Pages
                      <span class="badge bg-success rounded-pill ms-2">100+</span>
                    </a>
                  </li>
                  <li>
                    <a href="#"><i class="bi bi-cart-check"></i> Shop</a>
                    <ul>
                      <li>
                        <a href="shop-grid.html"> Shop Grid</a>
                      </li>
                      <li>
                        <a href="shop-list.html"> Shop List</a>
                      </li>
                      <li>
                        <a href="shop-details.html"> Shop Details</a>
                      </li>
                      <li>
                        <a href="cart.html"> Cart</a>
                      </li>
                      <li>
                        <a href="checkout.html"> Checkout</a>
                      </li>
                    </ul>
                  </li>
                  <li>
                    <a href="settings.html"><i class="bi bi-gear"></i> Settings</a>
                  </li>
                  <li>
                    <div class="night-mode-nav">
                      <i class="bi bi-moon"></i> Night Mode
                      <div class="form-check form-switch">
                        <input class="form-check-input form-check-success" id="darkSwitch2" type="checkbox">
                      </div>
                    </div>
                  </li>
                  <li>
                    <a href="login.html"><i class="bi bi-box-arrow-right"></i> Logout</a>
                  </li>
                </ul>

                <!-- Social Info -->
                <div class="social-info-wrap">
                  <a href="#">
                    <i class="bi bi-facebook"></i>
                  </a>
                  <a href="#">
                    <i class="bi bi-twitter"></i>
                  </a>
                  <a href="#">
                    <i class="bi bi-linkedin"></i>
                  </a>
                </div>

                <!-- Copyright Info -->
                <div class="copyright-info">
                  <p>
                    <span id="copyrightYear"></span>
                    &copy; Made by <a href="#">Designing World</a>
                  </p>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Top offcanvas</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <!-- Trigger Button -->
          <button class="btn btn-success" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasTop"
            aria-controls="offcanvasTop">Toggle top offcanvas</button>

          <!-- Offcanvas Wrapper -->
          <div class="offcanvas offcanvas-top" id="offcanvasTop" tabindex="-1" aria-labelledby="offcanvasTopLabel">
            <!-- Close Button -->
            <button class="btn-close text-reset" type="button" data-bs-dismiss="offcanvas" aria-label="Close"></button>

            <!-- Offcanvas Body -->
            <div class="offcanvas-body p-4">
              <h5>Placement</h5>
              <p>There’s no default placement for offcanvas components, so you must add one of the modifier classes
                below;</p>
              <ul class="ps-0 mb-4">
                <li class="fz-14 pb-2">
                  <code class="bg-dark text-white rounded p-1 me-1">.offcanvas-start</code>places
                  offcanvas on the left of the viewport.
                </li>
                <li class="fz-14 pb-2">
                  <code class="bg-dark text-white rounded p-1 me-1">.offcanvas-end</code>places
                  offcanvas on the right of the viewport.
                </li>
                <li class="fz-14 pb-2">
                  <code class="bg-dark text-white rounded p-1 me-1">.offcanvas-top</code>places
                  offcanvas on the top of the viewport.
                </li>
                <li class="fz-14">
                  <code class="bg-dark text-white rounded p-1 me-1">.offcanvas-bottom</code>places
                  offcanvas on the bottom of the viewport.
                </li>
              </ul>
              <h5>Hide &amp; Show</h5>
              <p>Use the buttons below to show and hide an offcanvas element via JavaScript that toggles the <code
                  class="bg-dark text-white rounded p-1 me-1">.show </code>class on an element with the <code
                  class="bg-dark text-white rounded p-1 me-1">.offcanvas </code>class.
              </p>
              <ul class="ps-0">
                <li class="fz-14 pb-2">
                  <code class="bg-dark text-white rounded p-1 me-1">.offcanvas</code>hides content
                  (by default).
                </li>
                <li class="fz-14 pb-2">
                  <code class="bg-dark text-white rounded p-1 me-1">.offcanvas.show</code>shows
                  content.
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Right offcanvas</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <!-- Trigger Button -->
          <button class="btn btn-warning" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasRight"
            aria-controls="offcanvasRight">Toggle right offcanvas</button>

          <!-- Offcanvas Wrapper -->
          <div class="offcanvas offcanvas-end" id="offcanvasRight" tabindex="-1" aria-labelledby="offcanvasRightLabel">
            <!-- Close Button -->
            <button class="btn-close text-reset" type="button" data-bs-dismiss="offcanvas" aria-label="Close"></button>

            <!-- Offcanvas Body -->
            <div class="offcanvas-body p-4 text-end">
              <h5>Placement</h5>
              <p>There’s no default placement for offcanvas components, so you must add one of the modifier classes
                below;</p>
              <ul class="ps-0 mb-4">
                <li class="fz-14 pb-2">
                  <code class="bg-dark text-white rounded p-1 me-1">.offcanvas-start</code>places
                  offcanvas on the left of the viewport.
                </li>
                <li class="fz-14 pb-2">
                  <code class="bg-dark text-white rounded p-1 me-1">.offcanvas-end</code>places
                  offcanvas on the right of the viewport.
                </li>
                <li class="fz-14 pb-2">
                  <code class="bg-dark text-white rounded p-1 me-1">.offcanvas-top</code>places
                  offcanvas on the top of the viewport.
                </li>
                <li class="fz-14">
                  <code class="bg-dark text-white rounded p-1 me-1">.offcanvas-bottom</code>places
                  offcanvas on the bottom of the viewport.
                </li>
              </ul>
              <h5>Hide &amp; Show</h5>
              <p>Use the buttons below to show and hide an offcanvas element via JavaScript that toggles the <code
                  class="bg-dark text-white rounded p-1 me-1">.show </code>class on an element with the <code
                  class="bg-dark text-white rounded p-1 me-1">.offcanvas </code>class.
              </p>
              <ul class="ps-0">
                <li class="fz-14 pb-2">
                  <code class="bg-dark text-white rounded p-1 me-1">.offcanvas</code>hides content
                  (by default).
                </li>
                <li class="fz-14 pb-2">
                  <code class="bg-dark text-white rounded p-1 me-1">.offcanvas.show</code>shows
                  content.
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Bottom Offcanvas</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <!-- Trigger Button -->
          <button class="btn btn-info" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasBottom"
            aria-controls="offcanvasBottom">Toggle bottom offcanvas</button>

          <!-- Offcanvas Wrapper -->
          <div class="offcanvas offcanvas-bottom" id="offcanvasBottom" tabindex="-1"
            aria-labelledby="offcanvasBottomLabel">
            <!-- Close Button -->
            <button class="btn-close text-reset" type="button" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            <!-- Offcanvas Body -->
            <div class="offcanvas-body p-4">
              <h5>Placement</h5>
              <p>There’s no default placement for offcanvas components, so you must add one of the modifier classes
                below;</p>
              <ul class="ps-0 mb-4">
                <li class="fz-14 pb-2"><code class="bg-dark text-white rounded p-1 me-1">.offcanvas-start</code>places
                  offcanvas on the left of the viewport.</li>
                <li class="fz-14 pb-2"><code class="bg-dark text-white rounded p-1 me-1">.offcanvas-end</code>places
                  offcanvas on the right of the viewport.</li>
                <li class="fz-14 pb-2"><code class="bg-dark text-white rounded p-1 me-1">.offcanvas-top</code>places
                  offcanvas on the top of the viewport.</li>
                <li class="fz-14"><code class="bg-dark text-white rounded p-1 me-1">.offcanvas-bottom</code>places
                  offcanvas on the bottom of the viewport.</li>
              </ul>
              <h5>Hide &amp; Show</h5>
              <p>Use the buttons below to show and hide an offcanvas element via JavaScript that toggles the <code
                  class="bg-dark text-white rounded p-1 me-1">.show </code>class on an element with the <code
                  class="bg-dark text-white rounded p-1 me-1">.offcanvas </code>class.</p>
              <ul class="ps-0">
                <li class="fz-14 pb-2"><code class="bg-dark text-white rounded p-1 me-1">.offcanvas</code>hides content
                  (by default).</li>
                <li class="fz-14 pb-2"><code class="bg-dark text-white rounded p-1 me-1">.offcanvas.show</code>shows
                  content.</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          <li class="active">
            <a href="home.html">
              <i class="bi bi-house"></i>
              <span>Home</span>
            </a>
          </li>

          <li>
            <a href="pages.html">
              <i class="bi bi-collection"></i>
              <span>Pages</span>
            </a>
          </li>

          <li>
            <a href="elements.html">
              <i class="bi bi-folder2-open"></i>
              <span>Elements</span>
            </a>
          </li>

          <li>
            <a href="chat-users.html">
              <i class="bi bi-chat-dots"></i>
              <span>Chat</span>
            </a>
          </li>

          <li>
            <a href="settings.html">
              <i class="bi bi-gear"></i>
              <span>Settings</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
</body>

</html>