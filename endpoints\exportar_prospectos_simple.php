<?php
// Endpoint para exportar prospectos a XLSX usando PhpSpreadsheet

session_start();

require_once __DIR__ . '/../con_db.php';

// Cargar autoload de Composer (asegúrate de instalar phpoffice/phpspreadsheet)
require_once __DIR__ . '/../../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

// Conexión a base de datos ya disponible en $mysqli

// Crear spreadsheet
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Encabezados de columna
$headers = ['ID', 'Razón Social', 'RUT', 'Email', 'Teléfono Celular'];
foreach ($headers as $col => $text) {
    $sheet->setCellValueByColumnAndRow($col + 1, 1, $text);
}

// Obtener datos de prospectos
$query = "SELECT id, razon_social, rut_cliente, email, telefono_celular FROM tb_inteletgroup_prospectos";
$stmt = $mysqli->prepare($query);
if (!$stmt) {
    die('Error preparando consulta: ' . $mysqli->error);
}
$stmt->execute();
$stmt->bind_result(
    $id, $razon, $rut, $email, $telefono_celular
);

$rowIndex = 2;
while ($stmt->fetch()) {
    $sheet->setCellValueByColumnAndRow(1, $rowIndex, $id);
    $sheet->setCellValueByColumnAndRow(2, $rowIndex, $razon);
    $sheet->setCellValueByColumnAndRow(3, $rowIndex, $rut);
    $sheet->setCellValueByColumnAndRow(4, $rowIndex, $email);
    $sheet->setCellValueByColumnAndRow(5, $rowIndex, $telefono_celular);
    $rowIndex++;
}
$stmt->close();
$mysqli->close();

// Enviar XLSX al navegador
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment; filename="prospectos.xlsx"');

$writer = new Xlsx($spreadsheet);
$writer->save('php://output');
exit;
