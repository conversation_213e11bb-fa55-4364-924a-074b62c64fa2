<?php


header('Content-Type: text/html; charset=UTF-8');
//Iniciar una nueva sesión o reanudar la existente.
session_start();

$inc = include("con_db.php");




?>



<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Affan - PWA Mobile HTML Template">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>GESTAR APP</title>

  <!-- Favicon -->
  <link rel="icon" href="img/icons/logoGestar.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">

  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

  <style>
    body {
      background-color: #a0111105;
    }
  </style>

  <title>Abrir resumen de competencia</title>
  <script>
    function abrirVentanaEmergente() {
      // Abrir ventana emergente y cargar el archivo PDF
      window.open('PDF_resumenCompetencia.php', 'ventanaEmergente', 'width=600,height=400');
    }
  </script>


</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card -->
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">
      <!-- Header Content -->
      <div class="header-content header-style-five position-relative d-flex align-items-center justify-content-between">
        <!-- Logo Wrapper -->
        <div class="logo-wrapper">
          <a href="home.html">
            <img src="img/core-img/logo.png" alt="">
          </a>
        </div>

        <h6 class="mb-0">CALCULADOR DE COMISIÓN DINAMICA</h6>
        <!-- Navbar Toggler -->
        <div class="navbar--toggler" id="affanNavbarToggler" data-bs-toggle="offcanvas" data-bs-target="#affanOffcanvas"
          aria-controls="affanOffcanvas">
          <span class="d-block"></span>
          <span class="d-block"></span>
          <span class="d-block"></span>
        </div>
      </div>
    </div>
    <!-- Page Title -->
    <div class="page-heading">

    </div>


  </div>

  <!-- Offcanvas Start -->
  <div class="offcanvas offcanvas-start" id="affanOffcanvas" data-bs-scroll="true" tabindex="-1"
    aria-labelledby="affanOffcanvsLabel">

    <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
      aria-label="Close"></button>

    <div class="offcanvas-body p-0">
      <div class="sidenav-wrapper">
        <!-- Sidenav Profile -->
        <div class="sidenav-profile bg-gradient" style="background-color: #dc3545;">

          <div class="sidenav-style1"></div>

          <!-- User Thumbnail -->
          <div class="user-profile">
            <img src="img/bg-img/2.jpg" alt="">
          </div>

          <!-- User Info -->
          <div class="user-info">
            <h6 class="user-name mb-0">Bienvenido COLABORADOR </h6>
            <span>ROL : BETA</span>
          </div>
        </div>

        <!-- Sidenav Nav -->
        <ul class="sidenav-nav ps-0">
          <li>
            <a href="#"><i class="bi bi-house-door"></i>HOME</a>
          </li>

          <li>
            <a href="#"><i class="bi bi-globe"></i>Formularios Web</a>
            <ul>
              
            <li>
                <a href="https://forms.gle/nF9h2eeZJrRYy3VL8" target="_blank">Formulario Sucursal</a>
            </li>

              <li>
                <a href="https://forms.gle/FCqd5aXBozkfaVVq7" target="_blank">Formulario Delivery</a>
              </li>
              
              <li>
                <a href="https://forms.gle/SUScwqhHStz7JZ9X9" target="_blank">Formulario Contratación GETNET</a>
              </li>
              <li>
                <a href="https://forms.gle/aA6qhiu9WKnsyPrb9" target="_blank">Formulario Sodexo</a>
              </li>
            </ul>
          </li>
          <!-- <li>
            <a href="settings.html"><i class="bi bi-gear"></i> Settings</a>
          </li> -->
          <!-- <li>
            <div class="night-mode-nav">
              <i class="bi bi-moon"></i> Night Mode
              <div class="form-check form-switch">
                <input class="form-check-input form-check-success" id="darkSwitch" type="checkbox">
              </div>
            </div>
          </li> -->
          <li>
            <a href="login.html"><i class="bi bi-box-arrow-right"></i>Cerrar sesión</a>
          </li>
        </ul>

        <!-- Social Info -->
        <div class="social-info-wrap">
          <a href="#">
            <i class="bi bi-facebook"></i>
          </a>
          <a href="#">
            <i class="bi bi-twitter"></i>
          </a>
          <a href="#">
            <i class="bi bi-linkedin"></i>
          </a>
        </div>

        <!-- Copyright Info -->
        <div class="copyright-info">
          <p style="color: red;">
            <span id="copyrightYear"></span>
            &copy; Realizado por <a href="#" style="color: red;">Tecnología Kayze</a>
          </p>
        </div>

      </div>
    </div>
  </div>

  <div class="page-content-wrapper py-3" id="elementsSearchList">
    <div class="container">
      <div class="card-body">
        <div class="form-group">
          <button class="btn btn-danger w-100 d-flex align-items-center justify-content-center" type="button"
            onclick="abrirVentanaEmergente()">Tabla resumen de competencia</button>
        </div>
      </div>
    </div>
    <div class="container">
      <div class="card-body">

        <form method="post" action="<?php echo $_SERVER['PHP_SELF']; ?>">

          <div class="form-group">
            <label class="form-label" for="exampleInputText">Ingrese el monto promedio de venta</label>
            <div class="input-group">
              <span class="input-group-text">$</span>
              <input class="form-control" id="dias" name="dias" type="number" placeholder="Ejemplo 8000">
            </div>
          </div>

          <!-- Element Heading -->
          <div class="container">
            <div class="element-heading mt-3">
              <h6>OPCIONES AVANZADAS</h6>
            </div>
          </div>

          <div class="container">
            <div class="card">
              <div class="accordion accordion-flush accordion-style-one" id="accordionStyle1">
                <!-- Single Accordion -->
                <div class="accordion-item">
                  <div class="accordion-header" id="accordionOne">
                    <h6 data-bs-toggle="collapse" data-bs-target="#accordionStyleOne" aria-expanded="true"
                      aria-controls="accordionStyleOne"><i class="bi bi-chevron-down"></i></h6>
                  </div>
                  <div class="accordion-collapse collapse show" id="accordionStyleOne" aria-labelledby="accordionOne"
                    data-bs-parent="#accordionStyle1">
                    <div class="accordion-body">
                      <div class="form-group">
                        <label class="form-label" for="exampleInputText">Ingrese los días</label>
                        <input class="form-control" id="exampleInputText" type="number" placeholder="Ejemplo 10">
                      </div>

                      <div class="form-group">
                        <label class="form-label" for="exampleInputText">Ingrese el número de ventas diarias</label>
                        <input class="form-control" id="Ventas" type="number" placeholder="Ejemplo 10">
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>




          <input class="btn btn-danger w-100 d-flex align-items-center justify-content-center" type="submit"
            value="Calcular">
          <!-- 
          <button class="btn btn-danger w-100 d-flex align-items-center justify-content-center" type="button">
            Calcular
            <i class="bi bi-arrow-right fz-16 ms-1"></i>
          </button> -->
        </form>


        <?php
        // Verificar si se ha enviado el formulario
        if ($_SERVER["REQUEST_METHOD"] == "POST") {
          // Obtener el valor ingresado
          $valor = $_POST["dias"];

          // Realizar la operación matemática según el valor ingresado
          $resultado = $valor * 2; // Ejemplo: multiplicar el valor por 2
          ?>
          <br>
          <h2>Tabla de comisiones por empresa </h2>
          <div class="container">
            <div class="card">
              <div class="card-body">
                <table class="table mb-0">
                  <thead>
                    <tr>
                      <th>EMPRESA</th>
                      <th>DEBITO</th>
                      <th>CREDITO</th>

                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>GETNET</td>
                      <td>$
                        <?php echo number_format($valor * 0.0063 + 0.002 * 36082, 2, ",", "."); ?>

                      </td>
                      <td>$
                        <?php echo number_format($valor * 0.0161 + 0.0023 * 36082, 2, ",", "."); ?>

                      </td>

                    </tr>
                    <tr>
                      <td>TRANSBANK</td>
                      <td>$
                        <?php echo number_format($valor * 0.0082 + 0.0016 * 36082, 2, ",", "."); ?>

                      </td>
                      <td>$
                        <?php echo number_format($valor * 0.0176 + 0.0018 * 36082, 2, ",", "."); ?>

                      </td>

                    </tr>
                    <tr>
                      <td>REDELCOM</td>
                      <td>$
                        <?php echo $valor * 0.0295; ?> + IVA
                      </td>
                      <td>$
                        <?php echo $valor * 0.0295; ?> + IVA
                      </td>

                    </tr>
                    <tr>
                      <td>SUMUP</td>
                      <td>$
                        <?php echo $valor * 0.0295; ?> + IVA
                      </td>
                      <td>$
                        <?php echo $valor * 0.0295; ?> + IVA
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <?php
        }
        ?>

      </div>
    </div>
  </div>


  <br>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          <li class="active">
            <a href="tabla_resumen.php">
              <i class="bi bi-calculator" style="color: red;"></i>
              <span style="color: red;">Calculadora</span>
            </a>
          </li>

        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
</body>

</html>