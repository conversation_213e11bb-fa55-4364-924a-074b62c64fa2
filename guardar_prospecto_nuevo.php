<?php
/**
 * Nuevo script para guardar prospectos con formulario extendido
 * Incluye validación completa, manejo de archivos y notificaciones por email
 */

// Configuración de headers y seguridad
header('Content-Type: application/json');
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");

// Iniciar sesión para verificar autenticación
session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['auth_token'])) {
    echo json_encode(['success' => false, 'message' => 'Usuario no autenticado']);
    exit();
}

// Incluir conexión a la base de datos
require_once("con_db.php");

// Función para sanitizar datos
function sanitize($conn, $data) {
    return mysqli_real_escape_string($conn, trim($data));
}

// Función para validar RUT chileno (sin puntos ni guiones)
function validarRUT($rut) {
    // Remover puntos y guiones si los hay
    $rut = preg_replace('/[^0-9kK]/', '', $rut);
    
    if (strlen($rut) < 8 || strlen($rut) > 9) {
        return false;
    }
    
    $dv = strtoupper(substr($rut, -1));
    $numero = substr($rut, 0, -1);
    
    $suma = 0;
    $multiplicador = 2;
    
    for ($i = strlen($numero) - 1; $i >= 0; $i--) {
        $suma += $numero[$i] * $multiplicador;
        $multiplicador = $multiplicador == 7 ? 2 : $multiplicador + 1;
    }
    
    $resto = $suma % 11;
    $dv_calculado = 11 - $resto;
    
    if ($dv_calculado == 11) $dv_calculado = '0';
    if ($dv_calculado == 10) $dv_calculado = 'K';
    
    return $dv == $dv_calculado;
}

// Función para validar email
function validarEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Función para validar teléfono (mínimo 9 dígitos)
function validarTelefono($telefono) {
    $telefono_limpio = preg_replace('/[^0-9]/', '', $telefono);
    return strlen($telefono_limpio) >= 9;
}

// Función para validar razón social (mayúsculas, sin acentos especiales)
function validarRazonSocial($razon_social) {
    // Permitir letras, números, espacios y algunos caracteres especiales básicos
    return preg_match('/^[A-Z0-9\s\.\-&]+$/', $razon_social);
}

// Función para manejar archivo adjunto (mejorada)
function manejarArchivoAdjunto($archivo, $rut_ejecutivo, $prospecto_id) {
    require_once("upload_manager.php");

    $result = uploadProspectDocument($archivo, $rut_ejecutivo, $prospecto_id);

    if ($result['success']) {
        return $result['filepath'];
    } else {
        error_log("Error subiendo archivo: " . $result['message']);
        return false;
    }
}

// Función para enviar notificación por email (mejorada)
function enviarNotificacionEmail($datos_prospecto) {
    require_once("email_config.php");

    // Validar configuración antes de enviar
    $errores_config = validarConfiguracionEmail();
    if (!empty($errores_config)) {
        error_log("Configuración de email incompleta: " . implode(', ', $errores_config));
        return false;
    }

    return enviarNotificacionProspecto($datos_prospecto);
}

try {
    // Verificar que sea una solicitud POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método no permitido');
    }

    // Obtener datos del formulario y sanitizar
    $nombre_ejecutivo = isset($_POST['nombre_ejecutivo']) ? sanitize($conn, $_POST['nombre_ejecutivo']) : '';
    $rut_ejecutivo = isset($_POST['rut_ejecutivo']) ? sanitize($conn, $_POST['rut_ejecutivo']) : '';
    $razon_social = isset($_POST['razon_social']) ? sanitize($conn, $_POST['razon_social']) : '';
    $rubro = isset($_POST['rubro']) ? sanitize($conn, $_POST['rubro']) : '';
    $direccion_comercial = isset($_POST['direccion_comercial']) ? sanitize($conn, $_POST['direccion_comercial']) : '';
    $telefono = isset($_POST['telefono']) ? sanitize($conn, $_POST['telefono']) : '';
    $email = isset($_POST['email']) ? sanitize($conn, $_POST['email']) : '';
    $num_pos = isset($_POST['num_pos']) && !empty($_POST['num_pos']) ? intval($_POST['num_pos']) : null;
    $tipo_cuenta = isset($_POST['tipo_cuenta']) ? sanitize($conn, $_POST['tipo_cuenta']) : '';
    $num_cuenta_bancaria = isset($_POST['num_cuenta_bancaria']) ? sanitize($conn, $_POST['num_cuenta_bancaria']) : '';
    $dias_atencion = isset($_POST['dias_atencion']) ? sanitize($conn, $_POST['dias_atencion']) : '';
    $horario_atencion = isset($_POST['horario_atencion']) ? sanitize($conn, $_POST['horario_atencion']) : '';
    $contrata_boleta = isset($_POST['contrata_boleta']) ? sanitize($conn, $_POST['contrata_boleta']) : '';
    $competencia_actual = isset($_POST['competencia_actual']) ? sanitize($conn, $_POST['competencia_actual']) : '';
    $observaciones = isset($_POST['observaciones']) ? sanitize($conn, $_POST['observaciones']) : '';
    
    $usuario_id = $_SESSION['usuario_id'];

    // Array para almacenar errores de validación
    $errores = [];

    // Validaciones de campos requeridos
    if (empty($nombre_ejecutivo)) {
        $errores[] = 'El nombre del ejecutivo es requerido';
    }

    if (empty($rut_ejecutivo)) {
        $errores[] = 'El RUT del cliente es requerido';
    } elseif (!validarRUT($rut_ejecutivo)) {
        $errores[] = 'El RUT del cliente no es válido';
    }

    if (empty($razon_social)) {
        $errores[] = 'La razón social es requerida';
    } elseif (!validarRazonSocial($razon_social)) {
        $errores[] = 'La razón social debe estar en mayúsculas y sin acentos o caracteres especiales';
    }

    if (empty($telefono)) {
        $errores[] = 'El teléfono celular es requerido';
    } elseif (!validarTelefono($telefono)) {
        $errores[] = 'El teléfono debe tener al menos 9 dígitos';
    }

    // Validaciones opcionales pero con formato
    if (!empty($email) && !validarEmail($email)) {
        $errores[] = 'El formato del email no es válido';
    }

    if (!empty($tipo_cuenta) && !in_array($tipo_cuenta, ['Cuenta Vista', 'Cuenta Corriente'])) {
        $errores[] = 'Tipo de cuenta no válido';
    }

    if (!empty($contrata_boleta) && !in_array($contrata_boleta, ['Sí', 'No'])) {
        $errores[] = 'Valor de "Contrata Boleta" no válido';
    }

    $competencias_validas = ['Transbank', 'Getnet', 'Compra Aquí (Bco Estado)', 'Klap', 'SumUp', 'Tuu', 'Ya Ganaste', 'Mercado Pago'];
    if (!empty($competencia_actual) && !in_array($competencia_actual, $competencias_validas)) {
        $errores[] = 'Competencia actual no válida';
    }

    // Si hay errores, devolverlos
    if (!empty($errores)) {
        echo json_encode([
            'success' => false,
            'message' => 'Errores de validación encontrados',
            'errors' => $errores
        ]);
        exit();
    }

    // Verificar si el RUT ya existe
    $check_rut = $mysqli->prepare("SELECT id FROM tb_experian_prospecto WHERE rut_ejecutivo = ?");
    $check_rut->bind_param("s", $rut_ejecutivo);
    $check_rut->execute();
    $result = $check_rut->get_result();
    
    if ($result->num_rows > 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Ya existe un prospecto con este RUT',
            'duplicate' => true
        ]);
        exit();
    }
    $check_rut->close();

    // Limpiar RUT para almacenamiento (sin puntos ni guiones)
    $rut_ejecutivo = preg_replace('/[^0-9kK]/', '', $rut_ejecutivo);

    // Preparar la consulta SQL de inserción
    $sql = "INSERT INTO tb_experian_prospecto (
                nombre_ejecutivo, rut_ejecutivo, razon_social, rubro, direccion_comercial,
                contacto, telefono, email, num_pos, tipo_cuenta, num_cuenta_bancaria,
                dias_atencion, horario_atencion, contrata_boleta, competencia_actual,
                estado, observaciones, fecha_registro, usuario_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'Envio información', ?, NOW(), ?)";

    $stmt = $mysqli->prepare($sql);
    
    if (!$stmt) {
        throw new Exception("Error preparando la consulta: " . $mysqli->error);
    }

    // Bind parameters - contacto se llena con razon_social por compatibilidad
    $contacto = $razon_social; // Campo legacy
    
    $stmt->bind_param("ssssssssissssssi", 
        $nombre_ejecutivo, $rut_ejecutivo, $razon_social, $rubro, $direccion_comercial,
        $contacto, $telefono, $email, $num_pos, $tipo_cuenta, $num_cuenta_bancaria,
        $dias_atencion, $horario_atencion, $contrata_boleta, $competencia_actual,
        $observaciones, $usuario_id
    );

    // Ejecutar la inserción
    if (!$stmt->execute()) {
        throw new Exception("Error al insertar prospecto: " . $stmt->error);
    }

    $prospecto_id = $mysqli->insert_id;
    $stmt->close();

    // Insertar en la bitácora
    $observacion_bitacora = "Prospecto creado: $razon_social";
    if (!empty($observaciones)) {
        $observacion_bitacora .= " - " . $observaciones;
    }

    $bitacora_sql = "INSERT INTO tb_experian_prospecto_bitacora (
                        rut_ejecutivo, estado, observaciones, usuario_id
                    ) VALUES (?, 'Envio información', ?, ?)";

    $bitacora_stmt = $mysqli->prepare($bitacora_sql);
    $bitacora_stmt->bind_param("ssi", $rut_ejecutivo, $observacion_bitacora, $usuario_id);
    
    if (!$bitacora_stmt->execute()) {
        // Log error but don't fail the main operation
        error_log("Error insertando en bitácora: " . $bitacora_stmt->error);
    }
    $bitacora_stmt->close();

    // Manejar archivo adjunto si existe
    $archivo_ruta = null;
    if (isset($_FILES['archivo_documentacion']) && $_FILES['archivo_documentacion']['error'] === UPLOAD_ERR_OK) {
        $archivo_ruta = manejarArchivoAdjunto($_FILES['archivo_documentacion'], $rut_ejecutivo, $prospecto_id);

        if ($archivo_ruta) {
            // Actualizar el registro con la ruta del archivo
            $update_sql = "UPDATE tb_experian_prospecto SET archivo_documentacion = ? WHERE id = ?";
            $update_stmt = $mysqli->prepare($update_sql);
            $update_stmt->bind_param("si", $archivo_ruta, $prospecto_id);
            $update_stmt->execute();
            $update_stmt->close();
        }
    }

    // Enviar notificación por email
    $datos_email = [
        'rut_ejecutivo' => $rut_ejecutivo,
        'razon_social' => $razon_social,
        'nombre_ejecutivo' => $nombre_ejecutivo,
        'rubro' => $rubro,
        'direccion_comercial' => $direccion_comercial,
        'telefono' => $telefono,
        'email' => $email,
        'num_pos' => $num_pos,
        'tipo_cuenta' => $tipo_cuenta,
        'num_cuenta_bancaria' => $num_cuenta_bancaria,
        'dias_atencion' => $dias_atencion,
        'horario_atencion' => $horario_atencion,
        'contrata_boleta' => $contrata_boleta,
        'competencia_actual' => $competencia_actual,
        'observaciones' => $observaciones
    ];
    enviarNotificacionEmail($datos_email);

    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'message' => 'Prospecto registrado exitosamente',
        'prospecto_id' => $prospecto_id,
        'rut_ejecutivo' => $rut_ejecutivo,
        'razon_social' => $razon_social,
        'archivo_subido' => $archivo_ruta ? true : false,
        'timestamp' => date('Y-m-d H:i:s')
    ]);

} catch (Exception $e) {
    error_log("Error en guardar_prospecto_nuevo.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error interno del servidor: ' . $e->getMessage()
    ]);
} finally {
    if (isset($mysqli)) {
        $mysqli->close();
    }
}
?>
