<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Affan - PWA Mobile HTML Template">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>Affan - PWA Mobile HTML Template</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/favicon.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card -->
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">
      <!-- Paste your Header Content from here -->
      <!-- Header Content -->
      <div class="header-content position-relative d-flex align-items-center justify-content-between">
        <!-- Back Button -->
        <div class="back-button">
          <a href="pages.html">
            <i class="bi bi-arrow-left-short"></i>
          </a>
        </div>

        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0">Blog Details</h6>
        </div>

        <!-- Settings -->
        <div class="setting-wrapper">
          <div class="setting-trigger-btn" id="settingTriggerBtn">
            <i class="bi bi-gear"></i>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="page-content-wrapper">
    <div class="container">
      <div class="pt-3 d-block"></div>

      <div class="blog-details-post-thumbnail position-relative">
        <!-- Post Image -->
        <img class="w-100 rounded-lg" src="img/bg-img/24.jpg" alt="">
        <!-- Post Bookmark -->
        <a class="post-bookmark position-absolute card-badge" href="#">
          <i class="bi bi-bookmark"></i>
        </a>
      </div>
    </div>

    <div class="blog-description py-3">
      <div class="container">
        <a class="badge bg-primary mb-2 d-inline-block" href="#">News</a>
        <h3 class="mb-3">A collection of textile samples lay spread out on the table</h3>

        <div class="d-flex align-items-center mb-4">
          <a class="badge-avater" href="#">
            <img class="img-circle" src="img/bg-img/user1.png" alt="">
          </a>
          <span class="ms-2">Jarah Clark</span>
        </div>

        <p class="fz-14">One morning, when Gregor Samsa woke from troubled dreams, he found himself transformed in his
          bed into a horrible vermin.</p>
        <p class="fz-14">He lay on his armour-like back, and if he lifted his head a little he could see his brown
          belly, slightly domed and divided by arches into stiff sections.</p>
        <p class="fz-14">The bedding was hardly able to cover it and seemed ready to slide off any moment.</p>
        <p class="fz-14">His many legs, pitifully thin compared with the size of the rest of him, waved about helplessly
          as he looked. "What's happened to me? " he thought.</p>
        <p class="fz-14">It wasn't a dream. His room, a proper human room although a little too small, lay peacefully
          between its four familiar walls.</p>
        <p class="fz-14">A collection of textile samples lay spread out on the table - Samsa was a travelling salesman -
          and above it there hung a picture that he had recently cut out of an illustrated magazine and housed in a
          nice, gilded frame.</p>
        <p class="fz-14">It showed a lady fitted out with a fur hat and fur boa who sat upright, raising a heavy fur
          muff that covered the whole of her lower arm towards the viewer. Gregor then turned to look out the window at
          the dull weather.</p>
      </div>
    </div>

    <!-- All Comments -->
    <div class="rating-and-review-wrapper pb-3 mt-3">
      <div class="container">
        <h6 class="mb-3">All comments</h6>
        <!-- Rating Review -->
        <div class="rating-review-content">
          <ul class="ps-2">
            <li class="single-user-review d-flex">
              <div class="user-thumbnail mt-0">
                <img src="img/bg-img/2.jpg" alt="">
              </div>
              <div class="rating-comment">
                <p class="comment mb-1">I strongly recommend this agency to everyone interested in running a business.
                </p>
                <span class="name-date">12 Dec 2022</span>
              </div>
            </li>
            <li class="single-user-review d-flex">
              <div class="user-thumbnail mt-0">
                <img src="img/bg-img/20.jpg" alt="">
              </div>
              <div class="rating-comment">
                <p class="comment mb-1">You've saved our business! Thanks guys, keep up the good work! The best on the
                  net!</p>
                <span class="name-date">8 Dec 2022</span>
              </div>
            </li>
            <li class="single-user-review d-flex">
              <div class="user-thumbnail mt-0">
                <img src="img/bg-img/21.jpg" alt="">
              </div>
              <div class="rating-comment">
                <p class="comment mb-1">Absolutely wonderful! I wish I would have thought of it first. I would be lost
                  without agency.</p>
                <span class="name-date">28 Nov 2022</span>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Comment Form -->
    <div class="ratings-submit-form pb-3">
      <div class="container">
        <h6 class="mb-3">Submit a comment</h6>
        <form action="#">
          <div class="form-group">
            <textarea class="form-control mb-3 border-0" name="comment" cols="30" rows="10"
              placeholder="Write a comment"></textarea>
          </div>
          <button class="btn w-100 btn-primary" type="submit">Post Comment</button>
        </form>
      </div>
    </div>
  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          <li class="active">
            <a href="home.html">
              <i class="bi bi-house"></i>
              <span>Home</span>
            </a>
          </li>

          <li>
            <a href="pages.html">
              <i class="bi bi-collection"></i>
              <span>Pages</span>
            </a>
          </li>

          <li>
            <a href="elements.html">
              <i class="bi bi-folder2-open"></i>
              <span>Elements</span>
            </a>
          </li>

          <li>
            <a href="chat-users.html">
              <i class="bi bi-chat-dots"></i>
              <span>Chat</span>
            </a>
          </li>

          <li>
            <a href="settings.html">
              <i class="bi bi-gear"></i>
              <span>Settings</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
</body>

</html>