<?php
/**
 * Endpoint para obtener información del usuario logueado
 * Devuelve nombre_usuario y otros datos relevantes
 */

header('Content-Type: application/json');
session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['auth_token'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Usuario no autenticado'
    ]);
    exit();
}

try {
    require_once("../con_db.php");
    
    $usuario_id = $_SESSION['usuario_id'];
    
    // Consultar información del usuario
    $sql = "SELECT id, correo, nombre_usuario, rol, proyecto, rut_ejecutivo 
            FROM tb_experian_usuarios 
            WHERE id = ?";
    
    $stmt = $mysqli->prepare($sql);
    
    if (!$stmt) {
        throw new Exception("Error preparando consulta: " . $mysqli->error);
    }
    
    $stmt->bind_param("i", $usuario_id);
    
    if (!$stmt->execute()) {
        throw new Exception("Error ejecutando consulta: " . $stmt->error);
    }
    
    // Usar bind_result() en lugar de get_result() para compatibilidad con PHP 7.3
    $id = $correo = $nombre_usuario = $rol = $proyecto = $rut_ejecutivo = null;
    $stmt->bind_result($id, $correo, $nombre_usuario, $rol, $proyecto, $rut_ejecutivo);

    if (!$stmt->fetch()) {
        throw new Exception("Usuario no encontrado");
    }

    $usuario = [
        'id' => $id,
        'correo' => $correo,
        'nombre_usuario' => $nombre_usuario,
        'rol' => $rol,
        'proyecto' => $proyecto,
        'rut_ejecutivo' => $rut_ejecutivo
    ];
    $stmt->close();
    
    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'usuario_id' => $usuario['id'],
        'correo' => $usuario['correo'],
        'nombre_usuario' => $usuario['nombre_usuario'] ?: 'Usuario',
        'rol' => $usuario['rol'],
        'proyecto' => $usuario['proyecto'],
        'rut_ejecutivo' => $usuario['rut_ejecutivo'],
        'es_admin' => $usuario['rol'] === 'admin'
    ]);
    
} catch (Exception $e) {
    error_log("Error en get_user_info.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error obteniendo información del usuario'
    ]);
} finally {
    if (isset($mysqli)) {
        $mysqli->close();
    }
}
?>
