<?php
// Test users loading for password creation
header('Content-Type: application/json');

try {
    // Database configuration
    $host = '*************';
    $port = 3306;
    $dbname = 'gestarse_experian';
    $username = 'gestarse_ncornejo7_experian';
    $password = 'N1c0l7as17';
    
    // Create connection
    $mysqli = new mysqli($host, $username, $password, $dbname, $port);
    
    if ($mysqli->connect_error) {
        throw new Exception("Connection failed: " . $mysqli->connect_error);
    }
    
    // Test query to get users without password
    $stmt = $mysqli->prepare("SELECT id, correo, nombre_usuario, clave FROM tb_experian_usuarios WHERE clave IS NULL OR clave = '' ORDER BY nombre_usuario");
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $mysqli->error);
    }
    
    $stmt->execute();
    
    // Use bind_result for PHP 7.3.33 compatibility
    $users = array();
    $userId = null;
    $userEmail = null;
    $userName = null;
    $userClave = null;
    $stmt->bind_result($userId, $userEmail, $userName, $userClave);
    
    while ($stmt->fetch()) {
        $users[] = array(
            'id' => $userId,
            'correo' => $userEmail,
            'nombre_usuario' => $userName,
            'clave' => $userClave
        );
    }
    
    $stmt->close();
    $mysqli->close();
    
    echo json_encode([
        'success' => true,
        'users_count' => count($users),
        'users' => $users,
        'message' => 'Connection successful'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
