<?php
// Script simplificado para exportar prospectos a Excel (CSV) sin librerías externas
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
error_reporting(E_ALL);
ini_set('error_log', __DIR__ . '/logs/export_errors.log');

// Log para debugging
error_log("=== EXPORTAR_PROSPECTOS.PHP INICIADO ===");

// Iniciar sesión
session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario_id'])) {
    error_log("ERROR: Usuario no autenticado");
    die("Usuario no autenticado");
}

// Prevenir caché del navegador
header("Cache-Control: no-cache, no-store, must-revalidate");
header("Pragma: no-cache");
header("Expires: 0");

// Incluir archivos necesarios
require_once 'cache_utils.php';
require_once 'con_db.php';

// Aplicar headers anti-caché
no_cache_headers();

// Obtener parámetros de filtros
$filtro_ejecutivo = isset($_GET['ejecutivo']) ? $_GET['ejecutivo'] : 'todos';
$filtro_periodo = isset($_GET['periodo']) ? $_GET['periodo'] : 'año';
$filtro_fecha_inicio = isset($_GET['fecha_inicio']) ? $_GET['fecha_inicio'] : date('Y-01-01');
$filtro_fecha_fin = isset($_GET['fecha_fin']) ? $_GET['fecha_fin'] : date('Y-12-31');

// Calcular fechas según el periodo seleccionado
switch($filtro_periodo) {
    case 'hoy':
        $filtro_fecha_inicio = date('Y-m-d');
        $filtro_fecha_fin = date('Y-m-d');
        break;
    case 'semana':
        $filtro_fecha_inicio = date('Y-m-d', strtotime('monday this week'));
        $filtro_fecha_fin = date('Y-m-d', strtotime('sunday this week'));
        break;
    case 'mes_actual':
        $filtro_fecha_inicio = date('Y-m-01');
        $filtro_fecha_fin = date('Y-m-t');
        break;
    case 'trimestre':
        $trimestre = ceil(date('n') / 3);
        $filtro_fecha_inicio = date('Y-') . sprintf('%02d', ($trimestre - 1) * 3 + 1) . '-01';
        $filtro_fecha_fin = date('Y-m-t', strtotime($filtro_fecha_inicio . ' +2 months'));
        break;
    case 'año':
        $filtro_fecha_inicio = date('Y-01-01');
        $filtro_fecha_fin = date('Y-12-31');
        break;
}

// Construir condición WHERE para filtros
$where_conditions = ["1=1"];
$params = [];
$types = "";

if ($filtro_ejecutivo !== 'todos') {
    $where_conditions[] = "p.usuario_id = ?";
    $params[] = $filtro_ejecutivo;
    $types .= "i";
}

$where_conditions[] = "DATE(p.fecha_registro) BETWEEN ? AND ?";
$params[] = $filtro_fecha_inicio;
$params[] = $filtro_fecha_fin;
$types .= "ss";

$where_clause = implode(" AND ", $where_conditions);

// Consulta principal para obtener prospectos con solo columnas que existen
$query = "
    SELECT
        p.id,
        p.rut_cliente,
        p.razon_social,
        p.rubro,
        p.direccion_comercial,
        p.telefono_celular,
        p.email,
        p.numero_pos,
        p.tipo_cuenta,
        p.numero_cuenta_bancaria,
        p.dias_atencion,
        p.horario_atencion,
        p.contrata_boleta,
        p.competencia_actual,
        p.fecha_registro,
        p.estado,
        COALESCE(u.nombre_usuario, p.nombre_ejecutivo) as ejecutivo_nombre_usuario
    FROM
        tb_inteletgroup_prospectos p
    LEFT JOIN
        tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    WHERE
        $where_clause
    ORDER BY
        p.fecha_registro DESC";

// Ejecutar la consulta
$stmt = $mysqli->prepare($query);
if (!$stmt) {
    error_log("ERROR: Error preparando consulta: " . $mysqli->error);
    error_log("Query: " . $query);
    die("Error preparando consulta: " . $mysqli->error);
}

if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}

if (!$stmt->execute()) {
    error_log("ERROR: Error ejecutando consulta: " . $stmt->error);
    die("Error ejecutando consulta: " . $stmt->error);
}

$result = $stmt->get_result();
$prospectos = [];

while ($row = $result->fetch_assoc()) {
    // Obtener información sobre documentos para cada prospecto
    $doc_query = "
        SELECT
            COUNT(DISTINCT d.id) as total_documentos,
            COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
            COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
        FROM
            tb_inteletgroup_prospectos p
        LEFT JOIN
            tb_inteletgroup_tipos_documento td ON
            (td.tipo_persona = 'Ambos' OR td.tipo_persona = 'Natural' OR td.tipo_persona = 'Juridica')
            AND td.estado = 'Activo'
        LEFT JOIN
            tb_inteletgroup_documentos d ON
            p.id = d.prospecto_id
            AND d.tipo_documento_id = td.id
            AND d.estado = 'Activo'
        WHERE
            p.id = ?";

    $doc_stmt = $mysqli->prepare($doc_query);
    if ($doc_stmt) {
        $doc_stmt->bind_param("i", $row['id']);
        $doc_stmt->execute();
        $doc_result = $doc_stmt->get_result();
        $doc_row = $doc_result->fetch_assoc();
        $doc_stmt->close();

        $total_documentos = $doc_row['total_documentos'] ?? 0;
        $obligatorios_completados = $doc_row['obligatorios_completados'] ?? 0;
        $total_obligatorios = $doc_row['total_obligatorios'] ?? 0;
        $porcentaje_completado = $total_obligatorios > 0 ?
            round(($obligatorios_completados / $total_obligatorios) * 100) : 0;
    } else {
        $total_documentos = 0;
        $obligatorios_completados = 0;
        $total_obligatorios = 0;
        $porcentaje_completado = 0;
    }

    // Crear fila de datos para exportación
    $prospectos[] = [
        'ID' => $row['id'],
        'RUT Cliente' => $row['rut_cliente'],
        'Razón Social' => $row['razon_social'],
        'Rubro' => $row['rubro'],
        'Dirección Comercial' => $row['direccion_comercial'],
        'Teléfono Celular' => $row['telefono_celular'],
        'Email' => $row['email'],
        'Número POS' => $row['numero_pos'],
        'Tipo Cuenta' => $row['tipo_cuenta'],
        'Número Cuenta Bancaria' => $row['numero_cuenta_bancaria'],
        'Días Atención' => $row['dias_atencion'],
        'Horario Atención' => $row['horario_atencion'],
        'Contrata Boleta' => $row['contrata_boleta'],
        'Competencia Actual' => $row['competencia_actual'],
        'Fecha Registro' => $row['fecha_registro'],
        'Estado' => $row['estado'],
        'Ejecutivo' => $row['ejecutivo_nombre_usuario'],
        'Total Documentos' => $total_documentos,
        'Documentos Obligatorios Completados' => $obligatorios_completados,
        'Total Documentos Obligatorios' => $total_obligatorios,
        'Porcentaje Completado' => $porcentaje_completado . '%'
    ];
}

$stmt->close();

// Verificar si hay datos
if (empty($prospectos)) {
    error_log("ADVERTENCIA: No hay prospectos para exportar con los filtros aplicados");
    die("No hay datos para exportar con los filtros seleccionados");
}

error_log("INFO: Se encontraron " . count($prospectos) . " prospectos para exportar");

// Nombre del archivo para la descarga
$fecha_actual = date('Y-m-d_H-i-s');
$nombre_archivo = "prospectos_inteletgroup_{$fecha_actual}.csv";

// Configurar encabezados para descarga de CSV
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $nombre_archivo . '"');
header('Content-Transfer-Encoding: binary');
header('Expires: 0');
header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Pragma: public');

// Crear el recurso de salida para escribir el CSV
$output = fopen('php://output', 'w');

// Configurar para que funcione con caracteres especiales (acentos, etc)
// BOM para UTF-8
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Escribir encabezados de las columnas
if (!empty($prospectos)) {
    fputcsv($output, array_keys($prospectos[0]), ';'); // Usar punto y coma como separador para Excel
}

// Escribir datos
foreach ($prospectos as $prospecto) {
    fputcsv($output, $prospecto, ';'); // Usar punto y coma como separador para Excel
}

fclose($output);

error_log("INFO: Exportación completada exitosamente");

// Cerrar conexión
if (isset($mysqli)) {
    $mysqli->close();
}

exit;
