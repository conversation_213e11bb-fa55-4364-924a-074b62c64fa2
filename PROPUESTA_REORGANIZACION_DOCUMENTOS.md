# 📋 PROPUESTA DE REORGANIZACIÓN - TABLA DOCUMENTOS

## 🔍 **ANÁLISIS DEL PROBLEMA ACTUAL:**

### **❌ Problemas identificados:**
1. **ID 19** (AMBOS_MANDATO_PAC) - **DUPLICADO** con ID 8 (PN_MANDATO_PAC)
2. **ID 21** (AMBOS_PODER_NOTARIAL) - **DUPLICADO** con ID 9 (PN_PODER_NOTARIAL)
3. **ID 7** (PN_CARPETA_TRIBUTARIA) - Debería ser **obligatorio** para Persona Natural
4. **IDs 7, 8, 9, 10** - Deberían ser tipo `'Ambos'` para reutilización

## 🎯 **SOLUCIÓN RECOMENDADA:**

### **Opción A: REORGANIZACIÓN COMPLETA (RECOMENDADA)**

#### **Ventajas:**
- ✅ Elimina duplicados completamente
- ✅ Estructura más limpia y mantenible
- ✅ Reutilización de documentos entre tipos de persona
- ✅ Consistencia en códigos y nombres

#### **Cambios a realizar:**

**1. ELIMINAR DUPLICADOS:**
```sql
DELETE FROM tb_inteletgroup_tipos_documento WHERE id IN (19, 21);
```

**2. CONVERTIR A TIPO 'AMBOS':**
```sql
-- Cambiar IDs 7, 8, 9, 10 de 'Natural' a 'Ambos'
UPDATE tb_inteletgroup_tipos_documento 
SET tipo_persona = 'Ambos' 
WHERE id IN (7, 8, 9, 10);

-- Actualizar códigos
UPDATE tb_inteletgroup_tipos_documento SET codigo = 'AMBOS_CARPETA_TRIBUTARIA' WHERE id = 7;
UPDATE tb_inteletgroup_tipos_documento SET codigo = 'AMBOS_MANDATO_PAC' WHERE id = 8;
UPDATE tb_inteletgroup_tipos_documento SET codigo = 'AMBOS_PODER_NOTARIAL' WHERE id = 9;
UPDATE tb_inteletgroup_tipos_documento SET codigo = 'AMBOS_CARTA_ACEPTACION' WHERE id = 10;
```

**3. CORREGIR OBLIGATORIEDAD:**
```sql
-- Carpeta Tributaria debe ser obligatoria para Natural
UPDATE tb_inteletgroup_tipos_documento SET es_obligatorio = 1 WHERE id = 7;

-- Carpeta Tributaria y E-Rut obligatorios para Jurídica
UPDATE tb_inteletgroup_tipos_documento SET es_obligatorio = 1 WHERE id IN (17, 18);
```

**4. ACTUALIZAR NOMBRES:**
```sql
-- Renombrar ID 20 para evitar confusión
UPDATE tb_inteletgroup_tipos_documento 
SET codigo = 'AMBOS_CUENTAS_TERCEROS',
    nombre = 'Cuentas de abono de terceros'
WHERE id = 20;
```

### **Opción B: REORGANIZACIÓN MÍNIMA (ALTERNATIVA)**

Si prefieres cambios mínimos:

**1. Solo eliminar duplicados:**
```sql
DELETE FROM tb_inteletgroup_tipos_documento WHERE id IN (19, 21);
```

**2. Mantener estructura actual pero corregir obligatoriedad:**
```sql
UPDATE tb_inteletgroup_tipos_documento SET es_obligatorio = 1 WHERE id = 7;
UPDATE tb_inteletgroup_tipos_documento SET es_obligatorio = 1 WHERE id IN (17, 18);
```

## 📊 **RESULTADO ESPERADO (OPCIÓN A):**

### **PERSONA NATURAL:**
- **Obligatorios (7):** IDs 1, 2, 3, 4, 5, 6, 7
- **Opcionales (5):** IDs 8, 9, 10, 20, 22 (tipo 'Ambos')
- **Total:** 12 documentos

### **PERSONA JURÍDICA:**
- **Obligatorios (8):** IDs 11, 12, 13, 14, 15, 16, 17, 18
- **Opcionales (5):** IDs 8, 9, 10, 20, 22 (tipo 'Ambos')
- **Total:** 13 documentos

### **DOCUMENTOS TIPO 'AMBOS' (5):**
1. **ID 7** - Carpeta Tributaria (FAPRO) - **Obligatorio**
2. **ID 8** - Mandato PAC - Opcional
3. **ID 9** - Poder Notarial - Opcional
4. **ID 10** - Carta de Aceptación - Opcional
5. **ID 20** - Cuentas de abono de terceros - Opcional
6. **ID 22** - Sociedad de Hecho - Declaración Jurada Notarial - Opcional

## 🔧 **IMPACTO EN EL CÓDIGO:**

### **JavaScript (inteletgroup-prospect.js):**
Necesitará actualización para usar los nuevos códigos:
- `PN_CARPETA_TRIBUTARIA` → `AMBOS_CARPETA_TRIBUTARIA`
- `PN_MANDATO_PAC` → `AMBOS_MANDATO_PAC`
- `PN_PODER_NOTARIAL` → `AMBOS_PODER_NOTARIAL`
- `PN_CARTA_ACEPTACION` → `AMBOS_CARTA_ACEPTACION`

### **Backend (PHP):**
No requiere cambios - ya maneja documentos tipo 'Ambos'

## 🎯 **RECOMENDACIÓN:**

**Ejecutar OPCIÓN A (Reorganización Completa)** porque:
1. ✅ Elimina duplicados definitivamente
2. ✅ Estructura más escalable
3. ✅ Facilita mantenimiento futuro
4. ✅ Consistencia en nomenclatura
5. ✅ Reutilización eficiente de documentos

## 📝 **PRÓXIMOS PASOS:**

1. **Ejecutar scripts SQL** de reorganización
2. **Actualizar JavaScript** con nuevos códigos
3. **Probar formulario** con ambos tipos de persona
4. **Verificar checklist** de documentos
5. **Documentar cambios** para el equipo

¿Prefieres que proceda con la **Opción A (Reorganización Completa)** o la **Opción B (Mínima)**?
