<?php

header('Content-Type: text/html; charset=UTF-8');
//Iniciar una nueva sesión o reanudar la existente.


$inc = include("con_db.php");
session_start();


$usuario = $_GET['usuario'];



$detalle = $conex->query(
  "
  SELECT FECHA 
  , A.id 
  , tipo_reclamo 
  , A.orden 
  , CASE 
       WHEN A.tipo_reclamo = 'declaracion' then B.SLA_DECLARACION                 
       WHEN A.tipo_reclamo = 'produccion' THEN 
                  CASE 
                      WHEN B.orden is not null THEN 'EN PRODUCCION'
                      ELSE 'Sin Produccion'
                  END 
  END  ESTADO_NDC
  ,'' Resolucion
  FROM TB_TECNICO_RECLAMO A
  LEFT JOIN (SELECT DISTINCT orden , SLA_DECLARACION 
              FROM tb_paso_pyNdc 
            ) B 
  ON A.orden = B.orden 
  WHERE rut_tecnico = '" . $usuario . "'   
  and tipo_reclamo = 'declaracion'
"
);


?>



<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="APP TQW">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>APP TQW</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/logo_con.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">

  <style>
  table#dataTable4 th,
  table#dataTable4 td {
    white-space: normal;
    word-wrap: break-word;
    max-width: 100px; /* Ajusta el ancho máximo de las celdas según tus necesidades */
  }
</style>

</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card -->
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">
      <!-- Header Content -->
      <div class="header-content position-relative d-flex align-items-center justify-content-between">
        <!-- Back Button -->
        <div class="back-button">
          <a href="home.php?usuario=<?php echo $usuario; ?>">
            <i class="bi bi-arrow-left-short"></i>
          </a>
        </div>

        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0">FORMULARIO INGRESO SOLICITUD A REVISIÓN</h6>
        </div>

        <!-- Settings -->
        <!-- <div class="setting-wrapper">
          <div class="setting-trigger-btn" id="settingTriggerBtn">
            <i class="bi bi-gear"></i>
            <span></span>
          </div>
        </div> -->
      </div>
    </div>
  </div>

  <div class="page-content-wrapper py-3">
    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading">
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <form action="Action_tecnico_reclamo.php?usuario=<?php echo $usuario; ?>" method="POST"
            enctype="multipart/form-data">

            <div class="form-group" style="display: none;">
              <input type="hidden" name="Usuario" value="<?php echo $usuario ?>">
            </div>


            <div class="form-group">
              <label class="form-label" for="defaultSelectLg">¿Que tipo de observación realizará?</label>
              <select class="form-select form-select-lg" id="tipo" name="tipo" aria-label="Default select example">


                <option value="produccion">Orden no figura en producción</option>
                <option value="declaracion">Orden declarada dentro de plazo</option>

              </select>
            </div>

            <div class="form-group">
              <label class="form-label" for="exampleInputText">ORDEN</label>
              <input class="form-control" id="Orden" type="text" name="Orden">
            </div>




            <div class="form-group">
              <label class="form-label" for="exampleTextarea4">Observación</label>
              <textarea class="form-control" id="obs" name="obs" cols="3" rows="5"></textarea>
            </div>






            <input type="submit" class="btn btn-primary w-100 d-flex align-items-center justify-content-center">

            </input>
          </form>
        </div>
      </div>
    </div>

    <div class="container">
        <div class="element-heading-wrapper">
        
          <div class="heading-text">
            <h6 class="mb-1"> </h6>
          
          </div>
        </div>
      </div>
    <div class="container">
        <div class="element-heading-wrapper">
        
          <div class="heading-text">
            <h6 class="mb-1">ESTADO DE LAS SOLICITUDES A  REVISIÓN</h6>
          
          </div>
        </div>
      </div>



    <div class="container">
      <div class="card">
        <div class="standard-tab">
          <ul class="nav rounded-lg mb-2 p-2 shadow-sm" id="affanTabs1" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="btn active" id="bootstrap-tab" data-bs-toggle="tab" data-bs-target="#bootstrap"
                type="button" role="tab" aria-controls="bootstrap" aria-selected="true">DECLARADA</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="btn" id="pwa-tab" data-bs-toggle="tab" data-bs-target="#pwa" type="button" role="tab"
                aria-controls="pwa" aria-selected="false">SIN PRODUCCION</button>
            </li>

          </ul>

          <div class="tab-content rounded-lg p-3 shadow-sm" id="affanTabs1Content">
            <div class="tab-pane fade show active" id="bootstrap" role="tabpanel" aria-labelledby="bootstrap-tab">
              <table class="table mb-0 table-striped" id="dataTable4">

                <thead>
                  <tr>
                    <th scope="col">Orden</th>
                    <th scope="col">Fecha Solicitud</th>
                    
                    <th scope="col">EN NDC</th>
                    <th scope="col">Resolución TQW</th>
                  </tr>
                </thead>
                <tbody>
                  <?php while ($row2 = mysqli_fetch_array($detalle)) { ?>
                    <tr>
                      <td>
                        <span style="font-size: 11px;">
                          <?php echo $row2['orden']; ?>
                        </span>
                      </td>
                      <td>
                        <span style="font-size: 11px;">
                          <?php echo $row2['FECHA']; ?>
                        </span>
                      </td>
                      
                      <td>
                        <span style="font-size: 11px;">
                          <?php echo $row2['ESTADO_NDC']; ?>
                        </span>
                      </td>
                      <td>

                      </td>
                    </tr>
                  <?php } ?>
                </tbody>
              </table>
            </div>

            <div class="tab-pane fade" id="pwa" role="tabpanel" aria-labelledby="pwa-tab">

              <table class="table mb-0 table-striped" id="dataTable4">

              <thead>
                  <tr>
                    <th scope="col">Orden</th>
                    <th scope="col">Fecha Solicitud</th>
                    
                    <th scope="col">EN NDC</th>
                    <th scope="col">Resolución TQW</th>
                  </tr>
                </thead>
                <tbody>
                  <?php while ($row2 = mysqli_fetch_array($detalle)) { ?>
                    <tr>
                      <td>
                        <span style="font-size: 11px;">
                          <?php echo $row2['orden']; ?>
                        </span>
                      </td>
                      <td>
                        <span style="font-size: 11px;">
                          <?php echo $row2['FECHA']; ?>
                        </span>
                      </td>
                      
                      <td>
                        <span style="font-size: 11px;">
                          <?php echo $row2['ESTADO_NDC']; ?>
                        </span>
                      </td>
                      <td>

                      </td>
                    </tr>
                  <?php } ?>
                </tbody>
              </table>
            </div>


          </div>
        </div>
      </div>
    </div>

  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">

          <li>
            <a href="home.php?usuario=<?php echo $usuario; ?>">
              <i class="bi bi-house"></i>
              <span>Home</span>
            </a>
          </li>

          <li>
            <a href="TecnicoProduccion.php?usuario=<?php echo $usuario; ?>">
              <i class="bi bi-bar-chart-fill"></i>
              <span>Graficos</span>
            </a>
          </li>

          <li>
            <a href="Tecnico_Cubos.php?usuario=<?php echo $usuario; ?>">
              <i class="bi bi-file-earmark-bar-graph-fill"></i>
              <span>Cubo de datos</span>
            </a>
          </li>

          <li class="active">
            <a href="tecnico_revision.php?usuario=<?php echo $usuario; ?>">
              <i class="bi bi-shield-exclamation"></i>
              <span>Revisión</span>
            </a>
          </li>


        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
</body>

</html>