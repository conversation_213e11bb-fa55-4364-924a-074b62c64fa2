<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Iniciar sesión
session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || empty($_SESSION['usuario_id'])) {
    // Redirigir a login con mensaje de error
    header('Location: login.php?error=' . urlencode("Debe iniciar sesión para continuar."));
    exit();
}

// Incluir archivo de conexión a la base de datos y verificar conexión
require_once 'con_db.php';
if (!isset($conn) || $conn === null) {
    header('Location: form_experian2.php?error=' . urlencode("Error de conexión a la base de datos"));
    exit();
}

// Función para limpiar y validar datos
function sanitize($conn, $data) {
    return mysqli_real_escape_string($conn, trim($data));
}

// Verificar que la solicitud sea POST
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    header('Content-Type: application/json'); // Establecer el tipo de contenido a JSON

    // Obtener y sanitizar datos del formulario
    $nombre_ejecutivo = isset($_POST['nombre_prospecto']) ? sanitize($conn, $_POST['nombre_prospecto']) : '';
    $rut_ejecutivo = isset($_POST['rut_ejecutivo']) ? sanitize($conn, $_POST['rut_ejecutivo']) : '';
    $razon_social = isset($_POST['razon_social']) ? sanitize($conn, $_POST['razon_social']) : '';
    $rubro = isset($_POST['rubro']) ? sanitize($conn, $_POST['rubro']) : '';
    $contacto = isset($_POST['contacto']) ? sanitize($conn, $_POST['contacto']) : '';
    $telefono = isset($_POST['telefono']) ? sanitize($conn, $_POST['telefono']) : '';
    $fecha = isset($_POST['fecha']) ? sanitize($conn, $_POST['fecha']) : '';
    $estado = isset($_POST['estado']) ? sanitize($conn, $_POST['estado']) : '';
    $observaciones = isset($_POST['observaciones']) ? mysqli_real_escape_string($conn, $_POST['observaciones']) : '';
    $usuario_id = $_SESSION['usuario_id']; // ID del usuario logueado

    // Validar campos requeridos
    if (empty($nombre_ejecutivo) || empty($rut_ejecutivo) || empty($razon_social) ||
        empty($rubro) || empty($contacto) || empty($telefono) || empty($fecha) || empty($estado)) {

        // Devolver error JSON
        echo json_encode(['success' => false, 'message' => 'Todos los campos son obligatorios']);
        exit();
    }

    // Consulta SQL para insertar datos
    $sql = "INSERT INTO tb_experian_prospecto (
                nombre_ejecutivo,
                rut_ejecutivo,
                razon_social,
                rubro,
                contacto,
                telefono,
                fecha,
                estado,
                observaciones,
                fecha_registro,
                usuario_id
            ) VALUES (
                '$nombre_ejecutivo',
                '$rut_ejecutivo',
                '$razon_social',
                '$rubro',
                '$contacto',
                '$telefono',
                '$fecha',
                '$estado',
                '$observaciones',
                NOW(),
                $usuario_id
            )";

    // Ejecutar consulta para guardar el prospecto
    if (mysqli_query($conn, $sql)) {
        // Prospecto guardado correctamente, ahora guardar en la bitácora
        // Preparar la observación para la bitácora
        $observacion_bitacora = isset($_POST['observaciones']) && !empty($_POST['observaciones'])
                              ? "Prospecto creado: $razon_social - " . $_POST['observaciones']
                              : "Prospecto creado: $razon_social";

        $bitacora_sql = "INSERT INTO tb_experian_prospecto_bitacora (
                            rut_ejecutivo,
                            estado,
                            observaciones,
                            usuario_id
                        ) VALUES (
                            '$rut_ejecutivo',
                            '$estado',
                            '$observacion_bitacora',
                            $usuario_id
                        )";

        // Ejecutar consulta para guardar en la bitácora
        if (mysqli_query($conn, $bitacora_sql)) {
            // Ambas operaciones exitosas
            echo json_encode(['success' => true, 'message' => 'Prospecto registrado correctamente']);
            exit();
        } else {
            // El prospecto se guardó pero hubo un error al guardar en la bitácora
            $error_message = "Prospecto registrado, pero hubo un error al guardar en la bitácora: " . mysqli_error($conn);
            escribir_log($error_message, 'warning'); // Loguear el error como advertencia
            echo json_encode(['success' => true, 'message' => 'Prospecto registrado correctamente, pero no se pudo guardar en la bitácora']);
            exit();
        }
    } else {
        // Error al guardar el prospecto
        $error_message = "Error al registrar prospecto: " . mysqli_error($conn);
        escribir_log($error_message, 'error'); // Loguear el error
        echo json_encode(['success' => false, 'message' => $error_message]);
        exit();
    }

} else {
    // Si no es una solicitud POST, devolver error JSON
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit();
}

// Cerrar conexión (ya se maneja con register_shutdown_function en con_db.php)
// mysqli_close($conn); // No es necesario aquí si se usa register_shutdown_function