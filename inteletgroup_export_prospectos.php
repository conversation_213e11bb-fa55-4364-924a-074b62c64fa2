<?php
// Configuración de errores para desarrollo - mostrar todos los errores posibles
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Configurar log de errores personalizado para este script
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/logs/export_errors.log');

// Log detallado para debugging
error_log("=== DETALLADO: Iniciando script de exportación ===\n");
error_log("PHP Version: " . phpversion());
error_log("Server info: " . $_SERVER['SERVER_SOFTWARE']);

// Registrar variables de sesión disponibles
error_log("Sesión activa: " . (session_status() === PHP_SESSION_ACTIVE ? 'Sí' : 'No'));
error_log("Variables de sesión: " . print_r($_SESSION, true));

// Registrar todos los parámetros GET recibidos
error_log("Parámetros GET recibidos: " . print_r($_GET, true));

// Aumentar límites para evitar timeouts
ini_set('max_execution_time', 300);
ini_set('memory_limit', '256M');

// Log para debugging
error_log("=== INTELETGROUP_EXPORT_PROSPECTOS.PHP INICIADO ===");

// Iniciar sesión
session_start();

// Verificar autenticación y permisos de administrador
// Solo permitir acceso a usuarios de inteletGroup
$es_ajax = isset($_GET['ajax']) && $_GET['ajax'] == 1;
error_log("DEBUG: Verificando autenticación. Es solicitud AJAX: " . ($es_ajax ? 'Sí' : 'No'));

if (!isset($_SESSION['usuario_id'])) {
    error_log("ERROR: Usuario no autenticado. ID: " . ($_SESSION['usuario_id'] ?? 'no definido'));
    
    if ($es_ajax) {
        // Para AJAX, enviar error HTTP en lugar de redireccionar
        header('HTTP/1.1 401 Unauthorized');
        echo "Error: Usuario no autenticado";
        exit;
    } else {
        $errorMsg = urlencode("Usuario no autenticado.");
        header('Location: login.php?error=' . $errorMsg);
        exit;
    }
}

if (!isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    error_log("ERROR: Usuario sin permisos para InteletGroup. Proyecto: " . ($_SESSION['proyecto'] ?? 'no definido'));
    
    if ($es_ajax) {
        // Para AJAX, enviar error HTTP en lugar de redireccionar
        header('HTTP/1.1 403 Forbidden');
        echo "Error: Sin permisos para acceder a InteletGroup";
        exit;
    } else {
        $errorMsg = urlencode("Sin permisos para acceder a InteletGroup.");
        header('Location: login.php?error=' . $errorMsg);
        exit;
    }
}

// Prevenir caché del navegador
header("Cache-Control: no-cache, no-store, must-revalidate");
header("Pragma: no-cache");
header("Expires: 0");

// Incluir archivos necesarios
error_log("Incluyendo archivos necesarios");

// Verificar archivos requeridos
if (!file_exists('cache_utils.php')) {
    error_log("ERROR CRÍTICO: No se encuentra el archivo cache_utils.php");
    die("Error: No se encuentra un archivo de sistema requerido (cache_utils.php)");
}

if (!file_exists('con_db.php')) {
    error_log("ERROR CRÍTICO: No se encuentra el archivo con_db.php");
    die("Error: No se encuentra un archivo de sistema requerido (con_db.php)");
}

require_once 'cache_utils.php';
error_log("Archivo cache_utils.php incluido correctamente");

try {
    error_log("Intentando incluir con_db.php");
    require_once 'con_db.php';
    error_log("Archivo con_db.php incluido correctamente");
} catch (Throwable $e) {
    error_log("ERROR CRÍTICO: Excepción al incluir con_db.php: " . $e->getMessage());
    error_log("Traza: " . $e->getTraceAsString());
    die("Error de conexión a la base de datos: " . $e->getMessage());
}

// Aplicar headers anti-caché
no_cache_headers();

// Verificar conexión a base de datos con logs detallados
try {
    if (!isset($mysqli)) {
        error_log("ERROR: Variable mysqli no está definida después de incluir con_db.php");
        
        // Verificar que se cargó el archivo con_db.php
        error_log("Verificando si existe con_db.php: " . (file_exists('con_db.php') ? 'Sí' : 'No'));
        
        // Ver qué variables globales están definidas
        error_log("Variables globales disponibles: " . print_r($GLOBALS, true));
        
        throw new Exception("Error de conexión a la base de datos. Variable mysqli no definida.");
    }

    if ($mysqli->connect_error) {
        error_log("ERROR: Error de conexión MySQL: " . $mysqli->connect_error);
        throw new Exception("Error de conexión a la base de datos: " . $mysqli->connect_error);
    }
    
    // Información sobre la conexión
    error_log("DEBUG: Conexión a BD establecida correctamente");
    error_log("Versión MySQL: " . $mysqli->server_info);
    error_log("Versión del protocolo: " . $mysqli->protocol_version);
    error_log("Set de caracteres: " . $mysqli->character_set_name());
    
} catch (Exception $e) {
    error_log("EXCEPCIÓN CAPTURADA en verificación de conexión: " . $e->getMessage());
    error_log("Traza: " . $e->getTraceAsString());
    
    if (isset($_GET['ajax']) && $_GET['ajax'] == 1) {
        header('HTTP/1.1 500 Internal Server Error');
        echo "Error de conexión a la base de datos: " . $e->getMessage();
    } else {
        die("Error de conexión a la base de datos: " . $e->getMessage());
    }
    exit;
}

// Obtener parámetros de filtros
$filtro_ejecutivo = $_GET['ejecutivo'] ?? 'todos';
$filtro_periodo = $_GET['periodo'] ?? 'año';
$filtro_fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-01-01');
$filtro_fecha_fin = $_GET['fecha_fin'] ?? date('Y-12-31');

// Calcular fechas según el periodo seleccionado
switch($filtro_periodo) {
    case 'hoy':
        $filtro_fecha_inicio = date('Y-m-d');
        $filtro_fecha_fin = date('Y-m-d');
        break;
    case 'semana':
        $filtro_fecha_inicio = date('Y-m-d', strtotime('monday this week'));
        $filtro_fecha_fin = date('Y-m-d', strtotime('sunday this week'));
        break;
    case 'mes_actual':
        $filtro_fecha_inicio = date('Y-m-01');
        $filtro_fecha_fin = date('Y-m-t');
        break;
    case 'trimestre':
        $trimestre = ceil(date('n') / 3);
        $filtro_fecha_inicio = date('Y-') . sprintf('%02d', ($trimestre - 1) * 3 + 1) . '-01';
        $filtro_fecha_fin = date('Y-m-t', strtotime($filtro_fecha_inicio . ' +2 months'));
        break;
    case 'año':
        $filtro_fecha_inicio = date('Y-01-01');
        $filtro_fecha_fin = date('Y-12-31');
        break;
}

// Construir condición WHERE para filtros
$where_conditions = ["1=1"];
$params = [];
$types = "";

if ($filtro_ejecutivo !== 'todos') {
    $where_conditions[] = "p.usuario_id = ?";
    $params[] = $filtro_ejecutivo;
    $types .= "i";
}

$where_conditions[] = "DATE(p.fecha_registro) BETWEEN ? AND ?";
$params[] = $filtro_fecha_inicio;
$params[] = $filtro_fecha_fin;
$types .= "ss";

$where_clause = implode(" AND ", $where_conditions);

error_log("INFO: Exportando prospectos con filtros - Ejecutivo: $filtro_ejecutivo, Periodo: $filtro_periodo");
error_log("INFO: Fechas - Inicio: $filtro_fecha_inicio, Fin: $filtro_fecha_fin");
error_log("INFO: WHERE clause: $where_clause");

// Consulta para obtener TODOS los prospectos con detalles incluyendo TODAS las columnas
$query = "
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
        p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
        p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE " . $where_clause . "
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, 
             p.telefono_celular, p.telefono_fijo, p.direccion, p.ciudad, p.comuna,
             p.codigo_comercial, p.monto_venta, p.origen, p.motivo_rechazo, p.fecha_registro, 
             p.fecha_modificacion, p.usuario_id, p.empresa_id, p.estado, u.nombre_usuario
    ORDER BY p.fecha_registro DESC";

// Verificar conexión activa
if (!$mysqli || $mysqli->connect_errno) {
    error_log("ERROR: Conexión a BD no válida");
    die("Error: Conexión a base de datos no disponible");
}

error_log("Preparando consulta principal: " . $query);
error_log("Parámetros: " . print_r($params, true));
error_log("Tipos de parámetros: " . $types);

try {
    $stmt = $mysqli->prepare($query);
    if (!$stmt) {
        error_log("ERROR: Error preparando consulta: " . $mysqli->error);
        throw new Exception("Error preparando consulta: " . $mysqli->error);
    }

    if (!empty($params)) {
        error_log("Vinculando parámetros a la consulta principal");
        $bind_result = $stmt->bind_param($types, ...$params);
        if (!$bind_result) {
            error_log("ERROR: Error vinculando parámetros: " . $stmt->error);
            throw new Exception("Error vinculando parámetros: " . $stmt->error);
        }
    }

    error_log("Ejecutando consulta principal");
    if (!$stmt->execute()) {
        error_log("ERROR: Error ejecutando consulta: " . $stmt->error);
        throw new Exception("Error ejecutando consulta: " . $stmt->error);
    }
    
    error_log("Consulta principal ejecutada correctamente");
} catch (Exception $e) {
    error_log("EXCEPCIÓN CAPTURADA en la consulta principal: " . $e->getMessage());
    error_log("Traza: " . $e->getTraceAsString());
    
    if (isset($_GET['ajax']) && $_GET['ajax'] == 1) {
        header('HTTP/1.1 500 Internal Server Error');
        echo "Error en la consulta: " . $e->getMessage();
    } else {
        die("Error en la consulta: " . $e->getMessage());
    }
    exit;
}

// Intentar obtener el resultado
$result = null;
try {
    // Verificar si método existe antes de llamarlo
    if (method_exists($stmt, 'get_result')) {
        $result = $stmt->get_result();
        error_log("DEBUG: Método get_result() ejecutado correctamente");
    } else {
        error_log("DEBUG: Método get_result() no disponible en este servidor");
        $result = null;
    }
} catch (Throwable $e) {
    error_log("ERROR: Excepción al obtener resultados: " . $e->getMessage());
    $result = null;
}

if (!$result) {
    // Compatibilidad con servidores sin mysqlnd
    error_log("ERROR: get_result() no disponible. Usando método alternativo.");
    
    // Vamos a usar un enfoque más simple para evitar problemas
    error_log("DEBUG: Modificando consulta para hacerla más simple y compatible");
    
    // Crear una consulta más simple y compatible con todos los servidores MySQL
    $simple_query = "SELECT 
            p.id, 
            p.tipo_persona, 
            p.rut_cliente, 
            p.razon_social, 
            p.rubro,
            p.email, 
            p.telefono_celular, 
            p.telefono_fijo, 
            p.direccion, 
            p.ciudad, 
            p.comuna,
            p.codigo_comercial, 
            p.monto_venta, 
            p.origen, 
            p.motivo_rechazo, 
            p.fecha_registro, 
            p.fecha_modificacion, 
            p.usuario_id, 
            p.empresa_id, 
            p.estado,
            COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario
        FROM tb_inteletgroup_prospectos p
        LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
        WHERE " . $where_clause . "
        ORDER BY p.fecha_registro DESC";
    
    error_log("DEBUG: Nueva consulta: {$simple_query}");
    
    // Cerrar la consulta anterior y preparar la nueva
    $stmt->close();
    $stmt = $mysqli->prepare($simple_query);
    if (!$stmt) {
        error_log("ERROR: Error preparando consulta simplificada: " . $mysqli->error);
        error_log("Consulta: " . $simple_query);
        error_log("Estado de la conexión: " . print_r($mysqli, true));
        throw new Exception("Error preparando consulta: " . $mysqli->error);
    }
    
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    if (!$stmt->execute()) {
        error_log("ERROR: Error ejecutando consulta simplificada: " . $stmt->error);
        die("Error ejecutando consulta: " . $stmt->error);
    }
    
    // Definir variables para bind_result - simplificado
    $id = $tipo_persona = $rut_cliente = $razon_social = $rubro = $email = $telefono_celular = 
    $telefono_fijo = $direccion = $ciudad = $comuna = $codigo_comercial = $monto_venta = 
    $origen = $motivo_rechazo = $fecha_registro = $fecha_modificacion = $usuario_id = 
    $empresa_id = $estado = $ejecutivo_nombre_usuario = null;
    
    $stmt->bind_result(
        $id, $tipo_persona, $rut_cliente, $razon_social, $rubro, $email, $telefono_celular,
        $telefono_fijo, $direccion, $ciudad, $comuna, $codigo_comercial, $monto_venta,
        $origen, $motivo_rechazo, $fecha_registro, $fecha_modificacion, $usuario_id,
        $empresa_id, $estado, $ejecutivo_nombre_usuario
    );
    
    // Crear array para almacenar resultados
    $prospectos = [];
    while ($stmt->fetch()) {
        // Consulta separada para obtener documentos para este prospecto
        $doc_query = "SELECT 
            COUNT(DISTINCT d.id) as total_documentos,
            COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
            COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
        FROM tb_inteletgroup_prospectos p
        LEFT JOIN tb_inteletgroup_tipos_documento td ON
            (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
             OR td.tipo_persona = 'Ambos')
            AND td.estado = 'Activo'
        LEFT JOIN tb_inteletgroup_documentos d ON
            p.id = d.prospecto_id
            AND d.tipo_documento_id = td.id
            AND d.estado = 'Activo'
        WHERE p.id = ?";
        
        $doc_stmt = $mysqli->prepare($doc_query);
        if (!$doc_stmt) {
            error_log("ERROR: Error preparando consulta de documentos: " . $mysqli->error);
            $total_documentos = $obligatorios_completados = $total_obligatorios = 0;
        } else {
            $doc_stmt->bind_param("i", $id);
            $doc_stmt->execute();
            $doc_stmt->bind_result($total_documentos, $obligatorios_completados, $total_obligatorios);
            $doc_stmt->fetch();
            $doc_stmt->close();
        }
        
        $porcentaje_completado = $total_obligatorios > 0 ? round(($obligatorios_completados / $total_obligatorios) * 100) : 0;
        
        $prospectos[] = [
            'ID' => $id,
            'Tipo de Persona' => $tipo_persona,
            'RUT' => $rut_cliente,
            'Razón Social' => $razon_social,
            'Rubro' => $rubro,
            'Email' => $email,
            'Teléfono Celular' => $telefono_celular,
            'Teléfono Fijo' => $telefono_fijo,
            'Dirección' => $direccion,
            'Ciudad' => $ciudad,
            'Comuna' => $comuna,
            'Código Comercial' => $codigo_comercial,
            'Monto Venta' => $monto_venta,
            'Origen' => $origen,
            'Motivo Rechazo' => $motivo_rechazo,
            'Fecha Registro' => $fecha_registro,
            'Fecha Modificación' => $fecha_modificacion,
            'Usuario ID' => $usuario_id,
            'Empresa ID' => $empresa_id,
            'Estado' => $estado,
            'Ejecutivo' => $ejecutivo_nombre_usuario,
            'Total Documentos' => $total_documentos,
            'Documentos Obligatorios Completados' => $obligatorios_completados,
            'Total Documentos Obligatorios' => $total_obligatorios,
            'Porcentaje Completado' => $porcentaje_completado . '%'
        ];
    }
} else {
    // Si get_result está disponible, usar el método estándar
    $prospectos = [];
    while ($row = $result->fetch_assoc()) {
        $porcentaje_completado = $row['total_obligatorios'] > 0 ? 
            round(($row['obligatorios_completados'] / $row['total_obligatorios']) * 100) : 0;
        
        $prospectos[] = [
            'ID' => $row['id'],
            'Tipo de Persona' => $row['tipo_persona'],
            'RUT' => $row['rut_cliente'],
            'Razón Social' => $row['razon_social'],
            'Rubro' => $row['rubro'],
            'Email' => $row['email'],
            'Teléfono Celular' => $row['telefono_celular'],
            'Teléfono Fijo' => $row['telefono_fijo'],
            'Dirección' => $row['direccion'],
            'Ciudad' => $row['ciudad'],
            'Comuna' => $row['comuna'],
            'Código Comercial' => $row['codigo_comercial'],
            'Monto Venta' => $row['monto_venta'],
            'Origen' => $row['origen'],
            'Motivo Rechazo' => $row['motivo_rechazo'],
            'Fecha Registro' => $row['fecha_registro'],
            'Fecha Modificación' => $row['fecha_modificacion'],
            'Usuario ID' => $row['usuario_id'],
            'Empresa ID' => $row['empresa_id'],
            'Estado' => $row['estado'],
            'Ejecutivo' => $row['ejecutivo_nombre_usuario'],
            'Total Documentos' => $row['total_documentos'],
            'Documentos Obligatorios Completados' => $row['obligatorios_completados'],
            'Total Documentos Obligatorios' => $row['total_obligatorios'],
            'Porcentaje Completado' => $porcentaje_completado . '%'
        ];
    }
}

$stmt->close();

// Agregar logs para debugging
error_log("\n=== FASE FINAL: GENERACIÓN DEL CSV ===");
error_log("DEBUG: Procesando solicitud de exportación");
error_log("DEBUG: Número de prospectos encontrados: " . count($prospectos));

// Verificar si hay datos para exportar
if (empty($prospectos)) {
    error_log("ADVERTENCIA: No hay prospectos para exportar");
    
    if ($es_ajax) {
        header('HTTP/1.1 404 Not Found');
        echo "No hay datos para exportar con los filtros seleccionados";
        exit;
    } else {
        die("No hay datos para exportar con los filtros seleccionados");
    }
}

// Verificar si es una solicitud AJAX (ya definido anteriormente, solo log)
error_log("DEBUG: Es solicitud AJAX: " . ($es_ajax ? 'Sí' : 'No'));

// Nombre del archivo para la descarga
$fecha_actual = date('Y-m-d');
$nombre_archivo = "prospectos_inteletgroup_{$fecha_actual}.csv";
error_log("DEBUG: Nombre del archivo a generar: {$nombre_archivo}");

try {
    // Configurar encabezados para descarga de CSV
    error_log("Configurando cabeceras HTTP para la descarga CSV");
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $nombre_archivo . '"');

    // Agregar cabeceras adicionales para AJAX
    if ($es_ajax) {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST');
        header('Cache-Control: no-cache, must-revalidate');
        error_log("DEBUG: Agregadas cabeceras CORS y caché para AJAX");
    }
    
    // Log de todas las cabeceras enviadas
    $headers_list = headers_list();
    error_log("Cabeceras enviadas: " . print_r($headers_list, true));

    // Crear el recurso de salida para escribir el CSV
    error_log("Abriendo flujo de salida para CSV");
    $output = fopen('php://output', 'w');
    if (!$output) {
        throw new Exception("No se pudo abrir el flujo de salida");
    }

    // Configurar para que funcione con caracteres especiales (acentos, etc)
    error_log("Escribiendo BOM UTF-8");
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

    // Escribir encabezados de las columnas
    error_log("Escribiendo encabezados de columnas CSV");
    $encabezados = array_keys($prospectos[0]);
    error_log("Encabezados: " . implode(", ", $encabezados));
    fputcsv($output, $encabezados);
    
    // Escribir datos
    error_log("Escribiendo " . count($prospectos) . " filas de datos");
    $contador = 0;
    foreach ($prospectos as $prospecto) {
        fputcsv($output, $prospecto);
        $contador++;
        if ($contador % 100 == 0) {
            error_log("Procesadas $contador filas");
        }
    }
    error_log("Completado: $contador filas escritas en total");

    // Cerrar el flujo de salida
    error_log("Cerrando flujo de salida CSV");
    fclose($output);
    error_log("=== EXPORTACIÓN COMPLETADA EXITOSAMENTE ===");
    exit;
    
} catch (Exception $e) {
    error_log("ERROR FATAL en la generación del CSV: " . $e->getMessage());
    error_log("Traza: " . $e->getTraceAsString());
    
    if ($es_ajax) {
        header('HTTP/1.1 500 Internal Server Error');
        echo "Error al generar el archivo CSV: " . $e->getMessage();
    } else {
        die("Error al generar el archivo CSV: " . $e->getMessage());
    }
    exit;
}
?>