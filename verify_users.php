<?php
/**
 * Script para verificar usuarios existentes y crear usuarios de prueba
 */

require_once("con_db.php");

echo "<h2>Verificación de Usuarios y Proyectos</h2>";
echo "<p>Este script muestra los usuarios existentes y permite crear usuarios de prueba.</p>";

try {
    // Consultar todos los usuarios
    $sql = "SELECT id, correo, nombre_usuario, rol, proyecto, rut_ejecutivo FROM tb_experian_usuarios ORDER BY proyecto, correo";
    $result = $mysqli->query($sql);
    
    if (!$result) {
        throw new Exception("Error en la consulta: " . $mysqli->error);
    }
    
    echo "<h3>Usuarios Existentes:</h3>";
    
    if ($result->num_rows > 0) {
        echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>ID</th><th>Correo</th><th>Nombre Usuario</th><th>Rol</th><th>Proyecto</th><th>RUT Ejecutivo</th>";
        echo "</tr>";
        
        $count_experian = 0;
        $count_inteletgroup = 0;
        $count_otros = 0;
        
        while ($row = $result->fetch_assoc()) {
            $proyecto = $row['proyecto'] ?? 'NULL';
            
            $bgColor = '';
            if ($proyecto === 'experian') {
                $bgColor = '#e3f2fd'; // Azul claro
                $count_experian++;
            } elseif ($proyecto === 'inteletGroup') {
                $bgColor = '#e8f5e8'; // Verde claro
                $count_inteletgroup++;
            } else {
                $bgColor = '#fff3e0'; // Naranja claro
                $count_otros++;
            }
            
            echo "<tr style='background-color: $bgColor;'>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['correo']) . "</td>";
            echo "<td>" . htmlspecialchars($row['nombre_usuario'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($row['rol']) . "</td>";
            echo "<td><strong>" . htmlspecialchars($proyecto) . "</strong></td>";
            echo "<td>" . htmlspecialchars($row['rut_ejecutivo'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        echo "<h3>Resumen:</h3>";
        echo "<ul>";
        echo "<li><strong>Usuarios Experian:</strong> $count_experian</li>";
        echo "<li><strong>Usuarios InteletGroup:</strong> $count_inteletgroup</li>";
        echo "<li><strong>Otros/NULL:</strong> $count_otros</li>";
        echo "<li><strong>Total:</strong> " . ($count_experian + $count_inteletgroup + $count_otros) . "</li>";
        echo "</ul>";
        
    } else {
        echo "<p style='color: red;'><strong>No se encontraron usuarios en la base de datos.</strong></p>";
    }
    
    // Verificar si existe el usuario específico que se intentó usar
    echo "<h3>Verificación del Usuario de Prueba:</h3>";
    $test_rut = "13305541-K";
    $sql_check = "SELECT * FROM tb_experian_usuarios WHERE rut_ejecutivo = ? OR correo LIKE ?";
    $stmt = $mysqli->prepare($sql_check);
    $email_pattern = "13305541K%";
    $stmt->bind_param("ss", $test_rut, $email_pattern);
    $stmt->execute();
    $result_check = $stmt->get_result();
    
    if ($result_check->num_rows > 0) {
        echo "<p style='color: green;'>✅ Usuario con RUT $test_rut encontrado:</p>";
        while ($row = $result_check->fetch_assoc()) {
            echo "<pre>" . print_r($row, true) . "</pre>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ Usuario con RUT $test_rut NO encontrado en la base de datos.</p>";
    }
    
    // Formulario para crear usuarios de prueba
    echo "<hr>";
    echo "<h3>Crear Usuarios de Prueba:</h3>";
    
    if (isset($_POST['create_test_users'])) {
        // Crear usuarios de prueba
        $test_users = [
            [
                'correo' => '<EMAIL>',
                'clave' => password_hash('123456', PASSWORD_DEFAULT),
                'rol' => 'ejecutivo',
                'nombre_usuario' => 'Usuario Experian Test',
                'proyecto' => 'experian',
                'rut_ejecutivo' => '11111111-1'
            ],
            [
                'correo' => '<EMAIL>',
                'clave' => password_hash('123456', PASSWORD_DEFAULT),
                'rol' => 'ejecutivo',
                'nombre_usuario' => 'Usuario InteletGroup Test',
                'proyecto' => 'inteletGroup',
                'rut_ejecutivo' => '22222222-2'
            ]
        ];
        
        foreach ($test_users as $user) {
            $sql_insert = "INSERT INTO tb_experian_usuarios (correo, clave, rol, nombre_usuario, proyecto, rut_ejecutivo) VALUES (?, ?, ?, ?, ?, ?)";
            $stmt_insert = $mysqli->prepare($sql_insert);
            $stmt_insert->bind_param("ssssss", 
                $user['correo'], 
                $user['clave'], 
                $user['rol'], 
                $user['nombre_usuario'], 
                $user['proyecto'], 
                $user['rut_ejecutivo']
            );
            
            if ($stmt_insert->execute()) {
                echo "<p style='color: green;'>✅ Usuario creado: " . htmlspecialchars($user['correo']) . " (Proyecto: " . htmlspecialchars($user['proyecto']) . ")</p>";
            } else {
                echo "<p style='color: red;'>❌ Error creando usuario " . htmlspecialchars($user['correo']) . ": " . $stmt_insert->error . "</p>";
            }
        }
        
        echo "<p><strong>Credenciales de prueba:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Usuario Experian:</strong> <EMAIL> / 123456</li>";
        echo "<li><strong>Usuario InteletGroup:</strong> <EMAIL> / 123456</li>";
        echo "</ul>";
        
        echo "<p><a href='verify_users.php'>Recargar página para ver los nuevos usuarios</a></p>";
    } else {
        echo "<form method='POST'>";
        echo "<p>¿Deseas crear usuarios de prueba para ambos proyectos?</p>";
        echo "<button type='submit' name='create_test_users' style='background-color: #4CAF50; color: white; padding: 10px 20px; border: none; cursor: pointer;'>Crear Usuarios de Prueba</button>";
        echo "</form>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; background-color: #ffebee; padding: 15px; border-left: 4px solid #f44336;'>";
    echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
} finally {
    if (isset($mysqli)) {
        $mysqli->close();
    }
}

echo "<hr>";
echo "<p><em>Fecha de verificación: " . date('Y-m-d H:i:s') . "</em></p>";
?>
