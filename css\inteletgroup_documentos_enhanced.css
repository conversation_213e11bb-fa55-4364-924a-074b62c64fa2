:root {
            --primary-blue: #2699FB;
            --primary-dark: #1e3a8a;
            --primary-medium: #3b82f6;
            --primary-light: #60a5fa;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --secondary-color: #6b7280;
            --light-gray: #f3f4f6;
            --border-radius: 12px;
            --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        body {
            background-color: #f8fafc;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* Header mejorado */
        .page-header {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-medium) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: var(--box-shadow);
        }

        .page-header h1 {
            font-weight: 700;
            margin: 0;
        }

        /* Cards modernos */
        .modern-card {
    margin-bottom: 1.25rem;

            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            transition: all 0.3s ease;
            background: white;
            overflow: hidden;
        }

        .modern-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* Lista de prospectos mejorada */
        .prospect-card {
    margin-bottom: 0.75rem;

            cursor: pointer;
            border-left: 4px solid transparent;
            position: relative;
        }

        .prospect-card:hover {
            border-left-color: var(--primary-medium);
            background-color: var(--light-gray);
        }

        .prospect-card.selected {
            border-left-color: var(--primary-dark);
            background-color: #e0f2fe;
        }

        /* Progress bar personalizado */
        .progress-custom {
            height: 8px;
            background-color: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            position: relative;
            width: 100%;
        }

        .progress-bar-custom {
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        /* Colores tipo semáforo para las barras de progreso */
        .progress-bar-custom.red {
            background: linear-gradient(90deg, #dc2626, #ef4444);
        }

        .progress-bar-custom.yellow {
            background: linear-gradient(90deg, #f59e0b, #fbbf24);
        }

        .progress-bar-custom.green {
            background: linear-gradient(90deg, #10b981, #34d399);
        }

        /* Document grid */
        .document-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .document-card {
            border: 2px solid #e5e7eb;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            background: white;
        }

        .document-card:hover {
            border-color: var(--primary-medium);
            transform: translateY(-4px);
            box-shadow: var(--box-shadow);
        }

        .document-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .document-card.pdf .document-icon { color: #dc2626; }
        .document-card.doc .document-icon { color: #2563eb; }
        .document-card.image .document-icon { color: #10b981; }
        .document-card.excel .document-icon { color: #059669; }

        /* Estilo para el tipo de documento */
        .document-type-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--primary-dark);
            text-align: center;
            line-height: 1.3;
            margin-bottom: 0.75rem;
            min-height: 2.6rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .document-filename {
            font-size: 0.8rem;
            color: var(--secondary-color);
            text-align: center;
        }

        /* Estilos para tablas del checklist */
        .checklist-table {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .checklist-table th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            color: var(--primary-dark);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .checklist-table td {
            vertical-align: middle;
            border-bottom: 1px solid #f1f3f4;
        }

        .checklist-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .table-success {
            background-color: rgba(25, 135, 84, 0.1) !important;
        }

        .table-warning {
            background-color: rgba(255, 193, 7, 0.1) !important;
        }

        .status-icon {
            font-size: 1.25rem;
        }

        .file-name {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
        }

        /* Checkbox personalizado */
        .document-checkbox {
            position: absolute;
            top: 1rem;
            left: 1rem;
            width: 24px;
            height: 24px;
        }

        /* Botones de acción flotantes */
        .floating-actions {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            z-index: 1000;
        }

        .floating-btn {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        .floating-btn:hover {
            transform: scale(1.1);
        }

        /* Checklist visual */
        .checklist-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            background: #f9fafb;
            transition: all 0.3s ease;
        }

        .checklist-item.completed {
            background: #d1fae5;
        }

        .checklist-item.pending {
            background: #fee2e2;
        }

        .checklist-icon {
            font-size: 1.5rem;
            margin-right: 1rem;
        }

        .checklist-item.completed .checklist-icon {
            color: var(--success-color);
        }

        .checklist-item.pending .checklist-icon {
            color: var(--danger-color);
        }

        /* Stats cards */
        .stats-card {
            text-align: center;
            padding: 1.5rem;
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-dark);
        }

        .stats-label {
            color: var(--secondary-color);
            font-size: 0.875rem;

    .document-grid {
        grid-template-columns: 1fr;
    }
    
    .floating-actions {
        bottom: 1rem;
        right: 1rem;
    }
}
                right: 1rem;
            }
        }

        /* Loading animation */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: var(--primary-medium);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Empty state */
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
        }

        .empty-state-icon {
            font-size: 5rem;
            color: #e5e7eb;
            margin-bottom: 1rem;
        }

        .empty-state-text {
            color: var(--secondary-color);
            font-size: 1.125rem;
        }

        /* User info in header */
        .user-info-container {
            text-align: right;
            line-height: 1.3;
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius-sm);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .user-name {
            font-size: 1rem;
            font-weight: 600;
            color: white;
            margin: 0;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .user-role {
            font-size: 0.8rem;
            opacity: 0.9;
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
            font-weight: 500;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.75rem;
            font-size: 1rem;
            border-radius: 50%;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 44px;
            height: 44px;
            text-decoration: none;
            backdrop-filter: blur(10px);
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            transform: scale(1.05);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .logout-btn:active {
            transform: scale(0.95);
        }