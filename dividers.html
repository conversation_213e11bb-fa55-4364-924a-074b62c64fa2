<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Affan - PWA Mobile HTML Template">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>Affan - PWA Mobile HTML Template</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/favicon.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card -->
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">
      <!-- Header Content -->
      <div class="header-content position-relative d-flex align-items-center justify-content-between">
        <!-- Back Button -->
        <div class="back-button">
          <a href="elements.html">
            <i class="bi bi-arrow-left-short"></i>
          </a>
        </div>

        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0">Divider</h6>
        </div>

        <!-- Settings -->
        <div class="setting-wrapper">
          <div class="setting-trigger-btn" id="settingTriggerBtn">
            <i class="bi bi-gear"></i>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="page-content-wrapper py-3">

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading">
        <h6>Solid Divider</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <!-- Solid Divider -->
          <div class="divider mt-0"></div>

          <!-- Solid Primary Divider -->
          <div class="divider border-primary"></div>

          <!-- Solid Secondary Divider -->
          <div class="divider border-secondary"></div>

          <!-- Solid Success Divider -->
          <div class="divider border-success"></div>

          <!-- Solid Danger Divider -->
          <div class="divider border-danger"></div>

          <!-- Solid Warning Divider -->
          <div class="divider border-warning"></div>

          <!-- Solid Info Divider -->
          <div class="divider border-info"></div>

          <!-- Solid Light Divider -->
          <div class="divider border-light"></div>

          <!-- Solid Dark Divider -->
          <div class="divider border-dark mb-0"></div>

          <!-- Solid White Divider -->
          <!--.divider.border-white-->
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Dotted Divider</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <!-- Dotted Divider -->
          <div class="divider divider-dotted mt-0"></div>

          <!-- Dotted Primary Divider -->
          <div class="divider divider-dotted border-primary"></div>

          <!-- Dotted Secondary Divider -->
          <div class="divider divider-dotted border-secondary"></div>

          <!-- Dotted Success Divider -->
          <div class="divider divider-dotted border-success"></div>

          <!-- Dotted Danger Divider -->
          <div class="divider divider-dotted border-danger"></div>

          <!-- Dotted Warning Divider -->
          <div class="divider divider-dotted border-warning"></div>

          <!-- Dotted Info Divider -->
          <div class="divider divider-dotted border-info"></div>

          <!-- Dotted Light Divider -->
          <div class="divider divider-dotted border-light"></div>

          <!-- Dotted Dark Divider -->
          <div class="divider divider-dotted border-dark mb-0"></div>

          <!-- Dotted White Divider -->
          <!--.divider.divider-dotted.border-white-->
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Dashed Divider</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <!-- Dashed Divider -->
          <div class="divider divider-dashed mt-0"></div>

          <!-- Dashed Primary Divider -->
          <div class="divider divider-dashed border-primary"> </div>

          <!-- Dashed Secondary Divider -->
          <div class="divider divider-dashed border-secondary"></div>

          <!-- Dashed Success Divider -->
          <div class="divider divider-dashed border-success"></div>

          <!-- Dashed Danger Divider -->
          <div class="divider divider-dashed border-danger"></div>

          <!-- Dashed Warning Divider -->
          <div class="divider divider-dashed border-warning"></div>

          <!-- Dashed Info Divider -->
          <div class="divider divider-dashed border-info"></div>

          <!-- Dashed Light Divider -->
          <div class="divider divider-dashed border-light"></div>

          <!-- Dashed Dark Divider -->
          <div class="divider divider-dashed border-dark mb-0"></div>

          <!-- Dashed White Divider -->
          <!--.divider.divider-dashed.border-white-->
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Center Icon Divider</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <!-- Center Icon Divider -->
          <div class="divider divider-center-icon mt-3">
            <i class="bi bi-gear"></i>
          </div>

          <div class="mb-5"></div>

          <!-- Center Icon Divider -->
          <div class="divider divider-center-icon border-primary">
            <i class="bi bi-arrow-down-circle"></i>
          </div>

          <div class="mb-5"></div>

          <!-- Center Icon Divider -->
          <div class="divider divider-center-icon border-secondary">
            <i class="bi bi-arrow-up-circle"></i>
          </div>

          <div class="mb-5"></div>

          <!-- Center Icon Divider -->
          <div class="divider divider-center-icon border-success">
            <i class="bi bi-arrow-up"></i>
          </div>

          <div class="mb-5"></div>

          <!-- Center Icon Divider -->
          <div class="divider divider-center-icon border-danger">
            <i class="bi bi-arrow-down"></i>
          </div>

          <div class="mb-5"></div>

          <!-- Center Icon Divider -->
          <div class="divider divider-center-icon border-warning">
            <i class="bi bi-braces"></i>
          </div>

          <div class="mb-5"></div>

          <!-- Center Icon Divider -->
          <div class="divider divider-center-icon border-info">
            <i class="bi bi-currency-dollar"></i>
          </div>

          <div class="mb-5"></div>

          <!-- Center Icon Divider -->
          <div class="divider divider-center-icon border-light">
            <i class="bi bi-flower1"></i>
          </div>

          <div class="mb-5"></div>

          <!-- Center Icon Divider -->
          <div class="divider divider-center-icon border-dark mb-3">
            <i class="bi bi-gift"></i>
          </div>

          <!--.mb-5-->

          <!-- Center Icon Divider -->
          <!--.divider.divider-center-icon.border-white-->
          <!--  i.bi.bi-gear-->
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          <li class="active">
            <a href="home.html">
              <i class="bi bi-house"></i>
              <span>Home</span>
            </a>
          </li>

          <li>
            <a href="pages.html">
              <i class="bi bi-collection"></i>
              <span>Pages</span>
            </a>
          </li>

          <li>
            <a href="elements.html">
              <i class="bi bi-folder2-open"></i>
              <span>Elements</span>
            </a>
          </li>

          <li>
            <a href="chat-users.html">
              <i class="bi bi-chat-dots"></i>
              <span>Chat</span>
            </a>
          </li>

          <li>
            <a href="settings.html">
              <i class="bi bi-gear"></i>
              <span>Settings</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
</body>

</html>