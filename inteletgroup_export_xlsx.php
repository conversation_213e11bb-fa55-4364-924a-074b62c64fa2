<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Aumentar límites para evitar timeouts
ini_set('max_execution_time', 300);
ini_set('memory_limit', '256M');

// Log para debugging
error_log("=== INTELETGROUP_EXPORT_XLSX.PHP INICIADO ===");

// Iniciar sesión
session_start();

// Verificar autenticación y permisos de administrador
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    $errorMsg = urlencode("Usuario no autenticado o sin permisos para InteletGroup.");
    header('Location: login.php?error=' . $errorMsg);
    exit;
}

// Prevenir caché del navegador
header("Cache-Control: no-cache, no-store, must-revalidate");
header("Pragma: no-cache");
header("Expires: 0");

// Incluir archivos necesarios
require_once 'cache_utils.php';
require_once 'con_db.php';

// Aplicar headers anti-caché
no_cache_headers();

// Verificar conexión a base de datos
if (!isset($mysqli) || $mysqli->connect_error) {
    error_log("ERROR: Error de conexión MySQL: " . ($mysqli->connect_error ?? 'mysqli no definido'));
    die("Error de conexión a la base de datos");
}

error_log("DEBUG: Conexión a BD establecida correctamente");

// Obtener parámetros de filtros (desde POST o GET)
$filtro_ejecutivo = $_POST['ejecutivo'] ?? $_GET['ejecutivo'] ?? 'todos';
$filtro_periodo = $_POST['periodo'] ?? $_GET['periodo'] ?? 'año';
$filtro_fecha_inicio = $_POST['fecha_inicio'] ?? $_GET['fecha_inicio'] ?? date('Y-01-01');
$filtro_fecha_fin = $_POST['fecha_fin'] ?? $_GET['fecha_fin'] ?? date('Y-12-31');

// Calcular fechas según el periodo seleccionado
switch($filtro_periodo) {
    case 'hoy':
        $filtro_fecha_inicio = date('Y-m-d');
        $filtro_fecha_fin = date('Y-m-d');
        break;
    case 'semana':
        $filtro_fecha_inicio = date('Y-m-d', strtotime('monday this week'));
        $filtro_fecha_fin = date('Y-m-d', strtotime('sunday this week'));
        break;
    case 'mes_actual':
        $filtro_fecha_inicio = date('Y-m-01');
        $filtro_fecha_fin = date('Y-m-t');
        break;
    case 'trimestre':
        $trimestre = ceil(date('n') / 3);
        $filtro_fecha_inicio = date('Y-') . sprintf('%02d', ($trimestre - 1) * 3 + 1) . '-01';
        $filtro_fecha_fin = date('Y-m-t', strtotime($filtro_fecha_inicio . ' +2 months'));
        break;
    case 'año':
        $filtro_fecha_inicio = date('Y-01-01');
        $filtro_fecha_fin = date('Y-12-31');
        break;
}

// Construir condición WHERE para filtros
$where_conditions = ["1=1"];
$params = [];
$types = "";

if ($filtro_ejecutivo !== 'todos') {
    $where_conditions[] = "p.usuario_id = ?";
    $params[] = $filtro_ejecutivo;
    $types .= "i";
}

$where_conditions[] = "DATE(p.fecha_registro) BETWEEN ? AND ?";
$params[] = $filtro_fecha_inicio;
$params[] = $filtro_fecha_fin;
$types .= "ss";

$where_clause = implode(" AND ", $where_conditions);

error_log("INFO: Exportando prospectos XLSX - Ejecutivo: $filtro_ejecutivo, Periodo: $filtro_periodo");
error_log("INFO: Fechas - Inicio: $filtro_fecha_inicio, Fin: $filtro_fecha_fin");

// Consulta optimizada para obtener solo las columnas que se muestran en el dashboard
$query = "
    SELECT
        p.id,
        p.razon_social,
        p.rut_cliente,
        p.tipo_persona,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        p.fecha_registro
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE (u.proyecto = 'inteletGroup' OR u.proyecto IS NULL) AND " . $where_clause . "
    ORDER BY p.fecha_registro DESC";

error_log("DEBUG: Ejecutando consulta: $query");

$stmt = $mysqli->prepare($query);
if (!$stmt) {
    error_log("ERROR: Error preparando consulta: " . $mysqli->error);
    die("Error preparando consulta: " . $mysqli->error);
}

if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}

if (!$stmt->execute()) {
    error_log("ERROR: Error ejecutando consulta: " . $stmt->error);
    die("Error ejecutando consulta: " . $stmt->error);
}

// Obtener resultados usando bind_result para compatibilidad con PHP 7.3
$id = $razon_social = $rut_cliente = $tipo_persona = $ejecutivo_nombre_usuario = $fecha_registro = null;
$stmt->bind_result($id, $razon_social, $rut_cliente, $tipo_persona, $ejecutivo_nombre_usuario, $fecha_registro);

$prospectos = [];
while ($stmt->fetch()) {
    // Obtener datos de documentos para cada prospecto
    $doc_query = "SELECT 
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p2
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p2.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p2.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE p2.id = ?";
    
    $doc_stmt = $mysqli->prepare($doc_query);
    if ($doc_stmt) {
        $doc_stmt->bind_param("i", $id);
        $doc_stmt->execute();
        $doc_stmt->bind_result($total_documentos, $obligatorios_completados, $total_obligatorios);
        $doc_stmt->fetch();
        $doc_stmt->close();
    } else {
        $total_documentos = $obligatorios_completados = $total_obligatorios = 0;
    }
    
    $porcentaje_completado = $total_obligatorios > 0 ? round(($obligatorios_completados / $total_obligatorios) * 100) : 0;
    
    $prospectos[] = [
        'ID' => $id,
        'Razón Social' => $razon_social,
        'RUT' => $rut_cliente,
        'Tipo' => $tipo_persona,
        'Ejecutivo' => $ejecutivo_nombre_usuario,
        'Documentos' => $obligatorios_completados . '/' . $total_obligatorios . ' (Total: ' . $total_documentos . ')',
        'Completitud' => $porcentaje_completado . '%',
        'Fecha Registro' => date('d/m/Y', strtotime($fecha_registro))
    ];
}

$stmt->close();

error_log("DEBUG: Número de prospectos encontrados: " . count($prospectos));

// Verificar si hay datos para exportar
if (empty($prospectos)) {
    error_log("ADVERTENCIA: No hay prospectos para exportar");
    die("No hay datos para exportar con los filtros seleccionados");
}

// Generar nombre de archivo descriptivo
$fecha_actual = date('Y-m-d_H-i-s');
$periodo_texto = $filtro_periodo;
$ejecutivo_texto = $filtro_ejecutivo === 'todos' ? 'todos' : 'ejecutivo_' . $filtro_ejecutivo;
$nombre_archivo = "prospectos_inteletgroup_{$periodo_texto}_{$ejecutivo_texto}_{$fecha_actual}.xlsx";

error_log("DEBUG: Generando archivo XLSX: $nombre_archivo");

// Función para generar XLSX usando solo PHP nativo (sin ZipArchive)
function generarXLSX($datos, $nombre_archivo) {
    // Verificar si ZipArchive está disponible
    if (!class_exists('ZipArchive')) {
        // Fallback: generar CSV con extensión .xlsx para compatibilidad
        error_log("WARNING: ZipArchive no disponible, generando CSV con extensión .xlsx");
        generarCSVComoXLSX($datos, $nombre_archivo);
        return;
    }

    // Configurar headers para descarga XLSX
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment; filename="' . $nombre_archivo . '"');
    header('Cache-Control: max-age=0');

    try {
        // Crear estructura XLSX básica usando ZIP
        $zip = new ZipArchive();
        $temp_file = tempnam(sys_get_temp_dir(), 'xlsx_');

        if ($zip->open($temp_file, ZipArchive::CREATE) !== TRUE) {
            throw new Exception("No se pudo crear el archivo XLSX");
        }

        // Agregar archivos necesarios para XLSX
        agregarArchivosXLSX($zip, $datos);

        $zip->close();

        // Enviar archivo al navegador
        readfile($temp_file);
        unlink($temp_file);
    } catch (Exception $e) {
        error_log("ERROR en generarXLSX: " . $e->getMessage());
        // Fallback a CSV
        generarCSVComoXLSX($datos, $nombre_archivo);
    }
}

// Función fallback para generar CSV con formato Excel
function generarCSVComoXLSX($datos, $nombre_archivo) {
    // Cambiar extensión a .csv para ser honesto sobre el formato
    $nombre_csv = str_replace('.xlsx', '.csv', $nombre_archivo);

    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $nombre_csv . '"');
    header('Cache-Control: max-age=0');

    $output = fopen('php://output', 'w');

    // BOM para UTF-8
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

    if (!empty($datos)) {
        // Escribir encabezados
        fputcsv($output, array_keys($datos[0]));

        // Escribir datos
        foreach ($datos as $fila) {
            fputcsv($output, $fila);
        }
    }

    fclose($output);
}

// Función para agregar archivos necesarios al ZIP XLSX
function agregarArchivosXLSX($zip, $datos) {
    // [Content-Types].xml
    $content_types = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
<Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
<Default Extension="xml" ContentType="application/xml"/>
<Override PartName="/xl/workbook.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml"/>
<Override PartName="/xl/worksheets/sheet1.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml"/>
</Types>';
    $zip->addFromString('[Content_Types].xml', $content_types);
    
    // _rels/.rels
    $rels = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
<Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="xl/workbook.xml"/>
</Relationships>';
    $zip->addFromString('_rels/.rels', $rels);
    
    // xl/_rels/workbook.xml.rels
    $workbook_rels = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
<Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet" Target="worksheets/sheet1.xml"/>
</Relationships>';
    $zip->addFromString('xl/_rels/workbook.xml.rels', $workbook_rels);
    
    // xl/workbook.xml
    $workbook = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships">
<sheets>
<sheet name="Prospectos InteletGroup" sheetId="1" r:id="rId1"/>
</sheets>
</workbook>';
    $zip->addFromString('xl/workbook.xml', $workbook);
    
    // xl/worksheets/sheet1.xml (aquí van los datos)
    $worksheet = generarWorksheet($datos);
    $zip->addFromString('xl/worksheets/sheet1.xml', $worksheet);
}

// Función para generar el worksheet con los datos
function generarWorksheet($datos) {
    $xml = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main">
<sheetData>';
    
    if (!empty($datos)) {
        // Encabezados (fila 1)
        $xml .= '<row r="1">';
        $col = 1;
        foreach (array_keys($datos[0]) as $header) {
            $xml .= '<c r="' . columnLetter($col) . '1" t="inlineStr"><is><t>' . htmlspecialchars($header) . '</t></is></c>';
            $col++;
        }
        $xml .= '</row>';
        
        // Datos (filas 2 en adelante)
        $row = 2;
        foreach ($datos as $fila) {
            $xml .= '<row r="' . $row . '">';
            $col = 1;
            foreach ($fila as $valor) {
                $xml .= '<c r="' . columnLetter($col) . $row . '" t="inlineStr"><is><t>' . htmlspecialchars($valor) . '</t></is></c>';
                $col++;
            }
            $xml .= '</row>';
            $row++;
        }
    }
    
    $xml .= '</sheetData>
</worksheet>';
    
    return $xml;
}

// Función para convertir número de columna a letra (A, B, C, etc.)
function columnLetter($col) {
    $letter = '';
    while ($col > 0) {
        $col--;
        $letter = chr(65 + ($col % 26)) . $letter;
        $col = intval($col / 26);
    }
    return $letter;
}

try {
    // Generar y enviar archivo XLSX
    generarXLSX($prospectos, $nombre_archivo);
    error_log("=== EXPORTACIÓN XLSX COMPLETADA EXITOSAMENTE ===");
    exit;
    
} catch (Exception $e) {
    error_log("ERROR FATAL en la generación del XLSX: " . $e->getMessage());
    die("Error al generar el archivo XLSX: " . $e->getMessage());
}
?>
