<?php
// Iniciar sesión
session_start();

// Simular un usuario autenticado
if (!isset($_SESSION['usuario_id'])) {
    $_SESSION['usuario_id'] = 1; // ID de usuario para pruebas
    $_SESSION['usuario'] = '<EMAIL>';
}

// Mostrar formulario de prueba
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba de Bitácora</title>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input, select, textarea { width: 100%; padding: 8px; box-sizing: border-box; }
        button { padding: 10px 15px; background: #4CAF50; color: white; border: none; cursor: pointer; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .loading-data { text-align: center; padding: 20px; }
        .error-data { color: red; text-align: center; padding: 20px; }
        .no-data { text-align: center; padding: 20px; color: #666; }
        .result { margin-top: 20px; border: 1px solid #ddd; padding: 15px; background: #f9f9f9; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Prueba de Bitácora</h1>
        
        <div class="form-group">
            <label for="rut">RUT Ejecutivo:</label>
            <input type="text" id="rut" name="rut" value="78.240.770-8">
        </div>
        
        <button id="cargarBitacora">Cargar Bitácora</button>
        
        <div class="result">
            <h3>Resultado:</h3>
            <pre id="resultado">Esperando consulta...</pre>
        </div>
        
        <h3>Registros de Bitácora:</h3>
        <table id="bitacora-table">
            <thead>
                <tr>
                    <th>Fecha</th>
                    <th>Estado</th>
                    <th>Observaciones</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td colspan="3" class="no-data">No hay datos disponibles</td>
                </tr>
            </tbody>
        </table>
        
        <h2>Agregar Registro de Bitácora</h2>
        <form id="formBitacora">
            <div class="form-group">
                <label for="bitacora_rut">RUT Ejecutivo:</label>
                <input type="text" id="bitacora_rut" name="rut_ejecutivo" value="78.240.770-8">
            </div>
            
            <div class="form-group">
                <label for="estado">Estado:</label>
                <select id="estado" name="estado">
                    <option value="Pendiente">Pendiente</option>
                    <option value="En proceso">En proceso</option>
                    <option value="Completado">Completado</option>
                    <option value="Cancelado">Cancelado</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="observaciones">Observaciones:</label>
                <textarea id="observaciones" name="observaciones" rows="4"></textarea>
            </div>
            
            <button type="submit">Guardar Registro</button>
        </form>
    </div>
    
    <script>
        $(document).ready(function() {
            // Cargar bitácora
            $('#cargarBitacora').click(function() {
                var rut = $('#rut').val();
                if (!rut) {
                    alert('Por favor ingrese un RUT');
                    return;
                }
                
                console.log('Intentando cargar bitácora para RUT:', rut);
                
                // Mostrar indicador de carga
                $('#bitacora-table tbody').html('<tr><td colspan="3" class="loading-data">Cargando registros...</td></tr>');
                $('#resultado').text('Consultando...');
                
                // Construir la URL completa para depuración
                var bitacoraUrl = 'endpoints/obtener_bitacora.php?rut=' + encodeURIComponent(rut);
                console.log('URL de la bitácora:', bitacoraUrl);
                
                $.ajax({
                    url: bitacoraUrl,
                    type: 'GET',
                    dataType: 'json',
                    cache: false,
                    beforeSend: function(xhr) {
                        console.log('Enviando solicitud AJAX...');
                    },
                    success: function(response) {
                        console.log('Respuesta recibida:', response);
                        $('#resultado').text(JSON.stringify(response, null, 2));
                        
                        if (response && response.success) {
                            if (response.data.length === 0) {
                                $('#bitacora-table tbody').html('<tr><td colspan="3" class="no-data">No hay registros disponibles</td></tr>');
                                console.log('No hay registros disponibles');
                            } else {
                                let html = '';
                                $.each(response.data, function(index, registro) {
                                    html += '<tr>';
                                    html += '<td>' + registro.fecha_registro + '</td>';
                                    html += '<td>' + registro.estado + '</td>';
                                    html += '<td>' + registro.observaciones + '</td>';
                                    html += '</tr>';
                                });
                                $('#bitacora-table tbody').html(html);
                                console.log('Registros cargados correctamente:', response.data.length);
                            }
                        } else {
                            $('#bitacora-table tbody').html('<tr><td colspan="3" class="error-data">Error: ' + (response ? response.message : 'Respuesta inválida') + '</td></tr>');
                            console.error('Error al cargar bitácora:', response ? response.message : 'Respuesta inválida');
                        }
                    },
                    error: function(xhr, status, error) {
                        $('#bitacora-table tbody').html('<tr><td colspan="3" class="error-data">Error de conexión: ' + status + '</td></tr>');
                        $('#resultado').text('Error: ' + status + '\n\nRespuesta: ' + xhr.responseText);
                        
                        console.error('Error AJAX al cargar bitácora:', {
                            status: status,
                            error: error,
                            response: xhr.responseText,
                            url: bitacoraUrl
                        });
                        
                        // Intentar analizar la respuesta para obtener más información
                        try {
                            if (xhr.responseText) {
                                var errorResponse = JSON.parse(xhr.responseText);
                                console.error('Detalles del error:', errorResponse);
                            }
                        } catch (e) {
                            console.error('No se pudo analizar la respuesta de error:', xhr.responseText);
                        }
                    }
                });
            });
            
            // Guardar registro de bitácora
            $('#formBitacora').submit(function(e) {
                e.preventDefault();
                
                var rut = $('#bitacora_rut').val();
                var estado = $('#estado').val();
                var observaciones = $('#observaciones').val();
                
                if (!rut || !estado || !observaciones) {
                    alert('Todos los campos son obligatorios');
                    return;
                }
                
                console.log('Intentando guardar registro de bitácora');
                
                $.ajax({
                    url: 'endpoints/guardar_bitacora.php',
                    type: 'POST',
                    data: {
                        rut_ejecutivo: rut,
                        estado: estado,
                        observaciones: observaciones
                    },
                    dataType: 'json',
                    success: function(response) {
                        console.log('Respuesta recibida:', response);
                        
                        if (response && response.success) {
                            alert('Registro guardado correctamente');
                            $('#observaciones').val('');
                            
                            // Recargar la bitácora
                            $('#cargarBitacora').click();
                        } else {
                            alert('Error: ' + (response ? response.message : 'Respuesta inválida'));
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error AJAX al guardar bitácora:', {
                            status: status,
                            error: error,
                            response: xhr.responseText
                        });
                        
                        alert('Error al guardar el registro: ' + status);
                    }
                });
            });
        });
    </script>
</body>
</html>
