# Recomendaciones de Seguridad - Sistema de Autenticación
## Portal Gestar - InteletGroup/Experian

**<PERSON>cha de Análisis:** 2025-01-04  
**Estado:** Pendiente de Implementación  
**Prioridad:** CRÍTICA

---

## 🚨 VULNERABILIDADES CRÍTICAS (Implementación Inmediata)

### 1. Almacenamiento de Contraseñas en Texto Plano

**Problema:** Las contraseñas se almacenan sin cifrar en la base de datos.

**Riesgo:** Exposición completa de credenciales si la base de datos es comprometida.

**Solución:**
```php
// 1. Migrar contraseñas existentes
// Script de migración (ejecutar una sola vez)
$usuarios = $mysqli->query("SELECT id, clave FROM tb_experian_usuarios");
while ($usuario = $usuarios->fetch_assoc()) {
    $hash = password_hash($usuario['clave'], PASSWORD_DEFAULT);
    $stmt = $mysqli->prepare("UPDATE tb_experian_usuarios SET clave = ? WHERE id = ?");
    $stmt->bind_param("si", $hash, $usuario['id']);
    $stmt->execute();
}

// 2. Actualizar verificación en ControllerGestar.php
if (password_verify($clave, $db_clave)) {
    // Autenticación exitosa
} else {
    // Contraseña incorrecta
}
```

**Tiempo estimado:** 2-3 horas  
**Impacto:** Alto - Mejora significativa de seguridad

---

### 2. Implementar Rate Limiting y Bloqueo de Cuentas

**Problema:** No hay protección contra ataques de fuerza bruta.

**Riesgo:** Ataques automatizados pueden comprometer cuentas.

**Solución:**
```php
// Crear tabla para tracking de intentos
CREATE TABLE tb_login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    email_or_rut VARCHAR(255) NOT NULL,
    attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    success BOOLEAN DEFAULT FALSE,
    INDEX idx_ip_time (ip_address, attempt_time),
    INDEX idx_email_time (email_or_rut, attempt_time)
);

// Implementar en ControllerGestar.php
function checkRateLimit($ip, $email_or_rut) {
    global $mysqli;
    
    // Verificar intentos por IP (máximo 10 en 15 minutos)
    $stmt = $mysqli->prepare("
        SELECT COUNT(*) as attempts 
        FROM tb_login_attempts 
        WHERE ip_address = ? 
        AND attempt_time > DATE_SUB(NOW(), INTERVAL 15 MINUTE)
        AND success = FALSE
    ");
    $stmt->bind_param("s", $ip);
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();
    
    if ($result['attempts'] >= 10) {
        throw new Exception("Demasiados intentos fallidos. Intente en 15 minutos.");
    }
    
    // Verificar intentos por usuario (máximo 5 en 15 minutos)
    $stmt = $mysqli->prepare("
        SELECT COUNT(*) as attempts 
        FROM tb_login_attempts 
        WHERE email_or_rut = ? 
        AND attempt_time > DATE_SUB(NOW(), INTERVAL 15 MINUTE)
        AND success = FALSE
    ");
    $stmt->bind_param("s", $email_or_rut);
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();
    
    if ($result['attempts'] >= 5) {
        throw new Exception("Cuenta temporalmente bloqueada. Intente en 15 minutos.");
    }
}

function logLoginAttempt($ip, $email_or_rut, $success) {
    global $mysqli;
    $stmt = $mysqli->prepare("
        INSERT INTO tb_login_attempts (ip_address, email_or_rut, success) 
        VALUES (?, ?, ?)
    ");
    $stmt->bind_param("ssi", $ip, $email_or_rut, $success);
    $stmt->execute();
}
```

**Tiempo estimado:** 4-6 horas  
**Impacto:** Alto - Previene ataques de fuerza bruta

---

### 3. Protección CSRF

**Problema:** No hay tokens CSRF en formularios.

**Riesgo:** Ataques de falsificación de solicitudes entre sitios.

**Solución:**
```php
// En session_check.php o archivo de utilidades
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// En formularios HTML
echo '<input type="hidden" name="csrf_token" value="' . generateCSRFToken() . '">';

// En ControllerGestar.php
if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
    throw new Exception("Token CSRF inválido");
}
```

**Tiempo estimado:** 2-3 horas  
**Impacto:** Medio-Alto - Previene ataques CSRF

---

## ⚠️ MEJORAS DE SEGURIDAD MEDIA PRIORIDAD

### 4. Validación de Entrada Mejorada

**Problema:** Validación limitada de datos de entrada.

**Solución:**
```php
function validateAndSanitizeInput($input, $type) {
    $input = trim($input);
    
    switch ($type) {
        case 'email':
            if (!filter_var($input, FILTER_VALIDATE_EMAIL)) {
                throw new Exception("Formato de email inválido");
            }
            return filter_var($input, FILTER_SANITIZE_EMAIL);
            
        case 'rut':
            // Limpiar RUT y validar formato
            $rut = preg_replace('/[^0-9kK\-]/', '', $input);
            if (!preg_match('/^\d{7,8}-[\dkK]$/', $rut)) {
                throw new Exception("Formato de RUT inválido");
            }
            return $rut;
            
        case 'password':
            if (strlen($input) < 8) {
                throw new Exception("La contraseña debe tener al menos 8 caracteres");
            }
            return $input;
    }
}
```

**Tiempo estimado:** 3-4 horas

---

### 5. Configuración de Sesión Segura

**Problema:** Configuración de sesión básica.

**Solución:**
```php
// En session_check.php
ini_set('session.cookie_secure', 1);        // Solo HTTPS
ini_set('session.cookie_httponly', 1);      // No JavaScript
ini_set('session.cookie_samesite', 'Strict'); // Anti-CSRF
ini_set('session.use_strict_mode', 1);       // Modo estricto
ini_set('session.gc_maxlifetime', 28800);    // 8 horas
ini_set('session.cookie_lifetime', 0);       // Solo durante sesión
```

**Tiempo estimado:** 1-2 horas

---

### 6. Logging de Seguridad Mejorado

**Problema:** Logs básicos sin análisis de patrones.

**Solución:**
```php
function logSecurityEvent($event_type, $details, $severity = 'INFO') {
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'event_type' => $event_type,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'session_id' => session_id(),
        'details' => $details,
        'severity' => $severity
    ];
    
    $log_file = __DIR__ . '/logs/security_' . date('Y-m-d') . '.log';
    file_put_contents($log_file, json_encode($log_entry) . PHP_EOL, FILE_APPEND | LOCK_EX);
    
    // Alertas para eventos críticos
    if ($severity === 'CRITICAL') {
        // Enviar notificación por email o sistema de alertas
        error_log("SECURITY ALERT: " . json_encode($log_entry));
    }
}
```

**Tiempo estimado:** 2-3 horas

---

## 🔒 MEJORAS AVANZADAS (Implementación a Largo Plazo)

### 7. Autenticación de Dos Factores (2FA)

**Beneficio:** Capa adicional de seguridad.

**Implementación:**
- Integración con Google Authenticator o similar
- SMS como respaldo
- Códigos de recuperación

**Tiempo estimado:** 1-2 semanas

---

### 8. Monitoreo de Seguridad en Tiempo Real

**Beneficio:** Detección temprana de amenazas.

**Implementación:**
- Dashboard de seguridad
- Alertas automáticas
- Análisis de patrones de acceso

**Tiempo estimado:** 2-3 semanas

---

### 9. Auditoría de Acceso

**Beneficio:** Trazabilidad completa de acciones.

**Implementación:**
- Log de todas las acciones de usuario
- Reportes de auditoría
- Retención de logs por tiempo definido

**Tiempo estimado:** 1-2 semanas

---

## 📋 PLAN DE IMPLEMENTACIÓN RECOMENDADO

### Fase 1 (Inmediata - 1-2 semanas)
1. ✅ **Autenticación por RUT** (Completado)
2. 🔴 **Cifrado de contraseñas**
3. 🔴 **Rate limiting básico**
4. 🔴 **Protección CSRF**

### Fase 2 (Corto plazo - 2-4 semanas)
1. 🟡 **Validación de entrada mejorada**
2. 🟡 **Configuración de sesión segura**
3. 🟡 **Logging de seguridad mejorado**

### Fase 3 (Mediano plazo - 1-3 meses)
1. 🟢 **Autenticación de dos factores**
2. 🟢 **Monitoreo en tiempo real**
3. 🟢 **Auditoría completa**

---

## 🛠️ HERRAMIENTAS Y RECURSOS

### Librerías Recomendadas
- **PHPMailer** - Para notificaciones por email
- **Google Authenticator PHP** - Para 2FA
- **Monolog** - Para logging avanzado

### Configuración del Servidor
- **Certificado SSL válido** (Let's Encrypt)
- **Headers de seguridad HTTP**
- **Firewall configurado**

---

## 📞 CONTACTO Y SOPORTE

Para implementación de estas recomendaciones:
- Revisar cada punto antes de implementar
- Realizar pruebas en ambiente de desarrollo
- Hacer backup de la base de datos antes de cambios
- Documentar todos los cambios realizados

---

**Nota:** Este documento debe actualizarse conforme se implementen las mejoras y se identifiquen nuevas vulnerabilidades.
