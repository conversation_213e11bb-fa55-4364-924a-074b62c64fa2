<?php
// Test simple de conexión
header('Content-Type: text/plain');

echo "=== TEST DE CONEXIÓN ===\n";

try {
    // Configuración directa
    $host = '*************';
    $port = 3306;
    $dbname = 'gestarse_experian';
    $username = 'gestarse_ncornejo7_experian';
    $password = 'N1c0l7as17';
    
    echo "Intentando conectar a: $host:$port\n";
    echo "Base de datos: $dbname\n";
    echo "Usuario: $username\n\n";
    
    // Crear conexión
    $mysqli = new mysqli($host, $username, $password, $dbname, $port);
    
    if ($mysqli->connect_error) {
        throw new Exception("Connection failed: " . $mysqli->connect_error);
    }
    
    echo "✅ CONEXIÓN EXITOSA\n";
    echo "Versión MySQL: " . $mysqli->server_info . "\n";
    
    // Test simple query
    $result = $mysqli->query("SELECT COUNT(*) as total FROM tb_inteletgroup_prospectos");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "Total prospectos: " . $row['total'] . "\n";
    }
    
    $mysqli->close();
    echo "\n✅ TEST COMPLETADO EXITOSAMENTE\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}
?>
