<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Iniciar sesión
session_start();

// Modo debug (si se pasa el parámetro debug=1)
$debug_mode = isset($_GET['debug']) && $_GET['debug'] == '1';

// Crear directorio de logs si no existe
$log_dir = __DIR__ . '/logs';
if (!file_exists($log_dir)) {
    mkdir($log_dir, 0777, true);
}

// Archivo de log para esta exportación
$log_file = $log_dir . '/export_' . date('Y-m-d_H-i-s') . '.log';

// Función para registrar logs
function export_log($message, $type = 'INFO') {
    global $log_file, $debug_mode;
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[$timestamp] [$type] $message" . PHP_EOL;
    file_put_contents($log_file, $log_message, FILE_APPEND);
    
    if ($debug_mode) {
        echo "<div style='margin: 5px; padding: 5px; border: 1px solid " . 
            ($type == 'ERROR' ? 'red' : ($type == 'WARNING' ? 'orange' : 'green')) . 
            ";'><strong>$type:</strong> $message</div>";
    }
    
    if ($type == 'ERROR') {
        error_log($message);
    }
}

export_log("Iniciando proceso de exportación");

// Verificar autenticación
if (!isset($_SESSION['usuario_id'])) {
    export_log("Usuario no autenticado", "ERROR");
    if (!$debug_mode) {
        echo "Error: Usuario no autenticado. Por favor inicie sesión.";
        exit;
    }
}

if (!isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    export_log("Usuario sin permisos para InteletGroup", "ERROR");
    if (!$debug_mode) {
        echo "Error: Sin permisos para acceder a InteletGroup.";
        exit;
    }
}

export_log("Usuario autenticado: " . ($_SESSION['usuario_id'] ?? 'No disponible en modo debug'));

// Incluir conexión a base de datos
require_once 'con_db.php';

// Verificar conexión
if (!isset($mysqli) || $mysqli->connect_error) {
    $error_msg = "Error de conexión a la base de datos: " . ($mysqli->connect_error ?? "Variable de conexión no disponible");
    export_log($error_msg, "ERROR");
    if (!$debug_mode) {
        echo "Error: " . $error_msg;
        exit;
    }
}

export_log("Conexión a base de datos establecida");

// Obtener parámetros de filtros
$filtro_ejecutivo = isset($_GET['ejecutivo']) ? $_GET['ejecutivo'] : 'todos';
$filtro_periodo = isset($_GET['periodo']) ? $_GET['periodo'] : 'año';
$filtro_fecha_inicio = isset($_GET['fecha_inicio']) ? $_GET['fecha_inicio'] : date('Y-01-01');
$filtro_fecha_fin = isset($_GET['fecha_fin']) ? $_GET['fecha_fin'] : date('Y-12-31');

export_log("Filtros recibidos: ejecutivo=$filtro_ejecutivo, periodo=$filtro_periodo, fecha_inicio=$filtro_fecha_inicio, fecha_fin=$filtro_fecha_fin");

// Calcular fechas según el periodo seleccionado
switch($filtro_periodo) {
    case 'hoy':
        $filtro_fecha_inicio = date('Y-m-d');
        $filtro_fecha_fin = date('Y-m-d');
        break;
    case 'semana':
        $filtro_fecha_inicio = date('Y-m-d', strtotime('monday this week'));
        $filtro_fecha_fin = date('Y-m-d', strtotime('sunday this week'));
        break;
    case 'mes':
        $filtro_fecha_inicio = date('Y-m-01');
        $filtro_fecha_fin = date('Y-m-t');
        break;
    case 'trimestre':
        $trimestre = ceil(date('n') / 3);
        $filtro_fecha_inicio = date('Y-') . sprintf('%02d', ($trimestre - 1) * 3 + 1) . '-01';
        $filtro_fecha_fin = date('Y-m-t', strtotime($filtro_fecha_inicio . ' +2 months'));
        break;
    case 'año':
        $filtro_fecha_inicio = date('Y-01-01');
        $filtro_fecha_fin = date('Y-12-31');
        break;
}

export_log("Fechas calculadas: inicio=$filtro_fecha_inicio, fin=$filtro_fecha_fin");

// Construir condición WHERE para filtros
$where_conditions = ["1=1"];
$params = [];
$types = "";

if ($filtro_ejecutivo !== 'todos') {
    $where_conditions[] = "p.usuario_id = ?";
    $params[] = $filtro_ejecutivo;
    $types .= "i";
}

$where_conditions[] = "DATE(p.fecha_registro) BETWEEN ? AND ?";
$params[] = $filtro_fecha_inicio;
$params[] = $filtro_fecha_fin;
$types .= "ss";

$where_clause = implode(" AND ", $where_conditions);

export_log("Cláusula WHERE construida: $where_clause");

// Consulta simplificada para obtener prospectos
$query = "
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.direccion_comercial, p.fecha_registro,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
    WHERE " . $where_clause . "
    ORDER BY p.fecha_registro DESC";

export_log("Consulta SQL preparada: " . str_replace("\n", " ", $query));

// Preparar y ejecutar la consulta
$stmt = $mysqli->prepare($query);
if (!$stmt) {
    $error_msg = "Error preparando consulta: " . $mysqli->error;
    export_log($error_msg, "ERROR");
    if (!$debug_mode) {
        echo "Error: " . $error_msg;
        exit;
    }
}

export_log("Consulta preparada correctamente");

if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
    export_log("Parámetros vinculados a la consulta");
}

if (!$stmt->execute()) {
    $error_msg = "Error ejecutando consulta: " . $stmt->error;
    export_log($error_msg, "ERROR");
    if (!$debug_mode) {
        echo "Error: " . $error_msg;
        exit;
    }
}

export_log("Consulta ejecutada correctamente");

// Obtener resultados usando bind_result (compatible con PHP 7.3.33)
$id = $tipo_persona = $rut = $razon_social = $rubro = $email = $telefono = $direccion = $fecha = $ejecutivo = null;

$stmt->bind_result(
    $id, $tipo_persona, $rut, $razon_social, $rubro, 
    $email, $telefono, $direccion, $fecha, $ejecutivo
);

export_log("Variables vinculadas para resultados");

// Preparar datos para exportación
$prospectos = [];
$contador = 0;

while ($stmt->fetch()) {
    $contador++;
    $prospectos[] = [
        'ID' => $id,
        'Tipo_Persona' => $tipo_persona,
        'RUT' => $rut,
        'Razon_Social' => $razon_social,
        'Rubro' => $rubro,
        'Email' => $email,
        'Telefono' => $telefono,
        'Direccion' => $direccion,
        'Fecha_Registro' => $fecha,
        'Ejecutivo' => $ejecutivo
    ];
}

$stmt->close();
$mysqli->close();

export_log("Se encontraron $contador prospectos");

// Verificar si hay datos para exportar
if (empty($prospectos)) {
    $error_msg = "No hay datos para exportar con los filtros seleccionados";
    export_log($error_msg, "WARNING");
    if (!$debug_mode) {
        echo "No hay datos para exportar con los filtros seleccionados.";
        exit;
    }
}

// Si estamos en modo debug, mostrar los datos en una tabla
if ($debug_mode) {
    echo "<h2>Datos a exportar</h2>";
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    
    // Encabezados
    echo "<tr>";
    foreach (array_keys($prospectos[0]) as $header) {
        echo "<th>" . htmlspecialchars($header) . "</th>";
    }
    echo "</tr>";
    
    // Datos
    foreach ($prospectos as $prospecto) {
        echo "<tr>";
        foreach ($prospecto as $value) {
            echo "<td>" . htmlspecialchars($value) . "</td>";
        }
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<p>Total de registros: $contador</p>";
    echo "<p>Archivo de log: " . basename($log_file) . "</p>";
    
    echo "<h3>Descargar archivo</h3>";
    echo "<a href='exportar_prospectos.php" . $_SERVER['QUERY_STRING'] . "' class='btn btn-primary'>Descargar CSV</a>";
    
    exit;
}

// Configurar encabezados para descarga de CSV
$fecha_actual = date('Y-m-d');
$nombre_archivo = "prospectos_inteletgroup_{$fecha_actual}.csv";

export_log("Generando archivo CSV: $nombre_archivo");

// Configurar encabezados HTTP para descarga
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $nombre_archivo . '"');
header('Pragma: no-cache');
header('Expires: 0');

// Crear el recurso de salida para escribir el CSV
$output = fopen('php://output', 'w');
if (!$output) {
    $error_msg = "No se pudo abrir el flujo de salida";
    export_log($error_msg, "ERROR");
    echo "Error: " . $error_msg;
    exit;
}

// Configurar para que funcione con caracteres especiales (acentos, etc)
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Escribir encabezados de las columnas
fputcsv($output, array_keys($prospectos[0]));

// Escribir datos
foreach ($prospectos as $prospecto) {
    fputcsv($output, $prospecto);
}

fclose($output);

export_log("Exportación completada con éxito");
exit;
?>
