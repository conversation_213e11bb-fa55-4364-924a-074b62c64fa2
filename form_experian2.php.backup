<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'cache_utils.php';
session_start();

// Inicializar variables importantes
$es_admin = false; // Variable que indica si el usuario es administrador

// Aplicar headers anti-caché
no_cache_headers();

// Verificación mejorada de autenticación
function checkAuth() {
    if (!isset($_SESSION['usuario']) || empty($_SESSION['usuario'])) {
        // Registrar el intento fallido
        error_log("[" . date('Y-m-d H:i:s') . "] Error de autenticación - Usuario no identificado - IP: " . $_SERVER['REMOTE_ADDR']);

        // Redireccionar a login con mensaje de error
        $errorMsg = urlencode("Usuario no autenticado. Inicie sesión para continuar.");
        header('Location: ' . version_url('login.php?error=' . $errorMsg));
        exit;
    }

    // Renovar la sesión para evitar su caducidad prematura
    $_SESSION['last_activity'] = time();
    return true;
}

// Verificar si el usuario está autenticado
checkAuth();

// Incluir el archivo de conexión
require_once 'con_db.php';

// Usar la función createPDOConnection() del archivo con_db.php
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formulario de Cliente</title>
    <?php echo no_cache_meta(); ?>
    <link rel="stylesheet" href="<?php echo version_url('css/form_experian.css'); ?>">
    <!-- Estilos para tablas ordenables -->
    <link rel="stylesheet" href="<?php echo version_url('css/table-sort.css'); ?>">
    <!-- Estilos para notificaciones -->
    <link rel="stylesheet" href="<?php echo version_url('css/notifications.css'); ?>">
    <!-- Agregar Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Cargar jQuery primero -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Agregar SheetJS (versión completa y estable) -->
    <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <!-- Sistema de notificaciones -->
    <script src="<?php echo version_url('js/notifications.js'); ?>"></script>
    <script>
        // Verificar que jQuery se cargó correctamente
        window.onload = function() {
            if (typeof jQuery === "undefined") {
                alert("jQuery no se cargó correctamente");
            } else {
                console.log("jQuery cargado correctamente: " + jQuery.fn.jquery);
            }
            // Verificar la carga de SheetJS
            if (typeof XLSX === "undefined") {
                console.error("Error: SheetJS no se cargó correctamente");
            } else {
                console.log("SheetJS cargado correctamente");
            }
        };

        // Definir la función cargarEjecutivos si no existe
        function cargarEjecutivos(forzarRecarga = false) {
          // Mostrar indicador de carga
          $('#ejecutivos-table tbody').html('<tr><td colspan="11" class="loading-data">Cargando datos...</td></tr>');

          // Agregar timestamp para evitar caché
          var timestamp = new Date().getTime();

          console.log('Cargando ejecutivos con forzarRecarga =', forzarRecarga);
          console.log('Usuario es admin:', window.userIsAdmin);

          $.ajax({
            url: 'endpoints/obtener_prospectos.php?t=' + timestamp, // Agregar timestamp para evitar caché
            type: 'GET',
            dataType: 'json',
            cache: false, // Siempre evitar caché
            beforeSend: function() {
              console.log('Enviando solicitud a endpoints/obtener_prospectos.php');
            },
            success: function(response) {
              console.log('Respuesta recibida:', response);
              if (response.success) {
                console.log('Datos recibidos correctamente. Registros:', response.data.length);
                actualizarTablaProspectos(response.data);
              } else {
                console.error('Error en la respuesta:', response.message);
                $('#ejecutivos-table tbody').html('<tr><td colspan="11" class="error-data">Error: ' + response.message + '</td></tr>');
              }
            },
            error: function(xhr, status, error) {
              console.error('Error AJAX:', {
                status: status,
                error: error,
                response: xhr.responseText
              });
              $('#ejecutivos-table tbody').html('<tr><td colspan="11" class="error-data">Error de conexión</td></tr>');
            }
          });
        }

        // Función para actualizar la tabla de prospectos
        function actualizarTablaProspectos(data) {
          if (!data || data.length === 0) {
            $('#ejecutivos-table tbody').html('<tr><td colspan="11" class="no-data">No hay datos disponibles</td></tr>');
            return;
          }

          var html = '';
          $.each(data, function(index, prospecto) {
            // Usar los datos de la última bitácora si están disponibles
            var estado = prospecto.ultimo_estado || prospecto.estado;
            var observaciones = prospecto.ultima_observacion || prospecto.observaciones || "";
            var fechaRegistro = prospecto.ultima_fecha_gestion || prospecto.fecha_registro;

            html += '<tr>';
            html += '<td><button class="btn-bitacora" data-rut="' + prospecto.rut_ejecutivo + '" data-nombre="' + prospecto.nombre_ejecutivo + '" data-razon="' + prospecto.razon_social + '"><i class="fa fa-book"></i> Bitácora</button></td>';
            html += '<td>' + prospecto.nombre_ejecutivo + '</td>';
            html += '<td>' + prospecto.rut_ejecutivo + '</td>';
            html += '<td>' + prospecto.razon_social + '</td>';
            html += '<td>' + prospecto.rubro + '</td>';
            html += '<td>' + prospecto.contacto + '</td>';
            html += '<td>' + prospecto.telefono + '</td>';
            html += '<td>' + prospecto.fecha + '</td>';
            html += '<td>' + estado + '</td>';
            html += '<td>' + observaciones + '</td>';
            html += '<td>' + fechaRegistro + '</td>';
            html += '</tr>';
          });

          $('#ejecutivos-table tbody').html(html);
        }

        // Función para cargar la bitácora de un prospecto
        function cargarBitacora(rut) {
          if (!rut) return;

          // Mostrar indicador de carga
          $('#bitacora-timeline').html('<div class="loading-data">Cargando actividades...</div>');

          // Definir el flujo de estados en orden
          var flujoEstados = [
            "Envio información",
            "Negociación",
            "Cerrado",
            "B.O. Experian",
            "Proceso de Firma",
            "Firmado",
            "Habilitado"
          ];

          $.ajax({
            url: 'endpoints/obtener_bitacora.php?rut=' + encodeURIComponent(rut),
            type: 'GET',
            dataType: 'json',
            cache: false,
            success: function(response) {
              if (response && response.success) {
                // Filtrar las opciones del select según el último estado
                if (response.ultimo_estado) {
                  var selectEstado = document.getElementById('bitacora_estado');
                  if (selectEstado) {
                    var indiceUltimo = flujoEstados.indexOf(response.ultimo_estado);

                    if (indiceUltimo !== -1) {
                      // Guardar todas las opciones originales si no lo hemos hecho antes
                      if (!selectEstado.dataset.opcionesOriginales) {
                        var opcionesOriginales = [];
                        for (var i = 0; i < selectEstado.options.length; i++) {
                          opcionesOriginales.push({
                            value: selectEstado.options[i].value,
                            text: selectEstado.options[i].text
                          });
                        }
                        selectEstado.dataset.opcionesOriginales = JSON.stringify(opcionesOriginales);
                      }

                      // Limpiar el select
                      selectEstado.innerHTML = '<option value="">Seleccione...</option>';

                      // Agregar solo las opciones válidas (igual o posterior al último estado)
                      for (var j = indiceUltimo; j < flujoEstados.length; j++) {
                        var option = document.createElement('option');
                        option.value = flujoEstados[j];
                        option.text = flujoEstados[j];
                        selectEstado.appendChild(option);
                      }
                    }
                  }
                }

                // Mostrar los registros en el timeline
                if (!response.data || response.data.length === 0) {
                  $('#bitacora-timeline').html('<div class="no-data">No hay registros disponibles</div>');
                } else {
                  let html = '';
                  $.each(response.data, function(index, registro) {
                    // Determinar la clase de estado para el color del punto
                    let estadoClass = 'estado-default';
                    if (registro.estado.toLowerCase().includes('pendiente')) {
                      estadoClass = 'estado-pendiente';
                    } else if (registro.estado.toLowerCase().includes('proceso') || registro.estado.toLowerCase().includes('en curso')) {
                      estadoClass = 'estado-en-proceso';
                    } else if (registro.estado.toLowerCase().includes('completado') || registro.estado.toLowerCase().includes('finalizado')) {
                      estadoClass = 'estado-completado';
                    } else if (registro.estado.toLowerCase().includes('cancelado') || registro.estado.toLowerCase().includes('rechazado')) {
                      estadoClass = 'estado-cancelado';
                    }

                    // Formatear la fecha para mostrar en formato corto
                    let fechaRegistro = new Date(registro.fecha_registro);
                    let fechaFormateada = fechaRegistro.toLocaleDateString('es-CL') + ' ' +
                                        fechaRegistro.toLocaleTimeString('es-CL', {hour: '2-digit', minute:'2-digit'});

                    html += '<div class="timeline-item ' + estadoClass + '">';
                    html += '  <div class="timeline-content">';
                    html += '    <div class="timeline-header">';
                    html += '      <div class="timeline-text"><span class="highlight">' + registro.estado + '</span></div>';
                    html += '      <div class="timeline-time">' + fechaFormateada + '</div>';
                    html += '    </div>';
                    html += '    <div class="timeline-subtext">' + registro.observaciones + '</div>';
                    html += '    <div class="timeline-footer">';
                    html += '      <div>Registrado por: <span class="highlight">' + (registro.nombre_usuario || 'N/A') + '</span></div>';
                    html += '    </div>';
                    html += '  </div>';
                    html += '</div>';
                  });
                  $('#bitacora-timeline').html(html);
                }
              } else {
                $('#bitacora-timeline').html('<div class="error-data">Error: ' + (response ? response.message : 'Respuesta inválida') + '</div>');
              }
            },
            error: function() {
              $('#bitacora-timeline').html('<div class="error-data">Error de conexión al cargar bitácora</div>');
            }
          });
        }
    </script>
    <!-- Se eliminaron las referencias a DataTables -->
</head>
<body>
<?php
// Obtener el nombre del usuario desde la base de datos
$nombre_usuario = '';
try {
    // Verificar que la función existe antes de usarla
    if (function_exists('createPDOConnection')) {
        $connection = createPDOConnection();
    } else {
        // Alternativa si la función no está disponible
        $connection = new PDO(
            "mysql:host=localhost;dbname=gestarse_experian;charset=utf8",
            'gestarse_ncornejo7_experian',
            'N1c0l7as17',
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
    }
    if ($connection) {
        $query = "SELECT correo, COALESCE(nombre_usuario, SUBSTRING_INDEX(correo, '@', 1)) AS nombre_usuario FROM tb_experian_usuarios WHERE id = ?";
        $stmt = $connection->prepare($query);
        $stmt->execute([$_SESSION['usuario_id']]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($result && isset($result['nombre_usuario']) && !empty($result['nombre_usuario'])) {
            $nombre_usuario = $result['nombre_usuario'];
            // Usar el nombre completo sin dividirlo
            $primer_nombre = $nombre_usuario;
        } else {
            // Si no hay nombre_usuario, usar el correo como alternativa
            $nombre_usuario = $_SESSION['usuario'];
            $primer_nombre = $nombre_usuario;
        }
    }
} catch (Exception $e) {
    error_log("Error al obtener nombre de usuario: " . $e->getMessage());
    // En caso de error, usar el correo como alternativa
    $nombre_usuario = $_SESSION['usuario'];
    $primer_nombre = $nombre_usuario;
}
?>
<div class="site-header">
    <div class="header-container">
        <div class="logo-container">
            <img src="<?php echo version_url('img/img_experian/logo.jpg'); ?>" alt="Logo Experian" class="header-logo">
        </div>
        <div class="user-info">
            <span class="user-name"><?php echo htmlspecialchars($primer_nombre); ?></span>
        </div>
        <a href="logout.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Salir</a>
    </div>
</div>

<!-- Loader global para indicar carga -->
<div id="global-loader" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(255,255,255,0.8); z-index:9999; text-align:center; padding-top:20%;">
    <div style="display:inline-block; width:50px; height:50px; border:5px solid #f3f3f3; border-top:5px solid #0046ad; border-radius:50%; animation:spin 1s linear infinite;"></div>
    <p style="margin-top:10px; color:#0046ad; font-weight:bold;">Cargando...</p>
</div>

<style>
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<!-- Error Monitor: Se mostrará solo si hay errores -->
<div id="error-monitor" style="display: none; background-color: #ffebee; border-left: 4px solid #f44336; margin: 10px 0; padding: 10px 15px;">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <h3 style="margin: 0; color: #d32f2f; font-size: 16px;">Error Detectado</h3>
        <button onclick="document.getElementById('error-details').style.display = document.getElementById('error-details').style.display === 'none' ? 'block' : 'none';" 
                style="background: none; border: none; color: #d32f2f; cursor: pointer; font-weight: bold;">
            <i class="fas fa-chevron-down"></i> Detalles
        </button>
    </div>
    <p id="error-message" style="margin: 5px 0; font-weight: 500;"></p>
    <div id="error-details" style="display: none; margin-top: 10px; padding: 10px; background-color: #fff; border: 1px solid #ffcdd2; border-radius: 4px; font-family: monospace; font-size: 12px; white-space: pre-wrap; max-height: 200px; overflow-y: auto;"></div>
</div>

<script>
// Monitor de errores para mostrar detalles en el panel especial
window.addEventListener('error', function(event) {
    var errorMonitor = document.getElementById('error-monitor');
    var errorMessage = document.getElementById('error-message');
    var errorDetails = document.getElementById('error-details');
    
    if (errorMonitor && errorMessage && errorDetails) {
        errorMonitor.style.display = 'block';
        errorMessage.textContent = event.message || 'Error desconocido';
        
        // Crear detalles del error
        var details = [];
        details.push('Archivo: ' + (event.filename || 'desconocido'));
        details.push('Línea: ' + (event.lineno || '?') + ', Columna: ' + (event.colno || '?'));
        if (event.error && event.error.stack) {
            details.push('\nStack Trace:\n' + event.error.stack);
        }
        
        errorDetails.textContent = details.join('\n');
    }
});

// Mostrar loader durante peticiones AJAX
$(document).ajaxStart(function() {
    $('#global-loader').fadeIn(300);
});

$(document).ajaxStop(function() {
    $('#global-loader').fadeOut(300);
});

// Capturar errores AJAX también
$(document).ajaxError(function(event, jqXHR, ajaxSettings, thrownError) {
    var errorMonitor = document.getElementById('error-monitor');
    var errorMessage = document.getElementById('error-message');
    var errorDetails = document.getElementById('error-details');
    
    if (errorMonitor && errorMessage && errorDetails) {
        errorMonitor.style.display = 'block';
        errorMessage.textContent = 'Error en solicitud AJAX: ' + (thrownError || jqXHR.statusText || 'Error desconocido');
        
        // Crear detalles del error
        var details = [];
        details.push('URL: ' + ajaxSettings.url);
        details.push('Tipo: ' + ajaxSettings.type);
        details.push('Código de estado: ' + (jqXHR.status || 'desconocido'));
        
        if (jqXHR.responseText) {
            try {
                // Intentar formatear como JSON
                var jsonResponse = JSON.parse(jqXHR.responseText);
                details.push('\nRespuesta del servidor (JSON):\n' + JSON.stringify(jsonResponse, null, 2));
            } catch (e) {
                // Si no es JSON, mostrar como texto
                details.push('\nRespuesta del servidor:\n' + jqXHR.responseText.substring(0, 1000));
                if (jqXHR.responseText.length > 1000) details.push('... (respuesta truncada)');
            }
        }
        
        errorDetails.textContent = details.join('\n');
    }
});
</script>

<div class="tabs-container">
    <!-- Contenido principal -->
    <div class="main-content" style="margin-bottom: 90px;">

    <!-- Tab Contenido: Formulario -->
    <div id="form-tab" class="tab-content" style="overflow-y: auto; margin-bottom: 65px;">
        <form id="formExperian" method="POST" action="guardar_formulario.php" enctype="multipart/form-data">            <div class="container">
                <header>

                    <h1>Formulario de Registro de Cliente</h1>
                </header>

                <!-- Indicador de pasos -->
                <div class="steps-container">
                    <div class="progress-line">
                        <div class="fill"></div>
                    </div>
                    <div class="step-indicator active" data-step="1">1. Identificación del Cliente</div>
                    <div class="step-indicator" data-step="2">2. Datos de Contactos</div>
                    <div class="step-indicator" data-step="3">3. Servicios y Transacciones</div>
                </div>

                <!-- Sección 1: Identificación del cliente -->
                <div class="section-container active" id="section1">
                    <div class="section-header">1. IDENTIFICACIÓN DEL CLIENTE</div>

                    <table>
                        <tr>
                            <td class="label">Tipo de Cliente</td>
                            <td class="input-cell">
                                <select name="tipo_cliente" title="Tipo de Cliente" required>
                                    <option value="">Seleccione...</option>
                                    <option>Cliente Vigente</option>
                                    <option>Cliente No vigente</option>
                                </select>
                            </td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="rut"
                                    title="Rut"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12"
                                    required>
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Razón Social</td>
                            <td class="input-cell">
                                <input type="text" name="razon_social" title="Razón Social" required>
                            </td>
                            <td class="label">Nombre Representante Legal 1</td>
                            <td class="input-cell">
                                <input type="text" name="nombre_representante1" title="Nombre Representante Legal 1" required>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Rut Representante 1</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="rut_representante1"
                                    title="Rut Representante 1"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                            <td class="label">Nombre Representante Legal 2</td>
                            <td class="input-cell"><input type="text" name="nombre_representante2" title="Nombre Representante Legal 2"></td>
                        </tr>
                        <tr>
                            <td class="label">Rut Representante 2</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="rut_representante2"
                                    title="Rut Representante 2"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                            <td class="label">Nombre Representante Legal 3</td>
                            <td class="input-cell"><input type="text" name="nombre_representante3" title="Nombre Representante Legal 3"></td>
                        </tr>
                        <tr>
                            <td class="label">Rut Representante 3</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="rut_representante3"
                                    title="Rut Representante 3"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                            <td class="label">Sistema Creación de Empresa</td>
                            <td class="input-cell">
                                <select name="sistema_creacion" title="Sistema Creación de Empresa">
                                    <option>Tradicional</option>
                                    <option>Empresa por un día</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Fecha de creación</td>
                            <td class="input-cell">
                                <input type="date" name="fecha_creacion" title="Fecha de creación" required>
                            </td>
                            <td class="label">Notaría</td>
                            <td class="input-cell"><input type="text" name="notaria" title="Notaría"></td>
                        </tr>
                        <tr>
                            <td class="label">Actividad Económica SII</td>
                            <td class="input-cell"><input type="number" name="actividad_economica" title="Actividad Económica SII"></td>
                            <td class="label">Fecha de Constitución</td>
                            <td class="input-cell">
                                <input type="date" name="fecha_constitucion" title="Fecha de Constitución" required>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Dirección</td>
                            <td class="input-cell"><input type="text" name="direccion" title="Dirección"></td>
                            <td class="label">Comuna</td>
                            <td class="input-cell"><input type="text" name="comuna" title="Comuna"></td>
                        </tr>
                        <tr>
                            <td class="label">Página Web</td>
                            <td class="input-cell"><input type="text" name="pagina_web" title="Página Web"></td>
                            <td class="label">Correo Electrónico contacto</td>
                            <td class="input-cell">
                                <input type="email" name="email" title="Correo Electrónico contacto"
                                    pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
                                    required>
                                <div class="info-message">Formato: <EMAIL></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="tel" name="telefono" title="Teléfono"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678"
                                    required>
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                            <td class="label">Clasificación de Cliente SII</td>
                            <td class="input-cell">
                                <select name="clasificacion_sii" title="Clasificación de Cliente SII">
                                    <option>Nuevo</option>
                                    <option>Antiguo</option>
                                </select>
                            </td>
                        </tr>
                    </table>

                    <div class="nav-buttons">
                        <div></div> <!-- Espacio vacío para alinear a la derecha -->
                        <button type="button" class="btn-next" data-next="2" onclick="console.log('Botón siguiente clickeado manualmente'); showManualFormSection(2);">Siguiente</button>
                    </div>
                    
                    <script>
                    // Función para navegación manual en caso de que el evento no funcione
                    function showManualFormSection(sectionNumber) {
                        console.log('Función manual para cambiar a sección:', sectionNumber);
                        
                        // Ocultar todas las secciones
                        document.querySelectorAll('#form-tab .section-container').forEach(function(section) {
                            section.classList.remove('active');
                        });
                        
                        // Mostrar la sección seleccionada
                        var targetSection = document.querySelector('#form-tab #section' + sectionNumber);
                        if (targetSection) {
                            targetSection.classList.add('active');
                        }
                        
                        // Actualizar indicadores
                        document.querySelectorAll('#form-tab .step-indicator').forEach(function(indicator) {
                            indicator.classList.remove('active');
                            if (indicator.getAttribute('data-step') === sectionNumber.toString()) {
                                indicator.classList.add('active');
                            }
                        });
                        
                        // Actualizar barra de progreso
                        var progressFill = document.querySelector('#form-tab .progress-line .fill');
                        if (progressFill) {
                            var percentage = ((parseInt(sectionNumber) - 1) / 2) * 100;
                            progressFill.style.width = percentage + '%';
                        }
                    }
                    </script>
                </div>

                <!-- Sección 2: Datos de Contactos -->
                <div class="section-container" id="section2">
                    <div class="section-header">2. DATOS DE CONTACTOS</div>

                    <table>
                        <tr>
                            <td class="label">Contacto Nombre</td>
                            <td class="input-cell"><input type="text" name="contacto_nombre" title="Contacto Nombre"></td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="contacto_rut"
                                    title="Rut"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="text" name="contacto_telefono" title="Teléfono"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678">
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                            <td class="label">Correo Electrónico</td>
                            <td class="input-cell">
                                <input type="email" name="contacto_email" title="Correo Electrónico"
                                    pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$">
                                <div class="info-message">Formato: <EMAIL></div>
                            </td>
                        </tr>
                        <td class="label">2.1 DATOS DE BACKUP</td>
                        <tr>
                            <td class="label">Contacto Backup Nombre</td>
                            <td class="input-cell"><input type="text" name="contacto_backup_nombre" title="Contacto Backup Nombre"></td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="contacto_backup_rut"
                                    title="Rut"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="text" name="contacto_backup_telefono" title="Teléfono"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678">
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                            <td class="label">Correo Electrónico</td>
                            <td class="input-cell">
                                <input type="email" name="contacto_backup_email" title="Correo Electrónico"
                                    pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$">
                                <div class="info-message">Formato: <EMAIL></div>
                            </td>
                        </tr>
                    </table>

                    <div class="nav-buttons">
                        <button type="button" class="btn-prev" data-prev="1" onclick="showManualFormSection(1);">Anterior</button>
                        <button type="button" class="btn-next" data-next="3" onclick="showManualFormSection(3);">Siguiente</button>
                    </div>
                </div>

                <!-- Sección 3: Servicios y Nivel de Transacciones -->
                <div class="section-container" id="section3">
                    <div class="section-header">3. SERVICIOS Y NIVEL DE TRANSACCIONES / PUNTOS</div>

                    <!-- Subsección 3.1: Publicación de Morosos -->
                    <div class="subsection-header">3.1 PUBLICACIÓN DE MOROSOS</div>
                    <table>
                        <tr>
                            <td class="label">Plan</td>
                            <td class="input-cell">
                                <select name="morosos_plan" id="morosos_plan" title="Plan Publicación de Morosos">
                                    <option value="">Seleccione...</option>
                                    <option>XS</option>
                                    <option>S</option>
                                    <option>M</option>
                                    <option>L</option>
                                    <option>XL</option>
                                </select>
                            </td>
                            <td class="label">Número de Consultas</td>
                            <td class="input-cell">
                                <input type="text" name="morosos_consultas" id="morosos_consultas" title="Número de Consultas"  readonly>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">UF Mensual</td>
                            <td class="input-cell">
                                <input type="number" name="morosos_uf" id="morosos_uf" step="0.01" title="UF Mensual" placeholder="0.00" readonly>
                            </td>
                            <td class="label">% de descuento</td>
                            <td class="input-cell">
                                <select name="morosos_descuento" title="% de descuento">
                                    <option value="0">0%</option>
                                    <option value="10">10%</option>
                                    <option value="20">20%</option>
                                    <option value="30">30%</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Nuevo Valor</td>
                            <td class="input-cell">
                                <input type="number" name="morosos_nuevo_valor" step="0.01" title="Nuevo Valor" placeholder="0.00" readonly>
                            </td>
                            <td></td>
                            <td></td>
                        </tr>
                    </table>

                    <!-- Subsección 3.2: Informe Advanced SME -->
                    <div class="subsection-header">3.2 INFORME ADVANCED SME</div>
                    <table>
                        <tr>
                            <td class="label">Plan</td>
                            <td class="input-cell">
                                <select name="advanced_plan" title="Plan Advanced SME">
                                    <option value="">Seleccione...</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                    <option value="200">200</option>
                                    <option value="300">300</option>
                                    <option value="400">400</option>
                                    <option value="500">500</option>
                                    <option value="1000">1.000</option>
                                    <option value="2000">2.000</option>
                                    <option value="3000">3.000</option>
                                    <option value="4000">4.000</option>
                                    <option value="5000">5.000</option>
                                </select>
                            </td>
                            <td class="label"> UF / transacción</td>
                            <td class="input-cell">
                                <input type="text" name="advanced_consultas" title="Número de Consultas"  readonly>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">UF Mensual</td>
                            <td class="input-cell">
                                <input type="number" name="advanced_uf" step="0.01" title="UF Mensual" placeholder="0.00" readonly>
                            </td>
                            <td class="label">% de descuento</td>
                            <td class="input-cell">
                                <select name="advanced_descuento" title="% de descuento">
                                    <option value="0">0%</option>
                                    <option value="5">5%</option>
                                    <option value="10">10%</option>
                                    <option value="15%">15%</option>
                                    <option value="20">20%</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Nuevo Valor</td>
                            <td class="input-cell">
                                <input type="number" name="advanced_nuevo_valor" step="0.01" title="Nuevo Valor" placeholder="0.00" readonly>
                            </td>
                            <td></td>
                            <td></td>
                        </tr>
                    </table>

                    <!-- Subsección 3.3: Uso de Claves -->
                    <div class="subsection-header">3.3 PARA USO DE CLAVES</div>
                    <table>
                        <tr>
                            <td class="label">Nombre</td>
                            <td class="input-cell">
                                <input type="text" name="clave_nombre" title="Nombre para uso de claves">
                            </td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="clave_rut"
                                    title="Rut para uso de claves"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Correo</td>
                            <td class="input-cell">
                                <input type="email" name="clave_email" title="Correo para uso de claves"
                                    pattern="[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,}$">
                                <div class="info-message">Formato: <EMAIL></div>
                            </td>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="text" name="clave_telefono" title="Teléfono para uso de claves"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678">
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                        </tr>
                    </table>

                    <!-- Subsección 3.4: Backup de Claves -->
                    <div class="subsection-header">3.4 BACKUP DE CLAVES</div>
                    <table>
                        <tr>
                            <td class="label">Nombre</td>
                            <td class="input-cell">
                                <input type="text" name="backup_clave_nombre" title="Nombre backup de claves">
                            </td>
                            <td class="label">Rut</td>
                            <td class="input-cell">
                                <input type="text"
                                    name="backup_clave_rut"
                                    title="Rut backup de claves"
                                    class="rut-input"
                                    pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                    placeholder="12.345.678-9"
                                    maxlength="12">
                                <div class="rut-message">Formato: 12.345.678-9</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Correo</td>
                            <td class="input-cell">
                                <input type="email" name="backup_clave_email" title="Correo backup de claves"
                                    pattern="[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,}$">
                                <div class="info-message">Formato: <EMAIL></div>
                            </td>
                            <td class="label">Teléfono</td>
                            <td class="input-cell">
                                <input type="text" name="backup_clave_telefono" title="Teléfono backup de claves"
                                    pattern="[0-9]{9}"
                                    maxlength="9"
                                    placeholder="912345678">
                                <div class="info-message">Debe contener 9 dígitos</div>
                            </td>
                        </tr>
                    </table>

                    <!-- Nueva Subsección 3.5: Documentos -->
                    <div class="subsection-header">3.5 DOCUMENTOS</div>
                    <table>
                        <tr>
                            <td class="label">Carnet de Identidad (CI)</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_ci" id="archivo_ci" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Erut</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_erut" id="archivo_erut" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Extracto</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_extracto" id="archivo_extracto" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">CI Frente</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_ci_frente" id="archivo_ci_frente" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">CI Detrás</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_ci_detras" id="archivo_ci_detras" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Carpeta Tributaria</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_carpeta_tributaria" id="archivo_carpeta_tributaria" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="label">Consulta de Terceros</td>
                            <td class="input-cell" colspan="3">
                                <input type="file" name="archivo_consulta_terceros" id="archivo_consulta_terceros" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="info-message">Formatos permitidos: PDF, JPG, PNG (Máx. 32MB)</div>
                            </td>
                        </tr>
                    </table>

                    <div class="nav-buttons">
                        <button type="button" class="btn-prev" data-prev="2" onclick="showManualFormSection(2);">Anterior</button>
                        <button type="submit" class="btn-submit">Guardar Formulario</button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Tab Contenido: Tabla -->
    <?php if ($_SESSION['usuario_id'] == 4): ?>
    <div id="table-tab" class="tab-content">
        <div id="table-container">
            <h2>Registros de Clientes</h2>
            <button id="exportClients" class="export-button">
                <i class="fa fa-download"></i> Descargar Registros
            </button>
            <div class="table-controls">
                <input type="text" class="table-search" id="tableSearch" placeholder="Buscar...">
            </div>
            <div class="table-wrapper" style="overflow-x: auto; width: 100%; display: block;">
                <table id="user-table" style="min-width: 100%; width: auto; border-collapse: separate; border-spacing: 0;">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Tipo Cliente</th>
                            <th>RUT</th>
                            <th>Razón Social</th>
                            <th>Nombre Representante 1</th>
                            <th>RUT Representante 1</th>
                            <th>Nombre Representante 2</th>
                            <th>RUT Representante 2</th>
                            <th>Nombre Representante 3</th>
                            <th>RUT Representante 3</th>
                            <th>Sistema Creación</th>
                            <th>Fecha Creación</th>
                            <th>Notaría</th>
                            <th>Actividad Económica</th>
                            <th>Fecha Constitución</th>
                            <th>Dirección</th>
                            <th>Comuna</th>
                            <th>Página Web</th>
                            <th>Email</th>
                            <th>Teléfono</th>
                            <th>Clasificación SII</th>
                            <th>Contacto Nombre</th>
                            <th>Contacto RUT</th>
                            <th>Contacto Teléfono</th>
                            <th>Contacto Email</th>
                            <th>Contacto Backup Nombre</th>
                            <th>Contacto Backup RUT</th>
                            <th>Contacto Backup Teléfono</th>
                            <th>Contacto Backup Email</th>
                            <th>Morosos Plan</th>
                            <th>Morosos Consultas</th>
                            <th>Morosos UF</th>
                            <th>Morosos Descuento</th>
                            <th>Morosos Nuevo Valor</th>
                            <th>Advanced Plan</th>
                            <th>Advanced Consultas</th>
                            <th>Advanced UF</th>
                            <th>Advanced Descuento</th>
                            <th>Advanced Nuevo Valor</th>
                            <!-- Columnas para claves de usuario -->
                            <th>Nombre Usuario Clave</th>
                            <th>RUT Usuario Clave</th>
                            <th>Email Usuario Clave</th>
                            <th>Teléfono Usuario Clave</th>
                            <th>Nombre Backup Clave</th>
                            <th>RUT Backup Clave</th>
                            <th>Email Backup Clave</th>
                            <th>Teléfono Backup Clave</th>
                            <!-- Nuevas columnas para documentos -->
                            <th>CI</th>
                            <th>ERUT</th>
                            <th>Extracto</th>
                            <th>CI Frente</th>
                            <th>CI Detrás</th>
                            <th>Carpeta Tributaria</th>
                            <th>Consulta Terceros</th>
                            <th>Fecha Creación Registro</th>
                            <th>ID Usuario</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        try {
                            // Verificar que la función existe antes de usarla
                            if (function_exists('createPDOConnection')) {
                                $connection = createPDOConnection();
                            } else {
                                // Alternativa si la función no está disponible
                                $connection = new PDO(
                                    "mysql:host=localhost;dbname=gestarse_experian;charset=utf8",
                                    'gestarse_ncornejo7_experian',
                                    'N1c0l7as17',
                                    [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                                );
                            }
                            if ($connection) {
                                $query = "SELECT * FROM form_experian ORDER BY id DESC";
                                $stmt = $connection->prepare($query);
                                $stmt->execute();
                                $clientes = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                if (empty($clientes)) {
                                    echo '<tr><td colspan="54" class="no-data">No hay registros disponibles</td></tr>';
                                } else {
                                    foreach ($clientes as $cliente) {
                                        echo '<tr>';
                                        foreach ($cliente as $valor) {
                                            echo '<td>' . htmlspecialchars($valor ?? '') . '</td>';
                                        }
                                        echo '</tr>';
                                    }
                                }
                            }
                        } catch (Exception $e) {
                            echo '<tr><td colspan="54" class="error-data">Error al cargar los datos: ' . htmlspecialchars($e->getMessage()) . '</td></tr>';
                            error_log("Error en la consulta de clientes: " . $e->getMessage());
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Tab Contenido: Nueva Opción -->
<div id="new-tab" class="tab-content active" style="margin-bottom: 70px; overflow-y: visible;">
    <div class="container">

        <!-- Modal para el formulario de prospecto -->
        <div id="prospectoModal" class="modal">
            <div class="modal-content">
                <span class="close-modal">&times;</span>
                <h2>Registro de prospecto</h2>



                <?php
                // Obtener el nombre del usuario desde la base de datos
                $nombre_usuario = '';
                try {
                    // Verificar que la función existe antes de usarla
                    if (function_exists('createPDOConnection')) {
                        $connection = createPDOConnection();
                    } else {
                        // Alternativa si la función no está disponible
                        $connection = new PDO(
                            "mysql:host=localhost;dbname=gestarse_experian;charset=utf8",
                            'gestarse_ncornejo7_experian',
                            'N1c0l7as17',
                            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                        );
                    }
                    if ($connection) {
                        $query = "SELECT correo, COALESCE(nombre_usuario, SUBSTRING_INDEX(correo, '@', 1)) AS nombre_usuario FROM tb_experian_usuarios WHERE id = ?";
                        $stmt = $connection->prepare($query);
                        $stmt->execute([$_SESSION['usuario_id']]);
                        $result = $stmt->fetch(PDO::FETCH_ASSOC);
                        if ($result && isset($result['nombre_usuario'])) {
                            $nombre_usuario = $result['nombre_usuario'];
                        } else {
                            // Si no hay nombre_usuario, usar el correo como alternativa
                            $nombre_usuario = $_SESSION['usuario'];
                        }
                    }
                } catch (Exception $e) {
                    error_log("Error al obtener nombre de usuario: " . $e->getMessage());
                    // En caso de error, usar el correo como alternativa
                    $nombre_usuario = $_SESSION['usuario'];
                }
                ?>

                <form id="formEjecutivos" method="POST" action="guardar_prospecto.php">
                <div class="section-header">DATOS DEL PROSPECTO</div>

                <table>
                    <tr>
                        <td class="label">Nombre Ejecutivo</td>
                        <td class="input-cell">
                            <input type="text" name="nombre_prospecto" title="Nombre Prospecto" value="<?php echo htmlspecialchars($nombre_usuario); ?>" readonly required>
                        </td>
                        <td class="label">Rut</td>
                        <td class="input-cell">
                            <input type="text"
                                name="rut_ejecutivo"
                                title="Rut"
                                class="rut-input"
                                pattern="^[0-9]{1,2}\.[0-9]{3}\.[0-9]{3}-[0-9kK]{1}$"
                                placeholder="12.345.678-9"
                                maxlength="12"
                                required>
                            <div class="rut-message">Formato: 12.345.678-9</div>
                        </td>
                    </tr>
                    <tr>
                        <td class="label">Razón Social</td>
                        <td class="input-cell">
                            <input type="text" name="razon_social" title="Razón Social" required>
                        </td>
                        <td class="label">Rubro</td>
                        <td class="input-cell">
                            <input type="text" name="rubro" title="Rubro" required>
                        </td>
                    </tr>
                    <tr>
                        <td class="label">Contacto</td>
                        <td class="input-cell">
                            <input type="text" name="contacto" title="Contacto" required>
                        </td>
                        <td class="label">Teléfono</td>
                        <td class="input-cell">
                            <input type="tel" name="telefono" title="Teléfono"
                                pattern="[0-9]{9}"
                                maxlength="9"
                                placeholder="912345678"
                                required>
                            <div class="info-message">Debe contener 9 dígitos</div>
                        </td>
                    </tr>
                    <tr>
                        <td class="label">Fecha</td>
                        <td class="input-cell">
                            <input type="date" name="fecha" title="Fecha" value="<?php echo date('Y-m-d'); ?>" required>
                        </td>
                        <td class="label">Estado</td>
                        <td class="input-cell">
                            <select name="estado" title="Estado" required>
                                <option value="">Seleccione...</option>
                                <!-- <option value="Interesado">Interesado</option>
                                <option value="No interesado">No interesado</option> -->
                                <option value="Envio información">Envio información</option>
                                <option value="Negociación">Negociación</option>
                                <option value="Cerrado">Cerrado</option>
                                <option value="B.O. Experian">B.O. Experian</option>
                                <option value="Proceso de Firma">Proceso de Firma</option>
                                <option value="Firmado">Firmado</option>
                                <option value="Habilitado">Habilitado</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="label">Observaciones</td>
                        <td class="input-cell">
                            <textarea name="observaciones" title="Observaciones" rows="3" style="width: 100%; resize: vertical;"></textarea>
                        </td>
                        <td class="label"></td>
                        <td class="input-cell"></td>
                    </tr>
                </table>

                <div class="form-actions">
                    <button type="submit" class="btn-submit" style="background-color: green;">
                        <i class="fa fa-save"></i> Guardar prospecto
                    </button>
                    <button type="reset" class="btn-reset">
                        <i class="fa fa-eraser"></i> Limpiar Formulario
                    </button>
                </div>
            </form>
            </div>
        </div>

        <div class="ejecutivos-table-container" style="margin-top: 30px;">
            <div class="section-header">Registros de prospectos</div>

            <!-- Botón para abrir el modal -->
            <div class="button-container" style="margin-bottom: 15px;">
                <button id="openProspectoModal" class="btn-modal-open" style="background-color: green;">
                    <i class="fa fa-plus-circle"></i> Nuevo Prospecto
                </button>
                <button id="exportEjecutivos" class="export-button">
                    <i class="fa fa-download"></i> Descargar Prospectos
                </button>
            </div>
            <div class="table-controls" style="margin-bottom: 15px; display: flex; justify-content: space-between; align-items: center;">
                <input type="text" id="ejecutivos-search" class="table-search" placeholder="Buscar...">
                <?php if (isset($es_admin) && !$es_admin): ?>
                <div class="filter-indicator" style="background-color: #e7f3ff; padding: 5px 10px; border-radius: 4px; font-size: 14px; color: #0056b3;">
                    <i class="fa fa-filter"></i> Mostrando solo sus prospectos
                </div>
                <?php endif; ?>
            </div>
            <div class="table-wrapper">
                <table id="ejecutivos-table">
                    <thead>
                        <tr>
                            <th>Acciones</th>
                            <th>Nombre Ejecutivo</th>
                            <th>RUT</th>
                            <th>Razón Social</th>
                            <th>Rubro</th>
                            <th>Contacto</th>
                            <th>Teléfono</th>
                            <th>Fecha</th>
                            <th>Estado</th>
                            <th>Observaciones</th>
                            <th>Fecha Registro</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- La tabla se llenará dinámicamente con JavaScript -->
                        <tr><td colspan="11" class="loading-data">Cargando datos...</td></tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Bitácora -->
<div id="bitacoraModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <span class="close-modal">&times;</span>
            <h2>Bitácora de Actividades</h2>
            <div id="bitacora-info" class="modal-subtitle"></div>
        </div>
        <div class="modal-body">
            <form id="formBitacora" method="post" action="endpoints/guardar_bitacora.php">
                <input type="hidden" name="rut_ejecutivo" id="bitacora_rut">

                <div class="form-group">
                    <label for="bitacora_estado">Estado:</label>
                    <select name="estado" id="bitacora_estado" required>
                        <option value="">Seleccione...</option>
                        <option value="Envio información">Envio información</option>
                        <option value="Negociación">Negociación</option>
                        <option value="Cerrado">Cerrado</option>
                        <option value="B.O. Experian">B.O. Experian</option>
                        <option value="Proceso de Firma">Proceso de Firma</option>
                        <option value="Firmado">Firmado</option>
                        <option value="Habilitado">Habilitado</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="bitacora_observaciones">Observaciones:</label>
                    <textarea name="observaciones" id="bitacora_observaciones" rows="4" required></textarea>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn-submit">
                        <i class="fa fa-save"></i> Guardar Registro
                    </button>
                </div>
            </form>

            <div class="bitacora-historial">
                <div class="bitacora-header">
                    <h3>Historial de Actividades</h3>
                    <a href="#" class="view-all">Ver todo <i class="fa fa-angle-right"></i></a>
                </div>
                <div class="timeline-container">
                    <div class="timeline" id="bitacora-timeline">
                        <!-- Aquí se cargarán los registros de la bitácora en formato timeline -->
                        <div class="loading-data">Cargando actividades...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer Navigation -->
<div class="app-footer">
    <button class="footer-tab active" data-tab="new-tab">
        <i class="fa fa-user-plus"></i>
        <span>Prospecto</span>
    </button>
    <button class="footer-tab" data-tab="form-tab">
        <i class="fa fa-file-text"></i>
        <span>Venta</span>
    </button>
    <?php if ($_SESSION['usuario_id'] == 4): ?>
    <button class="footer-tab" data-tab="table-tab">
        <i class="fa fa-table"></i>
        <span>Registros</span>
    </button>
    <?php endif; ?>
</div>

<!-- Scripts -->
<script>
// Define user permissions for JavaScript
window.userIsAdmin = <?php echo $_SESSION['usuario_id'] == 4 ? 'true' : 'false'; ?>;

// Arreglo para botones de navegación entre secciones del formulario
document.addEventListener('DOMContentLoaded', function() {
    // Verificar que los botones existan
    var nextButtons = document.querySelectorAll('#form-tab .btn-next');
    var prevButtons = document.querySelectorAll('#form-tab .btn-prev');
    var stepIndicators = document.querySelectorAll('#form-tab .step-indicator');
    
    console.log('Inicializando navegación de formulario - Botones siguiente:', nextButtons.length);
    
    // Función para mostrar una sección específica
    function showFormSection(sectionNumber) {
        console.log('Cambiando a sección:', sectionNumber);
        
        // Ocultar todas las secciones
        document.querySelectorAll('#form-tab .section-container').forEach(function(section) {
            section.classList.remove('active');
        });
        
        // Mostrar la sección seleccionada
        var targetSection = document.querySelector('#form-tab #section' + sectionNumber);
        if (targetSection) {
            targetSection.classList.add('active');
        }
        
        // Actualizar indicadores
        document.querySelectorAll('#form-tab .step-indicator').forEach(function(indicator) {
            indicator.classList.remove('active');
            if (indicator.getAttribute('data-step') === sectionNumber.toString()) {
                indicator.classList.add('active');
            }
        });
        
        // Actualizar barra de progreso
        var progressFill = document.querySelector('#form-tab .progress-line .fill');
        if (progressFill) {
            var percentage = ((parseInt(sectionNumber) - 1) / 2) * 100;
            progressFill.style.width = percentage + '%';
        }
    }
    
    // Configurar botones "Siguiente"
    nextButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            var nextSection = this.getAttribute('data-next');
            console.log('Botón siguiente clickeado, ir a sección:', nextSection);
            showFormSection(nextSection);
        });
    });
    
    // Configurar botones "Anterior"
    prevButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            var prevSection = this.getAttribute('data-prev');
            console.log('Botón anterior clickeado, ir a sección:', prevSection);
            showFormSection(prevSection);
        });
    });
    
    // Configurar indicadores de paso
    stepIndicators.forEach(function(indicator) {
        indicator.addEventListener('click', function(e) {
            var stepNumber = this.getAttribute('data-step');
            console.log('Indicador clickeado, ir a sección:', stepNumber);
            showFormSection(stepNumber);
        });
    });
});

// Pre-load table data to avoid additional requests
<?php
// Cargar datos para todos los usuarios

    // Get client records data
    try {
        // Inicializar con un array vacío para evitar errores
        echo "window.preloadedClientData = " . json_encode(['success' => true, 'data' => []]) . ";\n";
    } catch (Exception $e) {
        error_log("Error loading client data: " . $e->getMessage());
        echo "window.preloadedClientData = {success: false, message: 'Error al cargar los datos iniciales: " . addslashes($e->getMessage()) . "'};\n";
    }

    // Get prospects data
    try {
        // Verificar que la función existe antes de usarla
        if (function_exists('createPDOConnection')) {
            $connection = createPDOConnection();
        } else {
            // Alternativa si la función no está disponible
            $connection = new PDO(
                "mysql:host=localhost;dbname=gestarse_experian;charset=utf8",
                'gestarse_ncornejo7_experian',
                'N1c0l7as17',
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
        }
        if ($connection) {
            // Verificar si el usuario tiene rol de administrador
            $usuario_id = $_SESSION['usuario_id'];
            $es_admin = false;
            $query_rol = "SELECT id, correo, nombre_usuario, rol FROM tb_experian_usuarios WHERE id = ?";
            $stmt_rol = $connection->prepare($query_rol);
            $stmt_rol->execute([$usuario_id]);
            $usuario = $stmt_rol->fetch(PDO::FETCH_ASSOC);

            // Registrar información del usuario para depuración
            error_log("form_experian2.php - Usuario ID: " . $usuario_id . " - Datos: " . json_encode($usuario));

            // Forzar a que todos los usuarios que no sean explícitamente administradores vean solo sus prospectos
            if ($usuario && isset($usuario['rol']) && ($usuario['rol'] == 'admin' || $usuario['rol'] == 'administrador')) {
                $es_admin = true;
                error_log("form_experian2.php - Usuario " . $usuario['nombre_usuario'] . " (ID: " . $usuario_id . ") es administrador");
            } else {
                $es_admin = false;
                error_log("form_experian2.php - Usuario " . ($usuario ? $usuario['nombre_usuario'] : 'desconocido') . " (ID: " . $usuario_id . ") NO es administrador");
            }

            // Definir la variable para usar en JavaScript
            ?>
            <script>
            window.userIsAdmin = <?php echo $es_admin ? "true" : "false"; ?>;
            </script>
            <?php

            // Fetch prospects with last bitacora data
            if (isset($es_admin) && $es_admin) {
                // Si es admin, mostrar todos los prospectos
                $prospectsQuery = "SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                ORDER BY p.id DESC";
                $prospectsStmt = $connection->prepare($prospectsQuery);
                error_log("form_experian2.php - Consulta SQL para administrador: " . $prospectsQuery);
                $prospectsStmt->execute();
            } else {
                // Si no es admin, filtrar por usuario_id
                $prospectsQuery = "SELECT p.*,
                                  b.estado AS ultimo_estado,
                                  b.observaciones AS ultima_observacion,
                                  b.fecha_registro AS ultima_fecha_gestion,
                                  u.nombre_usuario AS nombre_usuario_creador
                                FROM tb_experian_prospecto p
                                LEFT JOIN (
                                    SELECT rut_ejecutivo, estado, observaciones, fecha_registro,
                                           ROW_NUMBER() OVER (PARTITION BY rut_ejecutivo ORDER BY fecha_registro DESC) as rn
                                    FROM tb_experian_prospecto_bitacora
                                ) b ON p.rut_ejecutivo = b.rut_ejecutivo AND b.rn = 1
                                LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id
                                WHERE p.usuario_id = :usuario_id
                                ORDER BY p.id DESC";
                $prospectsStmt = $connection->prepare($prospectsQuery);
                $prospectsStmt->bindParam(':usuario_id', $usuario_id, PDO::PARAM_INT);
                error_log("form_experian2.php - Consulta SQL para usuario regular: " . $prospectsQuery . " (usuario_id = " . $usuario_id . ")");
                $prospectsStmt->execute();
            }

            // Registrar cuántos registros se encontraron
            $prospectsData = $prospectsStmt->fetchAll(PDO::FETCH_ASSOC);
            error_log("form_experian2.php - Se encontraron " . count($prospectsData) . " prospectos para el usuario ID: " . $usuario_id);

            // Convert to JSON for JavaScript
            echo "<script>window.preloadedProspectsData = " . json_encode(['success' => true, 'data' => $prospectsData]) . ";</script>\n";
        }
    } catch (Exception $e) {
        error_log("Error loading prospects data: " . $e->getMessage());
        echo "<script>window.preloadedProspectsData = {success: false, message: 'Error al cargar los datos iniciales: " . addslashes($e->getMessage()) . "'};</script>\n";
    }
?>
<script src="<?php echo version_url('js/form_experian.js'); ?>?v=<?php echo filemtime(__DIR__ . '/js/form_experian.js'); ?>"></script>


<script>
// Esperar a que el DOM esté completamente cargado
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado - Configurando eventos del footer directamente');

    // Cargar la tabla de prospectos automáticamente para todos los usuarios
    if (typeof cargarEjecutivos === 'function') {
        console.log('Cargando tabla de prospectos automáticamente...');
        setTimeout(function() {
            cargarEjecutivos();
        }, 100);
    } else {
        console.warn('Función cargarEjecutivos no disponible en DOMContentLoaded');
    }

    // Obtener todos los botones del footer
    var footerTabs = document.querySelectorAll('.footer-tab');
    console.log('Tabs del footer encontrados:', footerTabs.length);

    // Configurar eventos de clic para cada botón
    footerTabs.forEach(function(tab) {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            var tabId = this.getAttribute('data-tab');
            console.log('Tab clickeado (script inline):', tabId);

            // Desactivar todos los tabs
            footerTabs.forEach(function(t) {
                t.classList.remove('active');
            });

            // Activar el tab actual
            this.classList.add('active');

            // Ocultar todos los contenidos
            var tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(function(content) {
                content.classList.remove('active');
            });

            // Mostrar el contenido correspondiente
            var activeContent = document.getElementById(tabId);
            if (activeContent) {
                activeContent.classList.add('active');
            }

            // Cargar datos si es necesario
            if (tabId === 'new-tab') {
                setTimeout(function() {
                    if (typeof cargarEjecutivos === 'function') {
                        cargarEjecutivos();
                    }
                }, 100);
            } else if (tabId === 'table-tab' && window.userIsAdmin) {
                setTimeout(function() {
                    if (typeof loadTableData === 'function') {
                        loadTableData();
                    }
                }, 100);
            }
        });
    });

    // Configurar búsqueda en tabla de prospectos directamente
    var ejecutivosSearch = document.getElementById('ejecutivos-search');
    if (ejecutivosSearch) {
        console.log('Configurando búsqueda para tabla de prospectos (script inline)');

        ejecutivosSearch.addEventListener('input', function() {
            var searchText = this.value.toLowerCase();
            console.log('Búsqueda en tabla de prospectos:', searchText);

            var rows = document.querySelectorAll('#ejecutivos-table tbody tr');
            rows.forEach(function(row) {
                var text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchText) ? '' : 'none';
            });
        });
    }

    // Configurar búsqueda en tabla de registros directamente
    var tableSearch = document.getElementById('tableSearch');
    if (tableSearch) {
        console.log('Configurando búsqueda para tabla de registros (script inline)');

        tableSearch.addEventListener('input', function() {
            var searchText = this.value.toLowerCase();
            console.log('Búsqueda en tabla de registros:', searchText);

            var rows = document.querySelectorAll('#user-table tbody tr');
            rows.forEach(function(row) {
                var text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchText) ? '' : 'none';
            });
        });
    }

    // Configurar botón de Nuevo Prospecto
    var openProspectoModal = document.getElementById('openProspectoModal');
    var prospectoModal = document.getElementById('prospectoModal');
    if (openProspectoModal && prospectoModal) {
        console.log('Configurando botón de Nuevo Prospecto (script inline)');

        openProspectoModal.addEventListener('click', function() {
            console.log('Botón Nuevo Prospecto clickeado (script inline)');
            prospectoModal.style.display = 'block';
        });

        // Cerrar el modal con el botón X
        var closeModalBtn = prospectoModal.querySelector('.close-modal');
        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', function() {
                console.log('Cerrando modal (script inline)');
                prospectoModal.style.display = 'none';
            });
        }

        // Cerrar el modal al hacer clic fuera de él
        window.addEventListener('click', function(event) {
            if (event.target === prospectoModal) {
                console.log('Cerrando modal (clic fuera) (script inline)');
                prospectoModal.style.display = 'none';
            }
        });

        // Configurar formulario de prospectos
        var formEjecutivos = document.getElementById('formEjecutivos');
        if (formEjecutivos) {
            console.log('Configurando formulario de prospectos (script inline)');

            // Eliminar cualquier manejador de eventos previo para evitar duplicación
            formEjecutivos.removeEventListener('submit', formEjecutivosSubmitHandler);

            // Definir el manejador de eventos como una función con nombre para poder eliminarlo
            function formEjecutivosSubmitHandler(e) {
                e.preventDefault();
                console.log('Formulario de prospectos enviado (script inline)');

                if (!confirm('¿Está seguro de guardar este prospecto?')) {
                    return false;
                }

                // Mostrar indicador de carga
                $('<div class="loading-overlay"><div class="spinner"></div></div>').appendTo('body');

                // Obtener datos del formulario
                var formData = new FormData(this);

                // Convertir FormData a objeto para depuración
                var formDataObj = {};
                formData.forEach(function(value, key) {
                    formDataObj[key] = value;
                });
                console.log('Datos del formulario:', formDataObj);

                // Enviar mediante fetch para evitar problemas con jQuery
                fetch(formEjecutivos.getAttribute('action'), {
                    method: 'POST',
                    body: new URLSearchParams(new FormData(formEjecutivos))
                })
                .then(function(response) {
                    return response.json();
                })
                .then(function(data) {
                    console.log('Respuesta del servidor:', data);

                    // Eliminar indicador de carga
                    $('.loading-overlay').remove();

                    if (data.success) {
                        // Mostrar mensaje de éxito
                        alert(data.message);

                        // Cerrar el modal
                        prospectoModal.style.display = 'none';

                        // Limpiar el formulario
                        formEjecutivos.reset();

                        // Si el usuario tiene permisos, actualizar la tabla
                        if (window.userIsAdmin) {
                            setTimeout(function() {
                                if (typeof cargarEjecutivos === 'function') {
                                    cargarEjecutivos();
                                } else {
                                    // Recargar la página como alternativa
                                    window.location.reload();
                                }
                            }, 500);
                        }
                    } else {
                        // Mostrar mensaje de error
                        alert(data.message || 'Error al guardar el prospecto');
                    }
                })
                .catch(function(error) {
                    console.error('Error al enviar formulario:', error);
                    $('.loading-overlay').remove();
                    alert('Error al enviar el formulario. Revise la consola para más detalles.');
                });
            }

            // Agregar el manejador de eventos
            formEjecutivos.addEventListener('submit', formEjecutivosSubmitHandler);
        }
    }

    // Configurar botón de Descargar Prospectos
    var exportEjecutivos = document.getElementById('exportEjecutivos');
    if (exportEjecutivos) {
        console.log('Configurando botón de Descargar Prospectos (script inline)');

        $("#exportEjecutivos").off('click').on("click", function(e) {
            e.preventDefault();
            console.log('Botón Descargar Prospectos clickeado (script inline)');

            // Mostrar indicador de carga
            $('<div class="loading-overlay"><div class="spinner"></div></div>').appendTo('body');

            // Usar ruta relativa en lugar de absoluta
            $.ajax({
                url: 'endpoints/exportar_prospectos.php', // Asegúrate que esta ruta sea correcta
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // Exportar a Excel
                        var wb = XLSX.utils.book_new();
                        var ws = XLSX.utils.json_to_sheet(response.data);
                        XLSX.utils.book_append_sheet(wb, ws, "Prospectos");
                        XLSX.writeFile(wb, "Prospectos_" + new Date().toISOString().slice(0,10) + ".xlsx");
                        alert("Archivo descargado correctamente");
                    } else {
                        alert("Error: " + response.message);
                    }
                    $('.loading-overlay').remove();
                },
                error: function(xhr, status, error) {
                    console.error("Error AJAX en exportación: ", xhr.responseText);
                    alert("Error al exportar: verifique la ruta del endpoint");
                    $('.loading-overlay').remove();
                }
            });
        });
    }

    // Configurar botones de bitácora - usar un ID único para este manejador de eventos
    if (!window.bitacoraButtonHandlerAdded) {
        window.bitacoraButtonHandlerAdded = true;
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('btn-bitacora') || e.target.closest('.btn-bitacora')) {
            var button = e.target.classList.contains('btn-bitacora') ? e.target : e.target.closest('.btn-bitacora');
            var rut = button.getAttribute('data-rut');
            var nombre = button.getAttribute('data-nombre');
            var razon = button.getAttribute('data-razon');

            console.log('Botón de bitácora clickeado para RUT:', rut, '(script inline)');

            var bitacoraModal = document.getElementById('bitacoraModal');
            var bitacoraRut = document.getElementById('bitacora_rut');
            var bitacoraInfo = document.getElementById('bitacora-info');

            if (bitacoraModal && bitacoraRut && bitacoraInfo) {
                // Establecer información en el modal
                bitacoraRut.value = rut;
                bitacoraInfo.innerHTML = '<strong>Cliente:</strong> ' + razon + ' | <strong>Ejecutivo:</strong> ' + nombre + ' | <strong>RUT:</strong> ' + rut;

                // Cargar registros de bitácora
                if (typeof cargarBitacora === 'function') {
                    cargarBitacora(rut);
                } else {
                    // Implementación alternativa si la función no está disponible
                    console.log('Función cargarBitacora no disponible, implementando alternativa');

                    // Mostrar indicador de carga
                    $('#bitacora-timeline').html('<div class="loading-data">Cargando actividades...</div>');

                    // Cargar registros mediante AJAX con más información de depuración
                    console.log('Intentando cargar bitácora para RUT:', rut);

                    // Mostrar indicador de carga
                    $('#bitacora-timeline').html('<div class="loading-data">Cargando actividades...</div>');

                    // Construir la URL completa para depuración
                    var bitacoraUrl = 'endpoints/obtener_bitacora.php?rut=' + encodeURIComponent(rut);
                    console.log('URL de la bitácora:', bitacoraUrl);

                    // Definir el flujo de estados en orden
                    var flujoEstados = [
                        "Envio información",
                        "Negociación",
                        "Cerrado",
                        "B.O. Experian",
                        "Proceso de Firma",
                        "Firmado",
                        "Habilitado"
                    ];

                    $.ajax({
                        url: bitacoraUrl,
                        type: 'GET',
                        dataType: 'json',
                        cache: false,
                        beforeSend: function(xhr) {
                            console.log('Enviando solicitud AJAX...');
                        },
                        success: function(response) {
                            console.log('Respuesta recibida:', response);
                            if (response && response.success) {
                                // Filtrar las opciones del select según el último estado
                                if (response.ultimo_estado) {
                                  var selectEstado = document.getElementById('bitacora_estado');
                                  var indiceUltimo = flujoEstados.indexOf(response.ultimo_estado);

                                  if (indiceUltimo !== -1) {
                                    // Guardar todas las opciones originales si no lo hemos hecho antes
                                    if (!selectEstado.dataset.opcionesOriginales) {
                                      var opcionesOriginales = [];
                                      for (var i = 0; i < selectEstado.options.length; i++) {
                                        opcionesOriginales.push({
                                          value: selectEstado.options[i].value,
                                          text: selectEstado.options[i].text
                                        });
                                      }
                                      selectEstado.dataset.opcionesOriginales = JSON.stringify(opcionesOriginales);
                                    }

                                    // Limpiar el select
                                    selectEstado.innerHTML = '<option value="">Seleccione...</option>';

                                    // Agregar solo las opciones válidas (igual o posterior al último estado)
                                    for (var j = indiceUltimo; j < flujoEstados.length; j++) {
                                      var option = document.createElement('option');
                                      option.value = flujoEstados[j];
                                      option.text = flujoEstados[j];
                                      selectEstado.appendChild(option);
                                    }

                                    console.log('Select de estados filtrado según último estado:', response.ultimo_estado);
                                  }
                                } else {
                                  // Si no hay último estado, restaurar todas las opciones originales
                                  var selectEstado = document.getElementById('bitacora_estado');
                                  if (selectEstado.dataset.opcionesOriginales) {
                                    var opcionesOriginales = JSON.parse(selectEstado.dataset.opcionesOriginales);
                                    selectEstado.innerHTML = '<option value="">Seleccione...</option>';
                                    opcionesOriginales.forEach(function(opcion) {
                                      if (opcion.value) { // Ignorar la opción vacía
                                        var option = document.createElement('option');
                                        option.value = opcion.value;
                                        option.text = opcion.text;
                                        selectEstado.appendChild(option);
                                      }
                                    });
                                  }
                                }

                                if (response.data.length === 0) {
                                    $('#bitacora-timeline').html('<div class="no-data">No hay registros disponibles</div>');
                                    console.log('No hay registros disponibles');
                                } else {
                                    let html = '';
                                    $.each(response.data, function(index, registro) {
                                        // Determinar la clase de estado para el color del punto
                                        let estadoClass = 'estado-default';
                                        if (registro.estado.toLowerCase().includes('pendiente')) {
                                          estadoClass = 'estado-pendiente';
                                        } else if (registro.estado.toLowerCase().includes('proceso') || registro.estado.toLowerCase().includes('en curso')) {
                                          estadoClass = 'estado-en-proceso';
                                        } else if (registro.estado.toLowerCase().includes('completado') || registro.estado.toLowerCase().includes('finalizado')) {
                                          estadoClass = 'estado-completado';
                                        } else if (registro.estado.toLowerCase().includes('cancelado') || registro.estado.toLowerCase().includes('rechazado')) {
                                          estadoClass = 'estado-cancelado';
                                        }

                                        // Formatear la fecha para mostrar en formato corto
                                        let fechaRegistro = new Date(registro.fecha_registro);
                                        let fechaFormateada = fechaRegistro.toLocaleDateString('es-CL') + ' ' +
                                                            fechaRegistro.toLocaleTimeString('es-CL', {hour: '2-digit', minute:'2-digit'});

                                        html += '<div class="timeline-item ' + estadoClass + '">';
                                        html += '  <div class="timeline-content">';
                                        html += '    <div class="timeline-header">';
                                        html += '      <div class="timeline-text"><span class="highlight">' + registro.estado + '</span></div>';
                                        html += '      <div class="timeline-time">' + fechaFormateada + '</div>';
                                        html += '    </div>';
                                        html += '    <div class="timeline-subtext">' + registro.observaciones + '</div>';
                                        html += '    <div class="timeline-footer">';
                                        html += '      <div>Registrado por: <span class="highlight">' + (registro.nombre_usuario || 'N/A') + '</span></div>';
                                        html += '    </div>';
                                        html += '  </div>';
                                        html += '</div>';
                                    });
                                    $('#bitacora-timeline').html(html);
                                    console.log('Registros cargados correctamente:', response.data.length);
                                }
                            } else {
                                $('#bitacora-timeline').html('<div class="error-data">Error: ' + (response ? response.message : 'Respuesta inválida') + '</div>');
                                console.error('Error al cargar bitácora:', response ? response.message : 'Respuesta inválida');
                            }
                        },
                        error: function(xhr, status, error) {
                            $('#bitacora-timeline').html('<div class="error-data">Error de conexión: ' + status + '</div>');
                            console.error('Error AJAX al cargar bitácora:', {
                                status: status,
                                error: error,
                                response: xhr.responseText,
                                url: bitacoraUrl
                            });

                            // Intentar analizar la respuesta para obtener más información
                            try {
                                if (xhr.responseText) {
                                    var errorResponse = JSON.parse(xhr.responseText);
                                    console.error('Detalles del error:', errorResponse);
                                }
                            } catch (e) {
                                console.error('No se pudo analizar la respuesta de error:', xhr.responseText);
                            }
                        }
                    });
                }

                // Mostrar el modal
                bitacoraModal.style.display = 'block';

                // Configurar cierre del modal
                var closeBitacoraBtn = bitacoraModal.querySelector('.close-modal');
                if (closeBitacoraBtn) {
                    closeBitacoraBtn.addEventListener('click', function() {
                        console.log('Cerrando modal de bitácora (script inline)');
                        bitacoraModal.style.display = 'none';
                    });
                }

                // Cerrar el modal al hacer clic fuera de él
                window.addEventListener('click', function(event) {
                    if (event.target === bitacoraModal) {
                        console.log('Cerrando modal de bitácora (clic fuera) (script inline)');
                        bitacoraModal.style.display = 'none';
                    }
                });
            }
        }
    });
    }

    // Configurar formulario de bitácora
    var formBitacora = document.getElementById('formBitacora');
    if (formBitacora) {
        console.log('Configurando formulario de bitácora (script inline)');

        // Eliminar todos los manejadores de eventos previos para evitar duplicación
        var formClone = formBitacora.cloneNode(true);
        formBitacora.parentNode.replaceChild(formClone, formBitacora);
        formBitacora = formClone;
        formBitacora.id = 'formBitacora';

        // Agregar un nuevo manejador de eventos
        $("#formBitacora").off('submit').on("submit", function(e) {
            e.preventDefault();
            var formData = $(this).serialize();
            var rutEjecutivo = $("#bitacora_rut").val();

            // Mostrar overlay
            $('<div class="loading-overlay"><div class="spinner"></div></div>').appendTo('body');

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        alert(response.message);
                        $("#formBitacora")[0].reset();
                        $("#bitacoraModal").hide();
                        
                        // Llamar directamente a obtener_prospectos.php sin intermediarios
                        $.ajax({
                            url: 'endpoints/obtener_prospectos.php', 
                            type: 'GET',
                            dataType: 'json',
                            data: { t: new Date().getTime() }, // Evitar caché
                            success: function(tableData) {
                                console.log('ACTUALIZAR TABLA: Datos recibidos', tableData);
                                if (tableData.success) {
                                    actualizarTablaProspectos(tableData.data);
                                }
                            }
                        });
                    } else {
                        alert("Error: " + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error:", xhr.responseText);
                    // Usar el sistema de notificaciones en lugar de alert
                    mostrarMensaje("Error al guardar el registro", "error", 6000);
                },
                complete: function() {
                    $('.loading-overlay').remove();
                }
            });
        });
    }
});
</script>

<!-- Script para reemplazar alerts con notificaciones estilizadas -->
<script>
// Reemplazar el alert para mostrar notificaciones estilizadas en formularios
$(document).ready(function() {
    // Reemplazar la función de manejo de formulario para venta (formulario principal)
    var originalFormExperian = $('#formExperian').off('submit');
    $('#formExperian').on('submit', function(e) {
        e.preventDefault();
        
        // Validación del formulario
        if (typeof validarFormularioVenta === 'function' && !validarFormularioVenta()) {
            return false;
        }
        
        // Confirmar antes de enviar
        if (!confirm('¿Está seguro de guardar esta venta?')) {
            return false;
        }
        
        // Deshabilitar botón de envío
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true);
        
        // Mostrar indicador de carga
        $('<div class="loading-overlay"><div class="spinner"></div></div>').appendTo('body');
        
        // Usar FormData para incluir archivos
        var formData = new FormData(this);
        
        // Para debugging
        console.log('Enviando datos del formulario...');
        
        $.ajax({
            url: 'guardar_formulario.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                console.log('Respuesta recibida:', response);
                if (response.success) {
                    // Usar el nuevo sistema de notificaciones en lugar de alert
                    mostrarMensaje(response.message, 'success', 6000);
                    
                    // Limpiar el formulario
                    if (typeof limpiarFormularioVenta === 'function') {
                        limpiarFormularioVenta();
                    } else {
                        $('#formExperian')[0].reset();
                    }
                    
                    // Volver a la primera sección
                    if (typeof showManualFormSection === 'function') {
                        showManualFormSection(1);
                    }
                } else {
                    // Notificación de error
                    mostrarMensaje('Error: ' + response.message, 'error', 8000);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error al enviar formulario:', {
                    status: status,
                    error: error,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText
                });
                
                // Verificar si hay respuesta del servidor
                let mensaje = 'Error al guardar los datos';
                
                try {
                    if (xhr.responseText) {
                        const jsonResponse = JSON.parse(xhr.responseText);
                        if (jsonResponse && jsonResponse.message) {
                            mensaje = jsonResponse.message;
                        }
                    }
                } catch (e) {
                    console.log('Error al parsear respuesta:', e);
                }
                
                // Mostrar notificación de error
                mostrarMensaje(mensaje, 'error', 8000);
            },
            complete: function() {
                // Habilitar botón de envío
                submitBtn.prop('disabled', false);
                // Quitar indicador de carga
                $('.loading-overlay').remove();
            }
        });
    });
});
</script>

</body>
</html>



