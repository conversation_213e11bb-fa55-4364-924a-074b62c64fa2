<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="APP TQW">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>APP TQW</title>

  <!-- Favicon -->
  <link rel="icon" href="img/core-img/favicon.ico">
  <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
  <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
  <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
  <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Dark mode switching -->
  <div class="dark-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="dark-mode-text text-center">
        <i class="bi bi-moon"></i>
        <p class="mb-0">Switching to dark mode</p>
      </div>
      <div class="light-mode-text text-center">
        <i class="bi bi-brightness-high"></i>
        <p class="mb-0">Switching to light mode</p>
      </div>
    </div>
  </div>

  <!-- RTL mode switching -->
  <div class="rtl-mode-switching">
    <div class="d-flex w-100 h-100 align-items-center justify-content-center">
      <div class="rtl-mode-text text-center">
        <i class="bi bi-text-right"></i>
        <p class="mb-0">Switching to RTL mode</p>
      </div>
      <div class="ltr-mode-text text-center">
        <i class="bi bi-text-left"></i>
        <p class="mb-0">Switching to default mode</p>
      </div>
    </div>
  </div>

  <!-- Setting Popup Overlay -->
  <div id="setting-popup-overlay"></div>

  <!-- Setting Popup Card -->
  <div class="card setting-popup-card shadow-lg" id="settingCard">
    <div class="card-body">
      <div class="container">
        <div class="setting-heading d-flex align-items-center justify-content-between mb-3">
          <p class="mb-0">Settings</p>
          <div class="btn-close" id="settingCardClose"></div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="availabilityStatus" checked>
            <label class="form-check-label" for="availabilityStatus">Availability status</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="sendMeNotifications" checked>
            <label class="form-check-label" for="sendMeNotifications">Send me notifications</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="darkSwitch">
            <label class="form-check-label" for="darkSwitch">Dark mode</label>
          </div>
        </div>

        <div class="single-setting-panel">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="rtlSwitch">
            <label class="form-check-label" for="rtlSwitch">RTL mode</label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Header Area -->
  <div class="header-area" id="headerArea">
    <div class="container">
      <!-- Header Content -->
      <div class="header-content position-relative d-flex align-items-center justify-content-between">
        <!-- Back Button -->
        <div class="back-button">
          <a href="elements.html">
            <i class="bi bi-arrow-left-short"></i>
          </a>
        </div>

        <!-- Page Title -->
        <div class="page-heading">
          <h6 class="mb-0">Alerts</h6>
        </div>

        <!-- Settings -->
        <div class="setting-wrapper">
          <div class="setting-trigger-btn" id="settingTriggerBtn">
            <i class="bi bi-gear"></i>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="page-content-wrapper py-3">
    <div class="container">
      <!-- Element Heading -->
      <div class="element-heading">
        <h6>Alerts 01</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="alert custom-alert-1 alert-primary alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i>
            Message successfully sent!
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-1 alert-secondary alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i>
            Message successfully sent!
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-1 alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i>
            Message successfully sent!
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-1 alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-x-circle"></i>
            Message not sent!
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-1 alert-warning alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-circle"></i>
            Oops! Message not sent!
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-1 alert-info alert-dismissible fade show" role="alert">
            <i class="bi bi-info-circle"></i>
            Message successfully sent!
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-1 alert-light alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i>
            Message successfully sent!
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-1 alert-dark alert-dismissible fade show mb-0" role="alert">
            <i class="bi bi-check-circle"></i>
            Message successfully sent!
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Alerts 02</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">

          <div class="alert custom-alert-2 alert-primary alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i>
            Message successfully sent!
            <button class="btn btn-close btn-close-white position-relative p-1 ms-auto" type="button"
              data-bs-dismiss="alert" aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-2 alert-secondary alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i>
            Message successfully sent!
            <button class="btn btn-close btn-close-white position-relative p-1 ms-auto" type="button"
              data-bs-dismiss="alert" aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-2 alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i>
            Message successfully sent!
            <button class="btn btn-close btn-close-white position-relative p-1 ms-auto" type="button"
              data-bs-dismiss="alert" aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-2 alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-x-circle"></i>
            Message not sent!
            <button class="btn btn-close btn-close-white position-relative p-1 ms-auto" type="button"
              data-bs-dismiss="alert" aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-2 alert-warning alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-circle"></i>
            Oops! Message not sent!
            <button class="btn btn-close btn-close-white position-relative p-1 ms-auto" type="button"
              data-bs-dismiss="alert" aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-2 alert-info alert-dismissible fade show" role="alert">
            <i class="bi bi-info-circle"></i>
            Message successfully sent!
            <button class="btn btn-close btn-close-white position-relative p-1 ms-auto" type="button"
              data-bs-dismiss="alert" aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-2 alert-light alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i>
            Message successfully sent!
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-2 alert-dark alert-dismissible fade show mb-0" role="alert">
            <i class="bi bi-check-circle"></i>
            Message successfully sent!
            <button class="btn btn-close btn-close-white position-relative p-1 ms-auto" type="button"
              data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Alerts 03</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="alert custom-alert-3 alert-primary alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i>
            <div class="alert-text">
              <h6>Successfully sent!</h6>
              <span>Your email successfully sent!</span>
              <a class="btn btn-sm btn-primary mt-2" href="#">View Details</a>
            </div>
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-3 alert-secondary alert-dismissible fade show" role="alert">
            <i class="bi bi-person-plus"></i>
            <div class="alert-text">
              <h6>New user added</h6>
              <span>Your email successfully sent!</span>
            </div>
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-3 alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-all"></i>
            <div class="alert-text">
              <h6>Payment received!</h6>
              <span>Your email successfully sent!</span>
              <a class="btn btn-sm btn-success mt-2" href="#">View balance</a>
            </div>
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-3 alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-x-circle"></i>
            <div class="alert-text">
              <h6>Oops! something is wrong</h6>
              <span>Your email successfully sent!</span>
            </div>
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-3 alert-warning alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-circle"></i>
            <div class="alert-text">
              <h6>Warning!</h6>
              <span>Your email successfully sent!</span>
            </div>
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-3 alert-info alert-dismissible fade show" role="alert">
            <i class="bi bi-arrow-repeat"></i>
            <div class="alert-text">
              <h6>Update available</h6>
              <span>Your email successfully sent!</span>
              <a class="btn btn-sm btn-creative btn-info mt-2" href="#">Update Now</a>
            </div>
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-3 alert-light alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i>
            <div class="alert-text">
              <h6>Successfully sent!</h6>
              <span>Your email successfully sent!</span>
            </div>
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert custom-alert-3 alert-dark mb-0 alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i>
            <div class="alert-text">
              <h6>Successfully sent!</h6>
              <span>Your email successfully sent!</span>
              <a class="btn btn-sm btn-outline-dark mt-2" href="#">View
                Details</a>
            </div>
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Bootstrap Alerts</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="alert alert-primary" role="alert">A simple primary alert!</div>
          <div class="alert alert-secondary" role="alert">A simple secondary alert!</div>
          <div class="alert alert-success" role="alert">A simple success alert!</div>
          <div class="alert alert-danger" role="alert">A simple danger alert!</div>
          <div class="alert alert-warning" role="alert">A simple warning alert!</div>
          <div class="alert alert-info" role="alert">A simple info alert!</div>
          <div class="alert alert-light" role="alert">A simple light alert!</div>
          <div class="alert mb-0 alert-dark" role="alert">A simple dark alert!</div>
        </div>
      </div>
    </div>

    <!-- Element Heading -->
    <div class="container">
      <div class="element-heading mt-3">
        <h6>Alerts with close icon</h6>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <div class="card-body">
          <div class="alert d-flex align-items-center alert-primary alert-dismissible fade show" role="alert">
            Message successfully sent!
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert d-flex align-items-center alert-secondary alert-dismissible fade show" role="alert">
            Message successfully sent!
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert d-flex align-items-center alert-success alert-dismissible fade show" role="alert">
            Message successfully sent!
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert d-flex align-items-center alert-danger alert-dismissible fade show" role="alert">
            Message successfully sent!
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert d-flex align-items-center alert-warning alert-dismissible fade show" role="alert">
            Message successfully sent!
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert d-flex align-items-center alert-info alert-dismissible fade show" role="alert">
            Message successfully sent!
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert d-flex align-items-center alert-light alert-dismissible fade show" role="alert">
            Message successfully sent!
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>

          <div class="alert d-flex align-items-center mb-0 alert-dark alert-dismissible fade show" role="alert">
            Message successfully sent!
            <button class="btn btn-close position-relative p-1 ms-auto" type="button" data-bs-dismiss="alert"
              aria-label="Close"></button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Nav -->
  <div class="footer-nav-area" id="footerNav">
    <div class="container px-0">
      <!-- Footer Content -->
      <div class="footer-nav position-relative">
        <ul class="h-100 d-flex align-items-center justify-content-between ps-0">
          <li class="active">
            <a href="home.html">
              <i class="bi bi-house"></i>
              <span>Home</span>
            </a>
          </li>

          <li>
            <a href="pages.html">
              <i class="bi bi-collection"></i>
              <span>Pages</span>
            </a>
          </li>

          <li>
            <a href="elements.html">
              <i class="bi bi-folder2-open"></i>
              <span>Elements</span>
            </a>
          </li>

          <li>
            <a href="chat-users.html">
              <i class="bi bi-chat-dots"></i>
              <span>Chat</span>
            </a>
          </li>

          <li>
            <a href="settings.html">
              <i class="bi bi-gear"></i>
              <span>Settings</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/slideToggle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/tiny-slider.js"></script>
  <script src="js/venobox.min.js"></script>
  <script src="js/countdown.js"></script>
  <script src="js/rangeslider.min.js"></script>
  <script src="js/vanilla-dataTables.min.js"></script>
  <script src="js/index.js"></script>
  <script src="js/imagesloaded.pkgd.min.js"></script>
  <script src="js/isotope.pkgd.min.js"></script>
  <script src="js/dark-rtl.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>
</body>

</html>