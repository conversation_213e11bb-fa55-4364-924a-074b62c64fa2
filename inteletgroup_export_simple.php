<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Iniciar sesión
session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario_id'])) {
    header('Location: login.php?error=' . urlencode("Usuario no autenticado."));
    exit;
}

if (!isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    header('Location: login.php?error=' . urlencode("Sin permisos para acceder a InteletGroup."));
    exit;
}

// Incluir conexión a base de datos
require_once 'con_db.php';

// Verificar conexión
if (!isset($mysqli) || $mysqli->connect_error) {
    die("Error de conexión a la base de datos: " . ($mysqli->connect_error ?? "Variable de conexión no disponible"));
}

// Obtener parámetros de filtros
$filtro_ejecutivo = $_GET['ejecutivo'] ?? 'todos';
$filtro_periodo = $_GET['periodo'] ?? 'año';
$filtro_fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-01-01');
$filtro_fecha_fin = $_GET['fecha_fin'] ?? date('Y-12-31');

// Calcular fechas según el periodo seleccionado
switch($filtro_periodo) {
    case 'hoy':
        $filtro_fecha_inicio = date('Y-m-d');
        $filtro_fecha_fin = date('Y-m-d');
        break;
    case 'semana':
        $filtro_fecha_inicio = date('Y-m-d', strtotime('monday this week'));
        $filtro_fecha_fin = date('Y-m-d', strtotime('sunday this week'));
        break;
    case 'mes':
        $filtro_fecha_inicio = date('Y-m-01');
        $filtro_fecha_fin = date('Y-m-t');
        break;
    case 'trimestre':
        $trimestre = ceil(date('n') / 3);
        $filtro_fecha_inicio = date('Y-') . sprintf('%02d', ($trimestre - 1) * 3 + 1) . '-01';
        $filtro_fecha_fin = date('Y-m-t', strtotime($filtro_fecha_inicio . ' +2 months'));
        break;
    case 'año':
        $filtro_fecha_inicio = date('Y-01-01');
        $filtro_fecha_fin = date('Y-12-31');
        break;
}

// Construir condición WHERE para filtros
$where_conditions = ["1=1"];
$params = [];
$types = "";

if ($filtro_ejecutivo !== 'todos') {
    $where_conditions[] = "p.usuario_id = ?";
    $params[] = $filtro_ejecutivo;
    $types .= "i";
}

$where_conditions[] = "DATE(p.fecha_registro) BETWEEN ? AND ?";
$params[] = $filtro_fecha_inicio;
$params[] = $filtro_fecha_fin;
$types .= "ss";

$where_clause = implode(" AND ", $where_conditions);

// Consulta para obtener prospectos con campos correctos según la estructura de la tabla
$query = "
    SELECT
        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
        p.email, p.telefono_celular, p.direccion_comercial, p.fecha_registro,
        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre_usuario,
        COUNT(DISTINCT d.id) as total_documentos,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 AND d.id IS NOT NULL THEN td.id END) as obligatorios_completados,
        COUNT(DISTINCT CASE WHEN td.es_obligatorio = 1 THEN td.id END) as total_obligatorios
    FROM tb_inteletgroup_prospectos p
    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
    LEFT JOIN tb_inteletgroup_tipos_documento td ON
        (td.tipo_persona COLLATE utf8mb4_0900_ai_ci = p.tipo_persona COLLATE utf8mb4_0900_ai_ci
         OR td.tipo_persona = 'Ambos')
        AND td.estado = 'Activo'
    LEFT JOIN tb_inteletgroup_documentos d ON
        p.id = d.prospecto_id
        AND d.tipo_documento_id = td.id
        AND d.estado = 'Activo'
    WHERE " . $where_clause . "
    GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, 
             p.telefono_celular, p.direccion_comercial, p.fecha_registro, u.nombre_usuario
    ORDER BY p.fecha_registro DESC";

// Preparar y ejecutar la consulta
$stmt = $mysqli->prepare($query);
if (!$stmt) {
    die("Error preparando consulta: " . $mysqli->error);
}

if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}

if (!$stmt->execute()) {
    die("Error ejecutando consulta: " . $stmt->error);
}

// Obtener resultados usando bind_result (compatible con PHP 7.3.33)
$id = $tipo_persona = $rut = $razon_social = $rubro = $email = $telefono = $direccion = $fecha = $ejecutivo = $total_docs = $oblig_comp = $total_oblig = null;

$stmt->bind_result(
    $id, $tipo_persona, $rut, $razon_social, $rubro, $email, $telefono, 
    $direccion, $fecha, $ejecutivo, $total_docs, $oblig_comp, $total_oblig
);

// Preparar datos para CSV
$prospectos = [];
while ($stmt->fetch()) {
    $porcentaje = $total_oblig > 0 ? round(($oblig_comp / $total_oblig) * 100) : 0;
    
    $prospectos[] = [
        'ID' => $id,
        'Tipo de Persona' => $tipo_persona,
        'RUT' => $rut,
        'Razón Social' => $razon_social,
        'Rubro' => $rubro,
        'Email' => $email,
        'Teléfono' => $telefono,
        'Dirección' => $direccion,
        'Fecha Registro' => $fecha,
        'Ejecutivo' => $ejecutivo,
        'Total Documentos' => $total_docs,
        'Documentos Obligatorios Completados' => $oblig_comp,
        'Total Documentos Obligatorios' => $total_oblig,
        'Porcentaje Completado' => $porcentaje . '%'
    ];
}

$stmt->close();
$mysqli->close();

// Verificar si hay datos para exportar
if (empty($prospectos)) {
    die("No hay datos para exportar con los filtros seleccionados");
}

// Configurar encabezados para descarga de CSV
$fecha_actual = date('Y-m-d');
$nombre_archivo = "prospectos_inteletgroup_{$fecha_actual}.csv";
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $nombre_archivo . '"');

// Crear el recurso de salida para escribir el CSV
$output = fopen('php://output', 'w');
if (!$output) {
    die("No se pudo abrir el flujo de salida");
}

// Configurar para que funcione con caracteres especiales (acentos, etc)
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Escribir encabezados de las columnas
fputcsv($output, array_keys($prospectos[0]));

// Escribir datos
foreach ($prospectos as $prospecto) {
    fputcsv($output, $prospecto);
}

fclose($output);
exit;
?>
