<?php
// Iniciar sesión y verificar autenticación
session_start();

// Verificar si el usuario está autenticado
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['usuario'])) {
    header('Location: login.php?error=unauthorized');
    exit;
}

// Incluir el archivo de conexión
require_once 'con_db.php';
?>

<!DOCTYPE html>
<html lang="es">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="GESTAR INTRANET - Cambiar Contraseña">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">

  <!-- Cache control headers -->
  <meta http-equiv="Cache-Control" content="no-store, no-cache, must-revalidate, max-age=0">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">

  <meta name="theme-color" content="#0134d4">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">

  <!-- Title -->
  <title>PORTAL GESTAR - CAMBIAR CONTRASEÑA</title>

  <!-- Favicon -->
  <link rel="icon" href="img/icons/logoGestar.ico">
  <link rel="apple-touch-icon" href="img/icons/logoGestar.ico">

  <!-- Style CSS -->
  <link rel="stylesheet" href="style.css">

  <!-- Enhanced Login Styles -->
  <link rel="stylesheet" href="css/enhanced-login.css">

  <!-- Password Creation Styles -->
  <link rel="stylesheet" href="css/password-creation.css">

  <!-- Web App Manifest -->
  <link rel="manifest" href="manifest.json">

  <!-- jQuery -->
  <script src="js/jquery-3.7.1.min.js"></script>

  <!-- Font Awesome for enhanced icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Animate.css for smooth animations -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
</head>

<body>
  <!-- Preloader -->
  <div id="preloader">
    <div class="spinner-grow text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Internet Connection Status -->
  <div class="internet-connection-status" id="internetStatus"></div>

  <!-- Enhanced Password Change Wrapper Area -->
  <div class="enhanced-login-wrapper d-flex align-items-center justify-content-center">
    <div class="login-container animate__animated animate__fadeInUp">

      <!-- Logo Section -->
      <div class="logo-section text-center mb-4">
        <div class="logo-container">
          <img class="login-logo animate__animated animate__pulse animate__infinite" src="logoGestar.png" alt="Gestar Logo">
        </div>
        <h2 class="brand-title">Portal Gestar</h2>
        <p class="brand-subtitle">Cambiar Contraseña</p>
      </div>

      <!-- Password Change Form Card -->
      <div class="login-card">
        <div class="card-header">
          <h4 class="login-title">
            <i class="fas fa-key me-2"></i>
            Cambiar Contraseña
          </h4>
          <p class="login-subtitle">Actualiza tu contraseña de acceso</p>
          <div class="user-info text-center mt-2">
            <i class="fas fa-user me-2"></i>
            <span><?php echo htmlspecialchars($_SESSION['nombre_usuario'] ?? $_SESSION['usuario']); ?></span>
          </div>
        </div>

        <!-- Enhanced Message System -->
        <div class="message-container">
          <!-- Success Message -->
          <div id="success-message" class="enhanced-message success-message" style="display: none;">
            <div class="message-icon">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="message-content">
              <div class="message-title">¡Éxito!</div>
              <div class="message-text"></div>
            </div>
            <div class="message-progress"></div>
          </div>

          <!-- Error Message -->
          <div id="error-message" class="enhanced-message error-message" style="display: none;">
            <div class="message-icon">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="message-content">
              <div class="message-title">Error</div>
              <div class="message-text"></div>
            </div>
            <button class="message-close" onclick="hideMessage('error')">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <!-- Info Message -->
          <div id="info-message" class="enhanced-message info-message" style="display: none;">
            <div class="message-icon">
              <i class="fas fa-info-circle"></i>
            </div>
            <div class="message-content">
              <div class="message-title">Información</div>
              <div class="message-text"></div>
            </div>
            <div class="message-progress"></div>
          </div>

          <!-- Loading Message -->
          <div id="loading-message" class="enhanced-message loading-message" style="display: none;">
            <div class="message-icon">
              <div class="loading-spinner">
                <i class="fas fa-circle-notch fa-spin"></i>
              </div>
            </div>
            <div class="message-content">
              <div class="message-title">Procesando</div>
              <div class="message-text">Cambiando contraseña...</div>
            </div>
            <div class="message-progress loading-progress"></div>
          </div>
        </div>

        <!-- Enhanced Form -->
        <form id="password-change-form" method="POST" class="enhanced-form">
          <!-- Current Password Input -->
          <div class="form-group enhanced-form-group">
            <label for="current-password" class="form-label">
              <i class="fas fa-lock me-2"></i>
              Contraseña Actual
            </label>
            <div class="input-container">
              <input
                class="form-control enhanced-input"
                id="current-password"
                name="current_password"
                type="password"
                placeholder="Ingresa tu contraseña actual"
                required
                autocomplete="current-password"
              >
              <div class="input-icon">
                <i class="fas fa-lock"></i>
              </div>
              <div class="password-toggle" id="current-password-visibility">
                <i class="fas fa-eye"></i>
              </div>
              <div class="input-validation">
                <i class="fas fa-check-circle validation-success"></i>
                <i class="fas fa-exclamation-circle validation-error"></i>
              </div>
            </div>
            <div class="field-feedback"></div>
          </div>

          <!-- New Password Input -->
          <div class="form-group enhanced-form-group">
            <label for="password" class="form-label">
              <i class="fas fa-lock me-2"></i>
              Nueva Contraseña
            </label>
            <div class="input-container">
              <input
                class="form-control enhanced-input"
                id="password"
                name="new_password"
                type="password"
                placeholder="Crea tu nueva contraseña"
                required
                autocomplete="new-password"
              >
              <div class="input-icon">
                <i class="fas fa-lock"></i>
              </div>
              <div class="password-toggle" id="password-visibility">
                <i class="fas fa-eye"></i>
              </div>
              <div class="input-validation">
                <i class="fas fa-check-circle validation-success"></i>
                <i class="fas fa-exclamation-circle validation-error"></i>
              </div>
            </div>
            <div class="field-feedback"></div>
            
            <!-- Password Strength Indicator -->
            <div class="password-strength-container mt-2">
              <div class="password-strength-bar">
                <div class="password-strength-fill" id="password-strength"></div>
              </div>
              <div class="password-strength-text" id="password-strength-text">Fortaleza: -</div>
            </div>

            <!-- Password Requirements -->
            <div class="password-requirements mt-2">
              <div class="requirement" id="req-length">
                <i class="fas fa-times-circle"></i>
                <span>Mínimo 8 caracteres</span>
              </div>
              <div class="requirement" id="req-uppercase">
                <i class="fas fa-times-circle"></i>
                <span>Al menos 1 letra mayúscula</span>
              </div>
              <div class="requirement" id="req-lowercase">
                <i class="fas fa-times-circle"></i>
                <span>Al menos 1 letra minúscula</span>
              </div>
              <div class="requirement" id="req-number">
                <i class="fas fa-times-circle"></i>
                <span>Al menos 1 número</span>
              </div>
              <div class="requirement" id="req-special">
                <i class="fas fa-times-circle"></i>
                <span>Al menos 1 carácter especial (!@#$%^&*)</span>
              </div>
              <div class="requirement" id="req-spaces">
                <i class="fas fa-check-circle"></i>
                <span>Sin espacios</span>
              </div>
            </div>
          </div>

          <!-- Confirm New Password Input -->
          <div class="form-group enhanced-form-group">
            <label for="confirm-password" class="form-label">
              <i class="fas fa-lock me-2"></i>
              Confirmar Nueva Contraseña
            </label>
            <div class="input-container">
              <input
                class="form-control enhanced-input"
                id="confirm-password"
                name="confirm_password"
                type="password"
                placeholder="Confirma tu nueva contraseña"
                required
                autocomplete="new-password"
              >
              <div class="input-icon">
                <i class="fas fa-lock"></i>
              </div>
              <div class="password-toggle" id="confirm-password-visibility">
                <i class="fas fa-eye"></i>
              </div>
              <div class="input-validation">
                <i class="fas fa-check-circle validation-success"></i>
                <i class="fas fa-exclamation-circle validation-error"></i>
              </div>
            </div>
            <div class="field-feedback"></div>
          </div>

          <!-- Submit Button -->
          <button class="btn enhanced-submit-btn w-100" type="submit" id="submit-btn" disabled>
            <span class="btn-content">
              <i class="fas fa-save me-2"></i>
              <span class="btn-text">Cambiar Contraseña</span>
            </span>
            <div class="btn-loading" style="display: none;">
              <i class="fas fa-circle-notch fa-spin me-2"></i>
              <span>Procesando...</span>
            </div>
          </button>

          <!-- Back to Dashboard Link -->
          <div class="text-center mt-3">
            <a href="<?php echo $_SESSION['proyecto'] === 'inteletGroup' ? 'form_inteletgroup.php' : 'form_experian2.php'; ?>" class="text-decoration-none">
              <i class="fas fa-arrow-left me-2"></i>
              Volver al panel principal
            </a>
          </div>
        </form>

        <!-- Additional Info -->
        <div class="login-footer">
          <div class="security-info">
            <i class="fas fa-shield-alt me-2"></i>
            <span>Conexión segura SSL</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- All JavaScript Files -->
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="js/internet-status.js"></script>
  <script src="js/active.js"></script>
  <script src="js/pwa.js"></script>

  <script>
    // Enhanced Message System Functions
    function showMessage(type, title, message, duration = 5000) {
      // Hide all messages first
      $('.enhanced-message').removeClass('show').addClass('hide');

      setTimeout(() => {
        $('.enhanced-message').hide();

        // Show the specific message
        const messageElement = $(`#${type}-message`);
        messageElement.find('.message-title').text(title);
        messageElement.find('.message-text').text(message);

        messageElement.show().removeClass('hide').addClass('show animate__animated animate__slideInDown');

        // Auto-hide for success and info messages
        if (type === 'success' || type === 'info') {
          const progressBar = messageElement.find('.message-progress');
          progressBar.css('animation', `progress-bar ${duration}ms linear`);

          setTimeout(() => {
            hideMessage(type);
          }, duration);
        }
      }, 100);
    }

    function hideMessage(type) {
      const messageElement = $(`#${type}-message`);
      messageElement.removeClass('show').addClass('hide animate__animated animate__slideOutUp');

      setTimeout(() => {
        messageElement.hide().removeClass('animate__animated animate__slideOutUp');
      }, 500);
    }

    function showLoading(message = 'Cambiando contraseña...') {
      $('.enhanced-message').removeClass('show').addClass('hide');

      setTimeout(() => {
        $('.enhanced-message').hide();

        const loadingElement = $('#loading-message');
        loadingElement.find('.message-text').text(message);
        loadingElement.show().removeClass('hide').addClass('show animate__animated animate__slideInDown');
      }, 100);
    }

    function hideLoading() {
      hideMessage('loading');
    }

    // Password Requirements
    const passwordRequirements = {
        minLength: 8,
        hasUppercase: /[A-Z]/,
        hasLowercase: /[a-z]/,
        hasNumber: /[0-9]/,
        hasSpecial: /[!@#$%^&*]/,
        noSpaces: /^\S*$/
    };

    // Check password strength
    function checkPasswordStrength(password) {
        let strength = 0;
        let passed = {
            length: false,
            uppercase: false,
            lowercase: false,
            number: false,
            special: false,
            spaces: true
        };

        // Check length
        if (password.length >= passwordRequirements.minLength) {
            strength += 20;
            passed.length = true;
        }

        // Check uppercase
        if (passwordRequirements.hasUppercase.test(password)) {
            strength += 20;
            passed.uppercase = true;
        }

        // Check lowercase
        if (passwordRequirements.hasLowercase.test(password)) {
            strength += 20;
            passed.lowercase = true;
        }

        // Check number
        if (passwordRequirements.hasNumber.test(password)) {
            strength += 20;
            passed.number = true;
        }

        // Check special character
        if (passwordRequirements.hasSpecial.test(password)) {
            strength += 20;
            passed.special = true;
        }

        // Check for spaces
        if (!passwordRequirements.noSpaces.test(password)) {
            passed.spaces = false;
            strength = Math.max(0, strength - 20);
        }

        return {
            strength: strength,
            passed: passed,
            allPassed: strength === 100
        };
    }

    // Update password strength indicator
    function updatePasswordStrength(password) {
        const result = checkPasswordStrength(password);
        const strengthBar = $('#password-strength');
        const strengthText = $('#password-strength-text');

        // Update strength bar
        strengthBar.removeClass('weak fair good strong');
        strengthText.removeClass('weak fair good strong');

        if (result.strength <= 25) {
            strengthBar.addClass('weak');
            strengthText.addClass('weak').text('Fortaleza: Débil');
        } else if (result.strength <= 50) {
            strengthBar.addClass('fair');
            strengthText.addClass('fair').text('Fortaleza: Regular');
        } else if (result.strength <= 75) {
            strengthBar.addClass('good');
            strengthText.addClass('good').text('Fortaleza: Buena');
        } else {
            strengthBar.addClass('strong');
            strengthText.addClass('strong').text('Fortaleza: Fuerte');
        }

        // Update requirements checklist
        updateRequirementsChecklist(result.passed);

        return result;
    }

    // Update requirements checklist
    function updateRequirementsChecklist(passed) {
        // Length requirement
        updateRequirement('#req-length', passed.length);

        // Uppercase requirement
        updateRequirement('#req-uppercase', passed.uppercase);

        // Lowercase requirement
        updateRequirement('#req-lowercase', passed.lowercase);

        // Number requirement
        updateRequirement('#req-number', passed.number);

        // Special character requirement
        updateRequirement('#req-special', passed.special);

        // No spaces requirement
        updateRequirement('#req-spaces', passed.spaces);
    }

    // Update individual requirement
    function updateRequirement(selector, isPassed) {
        const requirement = $(selector);
        const icon = requirement.find('i');

        if (isPassed) {
            requirement.addClass('valid').removeClass('invalid');
            icon.removeClass('fa-times-circle').addClass('fa-check-circle');
        } else {
            requirement.addClass('invalid').removeClass('valid');
            icon.removeClass('fa-check-circle').addClass('fa-times-circle');
        }
    }

    // Check if passwords match
    function checkPasswordsMatch() {
        const password = $('#password').val();
        const confirmPassword = $('#confirm-password').val();
        const container = $('#confirm-password').closest('.input-container');
        const feedback = $('#confirm-password').closest('.enhanced-form-group').find('.field-feedback');

        if (confirmPassword.length === 0) {
            container.removeClass('valid invalid');
            feedback.text('');
            return false;
        }

        if (password === confirmPassword) {
            container.removeClass('invalid').addClass('valid');
            feedback.text('Las contraseñas coinciden').removeClass('error').addClass('success');
            return true;
        } else {
            container.removeClass('valid').addClass('invalid');
            feedback.text('Las contraseñas no coinciden').addClass('error').removeClass('success');
            return false;
        }
    }

    // Validate current password
    function validateCurrentPassword() {
        const currentPassword = $('#current-password').val();
        const container = $('#current-password').closest('.input-container');
        const feedback = $('#current-password').closest('.enhanced-form-group').find('.field-feedback');

        if (currentPassword.length === 0) {
            container.removeClass('valid invalid');
            feedback.text('');
            return false;
        }

        if (currentPassword.length >= 3) {
            container.removeClass('invalid').addClass('valid');
            feedback.text('').removeClass('error');
            return true;
        } else {
            container.removeClass('valid').addClass('invalid');
            feedback.text('La contraseña debe tener al menos 3 caracteres').addClass('error');
            return false;
        }
    }

    // Check if form is valid
    function checkFormValidity() {
        const currentPasswordValid = validateCurrentPassword();
        const password = $('#password').val();
        const confirmPassword = $('#confirm-password').val();

        const passwordResult = checkPasswordStrength(password);
        const passwordsMatch = checkPasswordsMatch();

        const isValid = currentPasswordValid && passwordResult.allPassed && passwordsMatch;

        // Enable/disable submit button
        $('#submit-btn').prop('disabled', !isValid);

        return isValid;
    }

    // Input icon visibility function
    function updateInputIconVisibility(input) {
      const container = input.closest('.input-container');
      const value = input.val().trim();
      const isFocused = input.is(':focus');

      // Show icon if input has content or is focused
      if (value.length > 0 || isFocused) {
        container.addClass('has-content');
      } else {
        container.removeClass('has-content');
      }
    }

    $(document).ready(function() {
      // Password visibility toggles
      $('#current-password-visibility').on('click', function() {
        const passwordInput = $('#current-password');
        const icon = $(this).find('i');
        const type = passwordInput.attr('type') === 'password' ? 'text' : 'password';

        passwordInput.attr('type', type);

        if (type === 'text') {
          icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
          icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }

        // Add animation
        $(this).addClass('animate__animated animate__pulse');
        setTimeout(() => {
          $(this).removeClass('animate__animated animate__pulse');
        }, 600);
      });

      $('#password-visibility').on('click', function() {
        const passwordInput = $('#password');
        const icon = $(this).find('i');
        const type = passwordInput.attr('type') === 'password' ? 'text' : 'password';

        passwordInput.attr('type', type);

        if (type === 'text') {
          icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
          icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }

        // Add animation
        $(this).addClass('animate__animated animate__pulse');
        setTimeout(() => {
          $(this).removeClass('animate__animated animate__pulse');
        }, 600);
      });

      $('#confirm-password-visibility').on('click', function() {
        const passwordInput = $('#confirm-password');
        const icon = $(this).find('i');
        const type = passwordInput.attr('type') === 'password' ? 'text' : 'password';

        passwordInput.attr('type', type);

        if (type === 'text') {
          icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
          icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }

        // Add animation
        $(this).addClass('animate__animated animate__pulse');
        setTimeout(() => {
          $(this).removeClass('animate__animated animate__pulse');
        }, 600);
      });

      // Enhanced form focus effects with icon visibility
      $('.enhanced-input').on('focus', function() {
        $(this).closest('.input-container').addClass('focused');
        updateInputIconVisibility($(this));
      }).on('blur', function() {
        $(this).closest('.input-container').removeClass('focused');
        updateInputIconVisibility($(this));
      });

      // Initialize input icon visibility on page load
      $('.enhanced-input').each(function() {
        updateInputIconVisibility($(this));
      });

      // Current password input events
      $('#current-password').on('input blur', function() {
        validateCurrentPassword();
        checkFormValidity();
      });

      // Password input events
      $('#password').on('input', function() {
        const password = $(this).val();
        const container = $(this).closest('.input-container');
        const feedback = $(this).closest('.enhanced-form-group').find('.field-feedback');

        if (password.length === 0) {
            container.removeClass('valid invalid');
            feedback.text('');
            // Reset strength indicator
            $('#password-strength').removeClass('weak fair good strong').css('width', '0%');
            $('#password-strength-text').text('Fortaleza: -');
            // Reset requirements
            $('.requirement').removeClass('valid invalid');
            $('.requirement i').removeClass('fa-check-circle').addClass('fa-times-circle');
        } else {
            const result = updatePasswordStrength(password);
            
            if (result.allPassed) {
                container.removeClass('invalid').addClass('valid');
                feedback.text('Contraseña segura').removeClass('error').addClass('success');
            } else {
                container.removeClass('valid').addClass('invalid');
                feedback.text('La contraseña no cumple todos los requisitos').addClass('error').removeClass('success');
            }
        }

        // Check passwords match if confirm password has value
        if ($('#confirm-password').val().length > 0) {
            checkPasswordsMatch();
        }

        checkFormValidity();
      });

      // Confirm password input events
      $('#confirm-password').on('input', function() {
        checkPasswordsMatch();
        checkFormValidity();
      });

      // Enhanced form submission handler
      $('#password-change-form').on('submit', function(e) {
        e.preventDefault();

        // Validate all inputs
        if (!$('#submit-btn').prop('disabled')) {
          // Show loading state
          showLoading('Cambiando contraseña...');

          // Enhanced button state
          const submitBtn = $('#submit-btn');
          submitBtn.prop('disabled', true).addClass('loading');
          submitBtn.find('.btn-content').hide();
          submitBtn.find('.btn-loading').show();

          // Get form values
          const currentPassword = $('#current-password').val();
          const newPassword = $('#password').val();
          const confirmPassword = $('#confirm-password').val();

          // Add form shake animation for better UX
          $('.login-card').addClass('animate__animated animate__pulse');

          // Create FormData for sending data
          const formData = new FormData();
          formData.append('action', 'change_password');
          formData.append('current_password', currentPassword);
          formData.append('new_password', newPassword);
          formData.append('confirm_password', confirmPassword);

          // Configure timeout for the request
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 seconds timeout

          // Make the fetch request
          fetch('password_controller.php', {
            method: 'POST',
            body: formData,
            signal: controller.signal,
            cache: 'no-store'
          })
          .then(response => {
            clearTimeout(timeoutId);
            
            // Check if response is OK
            if (!response.ok) {
              throw new Error(`Error HTTP: ${response.status} ${response.statusText}`);
            }
            
            // Check content type
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
              throw new Error(`Tipo de contenido inesperado: ${contentType}`);
            }
            
            return response.json();
          })
          .then(data => {
            console.log('Respuesta del servidor:', data);
            hideLoading();

            if (data.success) {
              // Password changed successfully
              showMessage('success', '¡Éxito!', 'Contraseña cambiada correctamente. Redirigiendo...', 3000);

              // Enhanced success animation
              $('.login-card').removeClass('animate__pulse').addClass('animate__bounceIn');
              submitBtn.removeClass('loading').addClass('success');

              // Redirect to appropriate dashboard after delay
              setTimeout(function() {
                const redirectUrl = '<?php echo $_SESSION['proyecto'] === 'inteletGroup' ? 'form_inteletgroup.php' : 'form_experian2.php'; ?>';
                window.location.href = redirectUrl;
              }, 3000);
            } else {
              // Error changing password
              showMessage('error', 'Error', data.message || 'No se pudo cambiar la contraseña. Intente nuevamente.');

              // Enhanced error animation
              $('.login-card').removeClass('animate__pulse').addClass('animate__shakeX');
              setTimeout(() => {
                $('.login-card').removeClass('animate__animated animate__shakeX');
              }, 1000);

              // Reset button state
              resetSubmitButton();
            }
          })
          .catch(error => {
            clearTimeout(timeoutId);
            hideLoading();

            // Enhanced error handling with specific messages
            let errorTitle = 'Error de Conexión';
            let errorMessage = '';

            if (error.name === 'AbortError') {
              errorTitle = 'Tiempo Agotado';
              errorMessage = 'La solicitud ha excedido el tiempo de espera. Por favor inténtelo nuevamente.';
            } else if (error.message.includes('Failed to fetch')) {
              errorTitle = 'Sin Conexión';
              errorMessage = 'No se pudo conectar con el servidor. Verifique su conexión a internet.';
            } else {
              errorMessage = 'Error en la conexión: ' + error.message;
            }

            showMessage('error', errorTitle, errorMessage);

            // Enhanced error animation
            $('.login-card').removeClass('animate__pulse').addClass('animate__shakeX');
            setTimeout(() => {
              $('.login-card').removeClass('animate__animated animate__shakeX');
            }, 1000);

            resetSubmitButton();
          });
        }
      });

      // Initialize form state
      checkFormValidity();
    });

    // Helper function to reset submit button
    function resetSubmitButton() {
      const submitBtn = $('#submit-btn');
      submitBtn.prop('disabled', false).removeClass('loading success');
      submitBtn.find('.btn-content').show();
      submitBtn.find('.btn-loading').hide();
    }
  </script>
</body>

</html>