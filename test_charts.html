<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Chart.js</title>
    <style>
        .chart-container {
            width: 400px;
            height: 300px;
            margin: 20px;
            border: 1px solid #ccc;
            padding: 10px;
            background-color: #f9f9f9;
        }
        #testChart {
            width: 100% !important;
            height: 250px !important;
            border: 1px solid red;
        }
    </style>
</head>
<body>
    <h1>Test Chart.js</h1>
    
    <div class="chart-container">
        <h3>Gráfico de Prueba</h3>
        <canvas id="testChart"></canvas>
    </div>

    <!-- Chart.js desde CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        console.log('Iniciando test de Chart.js');
        console.log('Chart disponible:', typeof Chart !== 'undefined');

        // Esperar a que el DOM esté listo
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM cargado');

            if (typeof Chart !== 'undefined') {
                console.log('Chart.js está disponible');

                const canvas = document.getElementById('testChart');
                console.log('Canvas encontrado:', canvas);
                console.log('Canvas dimensiones:', canvas.width, 'x', canvas.height);

                // Datos de prueba
                const testData = {
                    labels: ['Enero', 'Febrero', 'Marzo', 'Abril'],
                    datasets: [{
                        label: 'Datos de Prueba',
                        data: [12, 19, 3, 5],
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.2)',
                            'rgba(54, 162, 235, 0.2)',
                            'rgba(255, 205, 86, 0.2)',
                            'rgba(75, 192, 192, 0.2)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 205, 86, 1)',
                            'rgba(75, 192, 192, 1)'
                        ],
                        borderWidth: 1
                    }]
                };

                try {
                    const ctx = canvas.getContext('2d');
                    console.log('Contexto 2D obtenido:', ctx);

                    const testChart = new Chart(ctx, {
                        type: 'bar',
                        data: testData,
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });

                    console.log('Gráfico de prueba creado exitosamente:', testChart);
                } catch (error) {
                    console.error('Error creando gráfico:', error);
                    document.getElementById('testChart').parentNode.innerHTML = '<p style="color: red;">Error creando gráfico: ' + error.message + '</p>';
                }
            } else {
                console.error('Chart.js no está disponible');
                document.getElementById('testChart').parentNode.innerHTML = '<p style="color: red;">Error: Chart.js no está disponible</p>';
            }
        });
    </script>
</body>
</html>
