<?php
/**
 * Este archivo implementa una versión mejorada de la respuesta al guardar el formulario
 * para incluir mensajes personalizados y más información en la respuesta JSON.
 * 
 * Se debe incluir al final del try {} en guardar_formulario.php
 */

// Obtener los datos más importantes del formulario para personalizar el mensaje
$razonSocial = $_POST['razon_social'] ?? '';
$rutCliente = $_POST['rut'] ?? '';
$tipoCliente = $_POST['tipo_cliente'] ?? '';
$correo = $_POST['email'] ?? '';
$telefono = $_POST['telefono'] ?? '';
$contactoNombre = $_POST['contacto_nombre'] ?? '';
$comuna = $_POST['comuna'] ?? '';
$direccion = $_POST['direccion'] ?? '';

// Construir un mensaje dinámico y personalizado
$mensaje = 'Formulario guardado exitosamente';
if ($razonSocial) {
    $mensaje .= " para <strong>{$razonSocial}</strong>";
}

// Agregar información adicional al mensaje
if ($tipoCliente) {
    $mensaje .= " (Cliente {$tipoCliente})";
}

// Construir la respuesta con información detallada
$responseData = [
    'success' => true,
    'message' => $mensaje,
    'id' => $id_insertado,
    'id_usuario' => $id_usuario,
    'archivos_subidos' => array_keys($rutas_archivos), // Lista de columnas de archivos subidos
    'timestamp' => date('Y-m-d H:i:s'),
    'detalles_cliente' => [
        'razon_social' => $razonSocial,
        'rut' => $rutCliente,
        'tipo_cliente' => $tipoCliente,
        'direccion' => $direccion,
        'comuna' => $comuna
    ],
    'detalles_contacto' => [
        'nombre' => $contactoNombre,
        'email' => $correo,
        'telefono' => $telefono
    ]
];

// Código de referencia para incluir en guardar_formulario.php
?>