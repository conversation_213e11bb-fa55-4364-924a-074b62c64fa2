<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Aumentar límites para evitar timeouts
ini_set('max_execution_time', 300); // 5 minutos
ini_set('memory_limit', '256M');

// Log para debugging
error_log("=== INTELETGROUP_DOCUMENTOS_ENHANCED.PHP INICIADO ===");
error_log("REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'N/A'));
error_log("QUERY_STRING: " . ($_SERVER['QUERY_STRING'] ?? 'N/A'));

// Registrar manejador de errores
set_error_handler(function($severity, $message, $file, $line) {
    error_log("Error en inteletgroup_documentos_enhanced.php: [$severity] $message in $file on line $line");
    
    // Para errores fatales, mostrar página de error
    if ($severity === E_ERROR || $severity === E_PARSE) {
        header('HTTP/1.1 500 Internal Server Error');
        echo "<!DOCTYPE html><html><head><title>Error</title></head><body>";
        echo "<h1>Error Interno del Servidor</h1>";
        echo "<p>Se ha producido un error. Por favor, contacte al administrador.</p>";
        echo "</body></html>";
        exit;
    }
    
    return true; // Continuar con el handler por defecto
});

set_exception_handler(function($exception) {
    error_log("Excepción en inteletgroup_documentos_enhanced.php: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine());
    error_log("Stack trace: " . $exception->getTraceAsString());
    
    header('HTTP/1.1 500 Internal Server Error');
    echo "<!DOCTYPE html><html><head><title>Error</title></head><body>";
    echo "<h1>Error Interno del Servidor</h1>";
    echo "<p>Se ha producido un error. Por favor, contacte al administrador.</p>";
    echo "</body></html>";
    exit;
});

// Verificar que cache_utils.php existe antes de incluirlo
if (!file_exists('cache_utils.php')) {
    error_log("ERROR: cache_utils.php no encontrado");
    die("Error: Archivo de configuración no encontrado.");
}

require_once 'cache_utils.php';

// Iniciar sesión
session_start();
error_log("Sesión iniciada. Usuario ID: " . ($_SESSION['usuario_id'] ?? 'No definido'));

// Aplicar headers anti-caché
no_cache_headers();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    $errorMsg = urlencode("Usuario no autenticado o sin permisos para InteletGroup.");
    header('Location: ' . version_url('login.php?error=' . $errorMsg));
    exit;
}

// Incluir conexión a la base de datos
error_log("Incluyendo con_db.php...");
if (!file_exists('con_db.php')) {
    error_log("ERROR: con_db.php no encontrado");
    die("Error: Archivo de conexión a base de datos no encontrado.");
}
require_once 'con_db.php';
error_log("con_db.php incluido exitosamente");

// Obtener información del usuario logueado
$usuario_id = $_SESSION['usuario_id'];
$nombre_usuario = $_SESSION['nombre_usuario'] ?? 'Usuario';
$proyecto = $_SESSION['proyecto'] ?? 'inteletGroup';

error_log("Usuario: $nombre_usuario (ID: $usuario_id), Proyecto: $proyecto");

// Usar la conexión mysqli del archivo con_db.php
if (!isset($mysqli)) {
    error_log("ERROR: Variable mysqli no está definida después de incluir con_db.php");
    die("Error de conexión a la base de datos. Variable mysqli no definida.");
}

if ($mysqli->connect_error) {
    error_log("ERROR: Error de conexión MySQL: " . $mysqli->connect_error);
    die("Error de conexión a la base de datos: " . $mysqli->connect_error);
}

$conexion = $mysqli;
error_log("Conexión a base de datos establecida correctamente");

// Procesar descarga masiva si se solicita
if (isset($_POST['download_selected']) && !empty($_POST['selected_docs'])) {
    error_log("Procesando descarga masiva...");
    $selected_docs = $_POST['selected_docs'];
    $prospecto_id = $_POST['prospecto_id'] ?? null;
    
    // Verificar si ZipArchive está disponible
    if (class_exists('ZipArchive')) {
        error_log("ZipArchive disponible, creando archivo...");
        // Crear archivo ZIP
        $zip = new ZipArchive();
        $zipFileName = 'documentos_' . date('Y-m-d_His') . '.zip';
        $zipPath = sys_get_temp_dir() . '/' . $zipFileName;
        
        if ($zip->open($zipPath, ZipArchive::CREATE) === TRUE) {
            foreach ($selected_docs as $doc_id) {
                // Obtener información del documento
                $stmt = $conexion->prepare("SELECT nombre_original, ruta_archivo FROM tb_inteletgroup_documentos WHERE id = ? AND estado = 'Activo'");
                $stmt->bind_param("i", $doc_id);
                $stmt->execute();
                $stmt->bind_result($nombre_original, $ruta_archivo);
                
                if ($stmt->fetch() && file_exists($ruta_archivo)) {
                    $zip->addFile($ruta_archivo, $nombre_original);
                }
                $stmt->close();
            }
            $zip->close();
            
            // Enviar archivo ZIP
            header('Content-Type: application/zip');
            header('Content-Disposition: attachment; filename="' . $zipFileName . '"');
            header('Content-Length: ' . filesize($zipPath));
            readfile($zipPath);
            unlink($zipPath);
            exit;
        }
    }
}

// Obtener tipos de documentos
$tipos_documento = [];
try {
    error_log("Obteniendo tipos de documentos...");
    // Verificar si la tabla existe primero
    $check_table = $conexion->query("SHOW TABLES LIKE 'tb_inteletgroup_tipos_documento'");
    if (!$check_table) {
        error_log("Error al verificar tabla tipos_documento: " . $conexion->error);
    } elseif ($check_table && $check_table->num_rows > 0) {
        error_log("Tabla tipos_documento existe, obteniendo datos...");
        $stmt = $conexion->prepare("SELECT id, codigo, nombre, descripcion FROM tb_inteletgroup_tipos_documento ORDER BY orden ASC");
        if ($stmt) {
            $stmt->execute();
            $stmt->bind_result($tipo_id, $tipo_codigo, $tipo_nombre, $tipo_descripcion);
            
            while ($stmt->fetch()) {
                $tipos_documento[$tipo_codigo] = [
                    'id' => $tipo_id,
                    'nombre' => $tipo_nombre,
                    'descripcion' => $tipo_descripcion
                ];
            }
            $stmt->close();
        }
    }
} catch (Exception $e) {
    error_log("Error obteniendo tipos de documento: " . $e->getMessage());
}

// Obtener todos los prospectos con información completa
$prospectos_completos = [];
try {
    // Verificar si las tablas existen y si podemos usar consultas complejas
    $tables_ok = true;
    $check_checklist = $conexion->query("SHOW TABLES LIKE 'tb_inteletgroup_documento_checklist'");
    if (!$check_checklist || $check_checklist->num_rows == 0) {
        $tables_ok = false;
        error_log("Tabla tb_inteletgroup_documento_checklist no existe");
    }

    // Verificar si hay problemas de collation
    $collation_ok = true;
    try {
        // Test simple para verificar si los JOINs funcionan
        $test_query = $conexion->prepare("
            SELECT COUNT(*) as test_count
            FROM tb_inteletgroup_prospectos p
            LEFT JOIN tb_inteletgroup_tipos_documento td ON td.tipo_persona = p.tipo_persona COLLATE utf8mb4_spanish_ci
            WHERE p.usuario_id = ?
            LIMIT 1
        ");
        if ($test_query) {
            $test_query->bind_param("i", $usuario_id);
            $test_query->execute();
            $test_query->close();
        } else {
            $collation_ok = false;
            error_log("Problema de collation detectado, usando consulta simplificada");
        }
    } catch (Exception $e) {
        $collation_ok = false;
        error_log("Error de collation: " . $e->getMessage());
    }

    $tables_ok = $tables_ok && $collation_ok;
    
    if ($tables_ok) {
        // Consulta con corrección de collation para evitar conflictos
        $stmt = $conexion->prepare("
            SELECT
                p.id,
                p.tipo_persona,
                p.rut_cliente,
                p.razon_social,
                p.rubro,
                p.email,
                p.telefono_celular,
                p.nombre_ejecutivo,
                p.fecha_registro,
                COUNT(DISTINCT d.id) as total_documentos,
                COUNT(DISTINCT CASE
                    WHEN d.tipo_documento_id IS NOT NULL
                    AND td.es_obligatorio = 1
                    AND d.estado = 'Activo'
                    THEN d.tipo_documento_id
                END) as documentos_obligatorios_completados,
                COUNT(DISTINCT CASE
                    WHEN td.es_obligatorio = 1
                    THEN td.id
                END) as total_documentos_obligatorios
            FROM tb_inteletgroup_prospectos p
            LEFT JOIN tb_inteletgroup_tipos_documento td ON
                (td.tipo_persona COLLATE utf8mb4_spanish_ci = p.tipo_persona COLLATE utf8mb4_spanish_ci
                 OR td.tipo_persona = 'Ambos')
                AND td.estado = 'Activo'
            LEFT JOIN tb_inteletgroup_documentos d ON
                p.id = d.prospecto_id
                AND d.tipo_documento_id = td.id
                AND d.estado = 'Activo'
            WHERE p.usuario_id = ?
            GROUP BY p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email,
                     p.telefono_celular, p.nombre_ejecutivo, p.fecha_registro
            ORDER BY p.fecha_registro DESC
        ");
    } else {
        // Usar consulta simplificada sin JOINs complejos para evitar problemas de collation
        $stmt = $conexion->prepare("
            SELECT
                p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email,
                p.telefono_celular, p.nombre_ejecutivo, p.fecha_registro,
                (SELECT COUNT(*) FROM tb_inteletgroup_documentos d WHERE d.prospecto_id = p.id AND d.estado = 'Activo') as total_documentos,
                0 as documentos_obligatorios_completados,
                0 as total_documentos_obligatorios
            FROM tb_inteletgroup_prospectos p
            WHERE p.usuario_id = ?
            ORDER BY p.fecha_registro DESC
        ");
    }
    
    if ($stmt) {
        error_log("Ejecutando consulta de prospectos para usuario_id: $usuario_id");
        $stmt->bind_param("i", $usuario_id);
        $stmt->execute();

        $stmt->bind_result($id, $tipo_persona, $rut_cliente, $razon_social, $rubro, $email,
                          $telefono_celular, $nombre_ejecutivo, $fecha_registro,
                          $total_documentos, $documentos_obligatorios_completados, $total_documentos_obligatorios);
        
        $count = 0;
        while ($stmt->fetch()) {
            $count++;
            error_log("Prospecto encontrado: ID=$id, RUT=$rut_cliente, Razón Social=$razon_social");

            // Calcular porcentaje basado solo en documentos obligatorios
            $porcentaje_completado = $total_documentos_obligatorios > 0
                ? round(($documentos_obligatorios_completados / $total_documentos_obligatorios) * 100) 
                : 0;
            
            $prospectos_completos[] = [
                'id' => $id,
                'tipo_persona' => $tipo_persona,
                'rut_cliente' => $rut_cliente,
                'razon_social' => $razon_social,
                'rubro' => $rubro,
                'email' => $email,
                'telefono_celular' => $telefono_celular,
                'nombre_ejecutivo' => $nombre_ejecutivo,
                'fecha_registro' => $fecha_registro,
                'total_documentos' => $total_documentos,
                'documentos_completados' => $documentos_obligatorios_completados,
                'documentos_requeridos' => $total_documentos_obligatorios,
                'porcentaje_completado' => $porcentaje_completado
            ];
            
            error_log("Prospecto $razon_social: $documentos_obligatorios_completados/$total_documentos_obligatorios obligatorios = $porcentaje_completado%");
        }
        $stmt->close();
        error_log("Total de prospectos encontrados: $count");
    } else {
        error_log("Error al preparar consulta de prospectos: " . $conexion->error);
    }
} catch (Exception $e) {
    error_log("Error obteniendo prospectos: " . $e->getMessage());
}

// Obtener prospecto seleccionado
$prospecto_seleccionado = null;
$documentos_prospecto = [];
$checklist_prospecto = [];

if (isset($_GET['prospecto_id'])) {
    $prospecto_id = intval($_GET['prospecto_id']);
    error_log("Buscando prospecto ID: $prospecto_id");
    
    // Buscar el prospecto en la lista
    foreach ($prospectos_completos as $prospecto) {
        if ($prospecto['id'] == $prospecto_id) {
            $prospecto_seleccionado = $prospecto;
            error_log("Prospecto encontrado en lista: " . print_r($prospecto_seleccionado, true));
            break;
        }
    }
    
    // Si no se encontró en la lista, buscarlo directamente en la BD
    if (!$prospecto_seleccionado) {
        error_log("Prospecto no encontrado en lista, buscando en BD...");
        $stmt = $conexion->prepare("
            SELECT 
                p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro, p.email, 
                p.telefono_celular, p.nombre_ejecutivo, p.fecha_registro
            FROM tb_inteletgroup_prospectos p
            WHERE p.id = ?
            LIMIT 1
        ");
        
        if ($stmt) {
            $stmt->bind_param("i", $prospecto_id);
            $stmt->execute();
            $stmt->bind_result($id, $tipo_persona, $rut_cliente, $razon_social, $rubro, $email, 
                              $telefono_celular, $nombre_ejecutivo, $fecha_registro);
            
            if ($stmt->fetch()) {
                $prospecto_seleccionado = [
                    'id' => $id,
                    'tipo_persona' => $tipo_persona,
                    'rut_cliente' => $rut_cliente,
                    'razon_social' => $razon_social,
                    'rubro' => $rubro,
                    'email' => $email,
                    'telefono_celular' => $telefono_celular,
                    'nombre_ejecutivo' => $nombre_ejecutivo,
                    'fecha_registro' => $fecha_registro,
                    'total_documentos' => 0,
                    'documentos_completados' => 0,
                    'documentos_requeridos' => 0,
                    'porcentaje_completado' => 0
                ];
                error_log("Prospecto encontrado en BD: " . print_r($prospecto_seleccionado, true));
            }
            $stmt->close();
        }
    }
    
    if ($prospecto_seleccionado) {
        // Obtener documentos del prospecto
        try {
            error_log("Obteniendo documentos del prospecto $prospecto_id");
            // Verificar si la tabla de tipos existe antes de hacer JOIN
            $check_tipos = $conexion->query("SHOW TABLES LIKE 'tb_inteletgroup_tipos_documento'");
            
            if ($check_tipos && $check_tipos->num_rows > 0) {
                $stmt = $conexion->prepare("
                    SELECT 
                        d.id, d.tipo_documento_id, d.nombre_archivo, d.nombre_original,
                        d.tipo_archivo, d.tamaño_archivo, d.fecha_subida,
                        td.nombre as tipo_documento_nombre, td.codigo as tipo_documento_codigo
                    FROM tb_inteletgroup_documentos d
                    LEFT JOIN tb_inteletgroup_tipos_documento td ON d.tipo_documento_id = td.id
                    WHERE d.prospecto_id = ? AND d.estado = 'Activo'
                    ORDER BY d.fecha_subida DESC
                ");
            } else {
                // Consulta sin JOIN si no existe la tabla de tipos
                $stmt = $conexion->prepare("
                    SELECT 
                        d.id, d.tipo_documento_id, d.nombre_archivo, d.nombre_original,
                        d.tipo_archivo, d.tamaño_archivo, d.fecha_subida,
                        NULL as tipo_documento_nombre, NULL as tipo_documento_codigo
                    FROM tb_inteletgroup_documentos d
                    WHERE d.prospecto_id = ? AND d.estado = 'Activo'
                    ORDER BY d.fecha_subida DESC
                ");
            }
            
            if ($stmt) {
                $stmt->bind_param("i", $prospecto_id);
                $stmt->execute();
                $stmt->bind_result($doc_id, $tipo_doc_id, $nombre_archivo, $nombre_original,
                                 $tipo_archivo, $tamaño_archivo, $fecha_subida,
                                 $tipo_doc_nombre, $tipo_doc_codigo);
                
                while ($stmt->fetch()) {
                    $documentos_prospecto[] = [
                        'id' => $doc_id,
                        'tipo_documento_id' => $tipo_doc_id,
                        'nombre_archivo' => $nombre_archivo,
                        'nombre_original' => $nombre_original,
                        'tipo_archivo' => $tipo_archivo,
                        'tamaño_archivo' => $tamaño_archivo,
                        'fecha_subida' => $fecha_subida,
                        'tipo_documento_nombre' => $tipo_doc_nombre,
                        'tipo_documento_codigo' => $tipo_doc_codigo
                    ];
                }
                $stmt->close();
            } else {
                error_log("Error preparando consulta de documentos: " . $conexion->error);
            }
            
            // Crear checklist dinámico basado en documentos realmente subidos
            $check_tipos_table = $conexion->query("SHOW TABLES LIKE 'tb_inteletgroup_tipos_documento'");

            if ($check_tipos_table && $check_tipos_table->num_rows > 0) {

                // Primero obtener todos los tipos de documentos para este tipo de persona
                $stmt = $conexion->prepare("
                    SELECT id, codigo, nombre, descripcion, es_obligatorio, orden
                    FROM tb_inteletgroup_tipos_documento
                    WHERE (tipo_persona = ? OR tipo_persona = 'Ambos') AND estado = 'Activo'
                    ORDER BY orden ASC
                ");

                if ($stmt) {
                    $stmt->bind_param("s", $prospecto_seleccionado['tipo_persona']);
                    $stmt->execute();
                    $stmt->bind_result($tipo_id, $codigo, $nombre, $descripcion, $es_obligatorio, $orden);

                    $tipos_disponibles = [];
                    while ($stmt->fetch()) {
                        $tipos_disponibles[$tipo_id] = [
                            'id' => $tipo_id,
                            'codigo' => $codigo,
                            'nombre' => $nombre,
                            'descripcion' => $descripcion,
                            'es_requerido' => $es_obligatorio, // Mapear es_obligatorio a es_requerido para consistencia
                            'orden' => $orden
                        ];
                    }
                    $stmt->close();

                    // Ahora verificar qué documentos están subidos para cada tipo
                    foreach ($tipos_disponibles as $tipo_id => $tipo_info) {
                        $documento_subido = null;
                        $estado = 'Pendiente';

                        // Buscar si hay un documento subido de este tipo
                        foreach ($documentos_prospecto as $doc) {
                            if ($doc['tipo_documento_id'] == $tipo_id) {
                                $documento_subido = $doc;
                                $estado = 'Subido';
                                break;
                            }
                        }

                        $checklist_prospecto[] = [
                            'tipo_documento_id' => $tipo_id,
                            'estado' => $estado,
                            'documento_id' => $documento_subido ? $documento_subido['id'] : null,
                            'codigo' => $tipo_info['codigo'],
                            'nombre' => $tipo_info['nombre'],
                            'descripcion' => $tipo_info['descripcion'],
                            'es_requerido' => $tipo_info['es_requerido'],
                            'documento_info' => $documento_subido
                        ];
                    }
                } else {
                    error_log("Error preparando consulta de tipos de documento: " . $conexion->error);
                }
            }
            
        } catch (Exception $e) {
            error_log("Error obteniendo documentos del prospecto: " . $e->getMessage());
        }
    }
}

// Log antes de generar HTML
error_log("=== Llegando a la generación de HTML ===");
error_log("Prospecto seleccionado: " . ($prospecto_seleccionado ? "ID " . $prospecto_seleccionado['id'] : "Ninguno"));
error_log("Total prospectos en lista: " . count($prospectos_completos));

// Modo de depuración: si hay un parámetro debug=1, mostrar información básica
if (isset($_GET['debug']) && $_GET['debug'] == '1') {
    echo "<!DOCTYPE html><html><head><title>Debug Info</title></head><body>";
    echo "<h1>Debug Information</h1>";
    echo "<p>Script ejecutado hasta el punto de generar HTML</p>";
    echo "<p>Usuario ID: " . $usuario_id . "</p>";
    echo "<p>Prospecto ID: " . ($prospecto_id ?? 'No especificado') . "</p>";
    echo "<p>Prospectos cargados: " . count($prospectos_completos) . "</p>";
    echo "<p>Prospecto seleccionado: " . ($prospecto_seleccionado ? 'Sí' : 'No') . "</p>";
    echo "<a href='inteletgroup_documentos_enhanced.php" . ($prospecto_id ? "?prospecto_id=$prospecto_id" : "") . "'>Continuar sin debug</a>";
    echo "</body></html>";
    exit;
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestión de Documentos - InteletGroup</title>
    <?php echo no_cache_meta(); ?>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    
    <link rel="stylesheet" href="css/inteletgroup_documentos_enhanced.css?v=<?php echo time(); ?>">


    <style>
        .header-action-btn {
            background-color: var(--primary-dark); /* Un azul oscuro de tu paleta */
            color: white !important; /* Texto blanco */
            border: 1px solid var(--primary-medium); /* Borde sutil */
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            transition: background-color 0.3s ease, border-color 0.3s ease;
            display: inline-flex;
            align-items: center;
            text-decoration: none; /* Asegura que no tenga subrayado */
        }

        .header-action-btn:hover {
            background-color: var(--primary-blue); /* Un azul más claro al pasar el ratón */
            border-color: var(--primary-blue);
            color: white !important;
        }

        .header-action-btn .bi {
            margin-right: 0.5rem;
        }

    html {
            zoom: 80%; /* Works in most modern browsers */
        } 
        
        /* Eliminar márgenes laterales y hacer que el contenedor ocupe todo el ancho */
        body {
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 100% !important;
            padding-left: 15px;
            padding-right: 15px;
            margin-left: 0;
            margin-right: 0;
            width: 100%;
        }
        
        .row {
            margin-left: 0;
            margin-right: 0;
        }
        
        /* Estilos para el modal personalizado que reemplaza al de Bootstrap */
        .custom-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            display: none;
            overflow: hidden;
        }
        
        .custom-modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 9998;
        }
        
        .custom-modal-dialog {
            position: relative;
            width: 500px;
            max-width: 90%;
            margin: 50px auto;
            z-index: 10000;
            animation: modalFadeIn 0.3s ease;
        }
        
        @keyframes modalFadeIn {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .custom-modal-content {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .custom-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
            background-color: #f8f9fa;
        }
        
        .custom-modal-title {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 500;
        }
        
        .custom-modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            font-weight: 700;
            line-height: 1;
            color: #000;
            opacity: 0.5;
            cursor: pointer;
        }
        
        .custom-modal-close:hover {
            opacity: 0.75;
        }
        
        .custom-modal-body {
            padding: 1rem;
            max-height: calc(100vh - 200px);
            overflow-y: auto;
        }
        
        .custom-modal-footer {
            display: flex;
            justify-content: flex-end;
            padding: 1rem;
            border-top: 1px solid #e9ecef;
            background-color: #f8f9fa;
            gap: 0.5rem;
        }
        
        /* Para evitar que el cuerpo de la página se desplace cuando el modal está abierto */
        body.custom-modal-open {
            overflow: hidden;
        }
    </style>
    
</head>
<body>
    <!-- Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1>
                        <i class="bi bi-files me-3"></i>
                        Gestión de Documentos
                    </h1>
                    <p class="mb-0 opacity-75">Administra todos los documentos de tus prospectos</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="user-section d-flex justify-content-end align-items-center">
                        <?php if ($_SESSION['usuario_id'] == 4): ?>
                        <a href="inteletgroup_admin_dashboard.php" class="header-action-btn me-2">
                            <i class="bi bi-speedometer2 me-1"></i> Dashboard Admin
                        </a>
                        <?php endif; ?>
                        <a href="form_inteletgroup.php" class="header-action-btn me-3">
                            <i class="bi bi-arrow-left me-2"></i>
                            Volver al Panel
                        </a>
                        <div class="user-info-container me-3">
                            <div class="user-name"><?php echo htmlspecialchars($nombre_usuario); ?></div>
                            <div class="user-role"><?php echo htmlspecialchars($proyecto); ?></div>
                        </div>
                        <a href="logout.php" class="logout-btn" title="Cerrar sesión" onclick="return confirm('¿Estás seguro de que quieres cerrar la sesión?');">
                            <i class="bi bi-box-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Mensajes de alerta -->
        <?php if (isset($_GET['mensaje'])): ?>
            <div class="alert alert-<?php echo $_GET['tipo'] ?? 'info'; ?> alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($_GET['mensaje']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        
        <!-- Estadísticas generales -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="modern-card stats-card">
                    <div class="stats-number"><?php echo count($prospectos_completos); ?></div>
                    <div class="stats-label">Total Prospectos</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="modern-card stats-card">
                    <div class="stats-number">
                        <?php 
                        $total_docs = array_sum(array_column($prospectos_completos, 'total_documentos'));
                        echo $total_docs;
                        ?>
                    </div>
                    <div class="stats-label">Total Documentos</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="modern-card stats-card">
                    <div class="stats-number">
                        <?php 
                        $completos = count(array_filter($prospectos_completos, function($p) {
                            // Solo contar como completos aquellos con 100% de documentos obligatorios
                            return $p['porcentaje_completado'] == 100 && $p['documentos_requeridos'] > 0;
                        }));
                        echo $completos;
                        ?>
                    </div>
                    <div class="stats-label">Prospectos Completos</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="modern-card stats-card">
                    <div class="stats-number">
                        <?php 
                        $promedio = count($prospectos_completos) > 0 
                            ? round(array_sum(array_column($prospectos_completos, 'porcentaje_completado')) / count($prospectos_completos))
                            : 0;
                        echo $promedio . '%';
                        ?>
                    </div>
                    <div class="stats-label">Completitud Promedio (Obligatorios)</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Lista de prospectos -->
            <div class="col-md-4">
                <div class="modern-card">
                    <div class="card-header bg-transparent border-0 py-3">
                        <h5 class="mb-0">
                            <i class="bi bi-building me-2"></i>
                            Mis Prospectos
                        </h5>
                        <!-- Filtro de búsqueda -->
                        <div class="mt-3">
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-search"></i></span>
                                <input type="text" class="form-control" id="searchProspects" 
                                       placeholder="Buscar por razón social, RUT o ejecutivo...">
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0" style="max-height: 600px; overflow-y: auto;" id="prospectsList">
                        <?php if (empty($prospectos_completos)): ?>
                            <div class="empty-state">
                                <i class="bi bi-inbox empty-state-icon"></i>
                                <p class="empty-state-text">No hay prospectos registrados</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($prospectos_completos as $prospecto): ?>
                                <div class="prospect-card p-3 border-bottom <?php echo ($prospecto_seleccionado && $prospecto_seleccionado['id'] == $prospecto['id']) ? 'selected' : ''; ?>"
                                     onclick="window.location.href='?prospecto_id=<?php echo $prospecto['id']; ?>'"
                                     data-razon-social="<?php echo htmlspecialchars(strtolower($prospecto['razon_social'])); ?>"
                                     data-rut="<?php echo htmlspecialchars(strtolower($prospecto['rut_cliente'])); ?>"
                                     data-ejecutivo="<?php echo htmlspecialchars(strtolower($prospecto['nombre_ejecutivo'])); ?>">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($prospecto['razon_social']); ?></h6>
                                            <small class="text-muted">RUT: <?php echo htmlspecialchars($prospecto['rut_cliente']); ?></small>
                                        </div>
                                        <span class="badge bg-<?php echo $prospecto['tipo_persona'] == 'Natural' ? 'info' : 'success'; ?>">
                                            <?php echo $prospecto['tipo_persona']; ?>
                                        </span>
                                    </div>
                                    
                                    <div class="progress-custom mb-2">
                                        <div class="progress-bar-custom <?php 
                                            $porcentaje = $prospecto['porcentaje_completado'];
                                            if ($porcentaje < 50) {
                                                echo 'red';
                                            } elseif ($porcentaje < 80) {
                                                echo 'yellow';
                                            } else {
                                                echo 'green';
                                            }
                                        ?>" style="width: <?php echo $prospecto['porcentaje_completado']; ?>%"></div>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="bi bi-files me-1"></i>
                                            <?php echo $prospecto['documentos_completados']; ?>/<?php echo $prospecto['documentos_requeridos']; ?> obligatorios
                                        </small>
                                        <small class="text-muted">
                                            <?php echo $prospecto['porcentaje_completado']; ?>% completo
                                        </small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Detalles del prospecto -->
            <div class="col-md-8">
                <?php if ($prospecto_seleccionado): ?>
                    <!-- Información del prospecto -->
                    <div class="modern-card mb-4">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h4 class="mb-3"><?php echo htmlspecialchars($prospecto_seleccionado['razon_social']); ?></h4>
                                    <p class="mb-2"><strong>RUT:</strong> <?php echo htmlspecialchars($prospecto_seleccionado['rut_cliente']); ?></p>
                                    <p class="mb-2"><strong>Email:</strong> <?php echo htmlspecialchars($prospecto_seleccionado['email']); ?></p>
                                    <p class="mb-2"><strong>Teléfono:</strong> <?php echo htmlspecialchars($prospecto_seleccionado['telefono_celular']); ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-2"><strong>Rubro:</strong> <?php echo htmlspecialchars($prospecto_seleccionado['rubro']); ?></p>
                                    <p class="mb-2"><strong>Ejecutivo:</strong> <?php echo htmlspecialchars($prospecto_seleccionado['nombre_ejecutivo']); ?></p>
                                    <p class="mb-2"><strong>Fecha registro:</strong> <?php echo date('d/m/Y', strtotime($prospecto_seleccionado['fecha_registro'])); ?></p>
                                    <p class="mb-2"><strong>Tipo:</strong> Persona <?php echo $prospecto_seleccionado['tipo_persona']; ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tabs para documentos y checklist -->
                    <div class="modern-card">
                        <div class="card-header bg-transparent border-0">
                            <ul class="nav nav-tabs card-header-tabs" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" data-bs-toggle="tab" href="#documentos-tab">
                                        <i class="bi bi-files me-2"></i>
                                        Documentos (<?php echo count($documentos_prospecto); ?>)
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#checklist-tab">
                                        <i class="bi bi-check2-square me-2"></i>
                                        Checklist
                                    </a>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="card-body">
                            <div class="tab-content">
                                <!-- Tab de documentos -->
                                <div class="tab-pane fade show active" id="documentos-tab">
                                    <?php if (empty($documentos_prospecto)): ?>
                                        <div class="empty-state">
                                            <i class="bi bi-file-earmark empty-state-icon"></i>
                                            <p class="empty-state-text">No hay documentos subidos</p>
                                        </div>
                                    <?php else: ?>
                                        <form method="POST" id="documents-form">
                                            <input type="hidden" name="prospecto_id" value="<?php echo $prospecto_seleccionado['id']; ?>">
                                            
                                            <div class="mb-3">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleAllDocuments()">
                                                    <i class="bi bi-check-all me-1"></i>
                                                    Seleccionar todos
                                                </button>
                                                <button type="submit" name="download_selected" class="btn btn-sm btn-success ms-2" 
                                                        onclick="return validateSelection()">
                                                    <i class="bi bi-download me-1"></i>
                                                    Descargar seleccionados
                                                </button>
                                            </div>
                                            
                                            <div class="document-grid">
                                                <?php foreach ($documentos_prospecto as $doc): 
                                                    $file_extension = strtolower(pathinfo($doc['nombre_original'], PATHINFO_EXTENSION));
                                                    $doc_class = '';
                                                    $icon_class = '';
                                                    
                                                    if ($file_extension == 'pdf') {
                                                        $doc_class = 'pdf';
                                                        $icon_class = 'bi-file-earmark-pdf';
                                                    } elseif (in_array($file_extension, ['doc', 'docx'])) {
                                                        $doc_class = 'doc';
                                                        $icon_class = 'bi-file-earmark-word';
                                                    } elseif (in_array($file_extension, ['jpg', 'jpeg', 'png'])) {
                                                        $doc_class = 'image';
                                                        $icon_class = 'bi-file-earmark-image';
                                                    } elseif (in_array($file_extension, ['xls', 'xlsx'])) {
                                                        $doc_class = 'excel';
                                                        $icon_class = 'bi-file-earmark-excel';
                                                    } else {
                                                        $icon_class = 'bi-file-earmark';
                                                    }
                                                ?>
                                                    <div class="document-card <?php echo $doc_class; ?>">
                                                        <input type="checkbox" name="selected_docs[]" value="<?php echo $doc['id']; ?>"
                                                               class="form-check-input document-checkbox">

                                                        <?php if ($doc['tipo_documento_nombre']): ?>
                                                            <div class="document-type-title">
                                                                <?php echo htmlspecialchars($doc['tipo_documento_nombre']); ?>
                                                            </div>
                                                        <?php else: ?>
                                                            <div class="document-type-title text-muted">
                                                                Documento sin clasificar
                                                            </div>
                                                        <?php endif; ?>

                                                        <div class="d-flex align-items-center justify-content-center mb-2">
                                                            <i class="bi <?php echo $icon_class; ?> document-icon me-2"></i>
                                                            <div class="document-filename text-truncate" title="<?php echo htmlspecialchars($doc['nombre_original']); ?>">
                                                                <?php echo htmlspecialchars($doc['nombre_original']); ?>
                                                            </div>
                                                        </div>
                                                        
                                                        <p class="text-muted small mb-2">
                                                            <?php echo number_format($doc['tamaño_archivo'] / 1024, 2); ?> KB
                                                        </p>
                                                        
                                                        <p class="text-muted small mb-3">
                                                            <i class="bi bi-calendar3 me-1"></i>
                                                            <?php echo date('d/m/Y H:i', strtotime($doc['fecha_subida'])); ?>
                                                        </p>
                                                        
                                                        <div class="d-grid gap-2">
                                                            <a href="descargar_documento.php?id=<?php echo $doc['id']; ?>&action=view"
                                                               class="btn btn-sm btn-primary" target="_blank">
                                                                <i class="bi bi-eye me-1"></i>
                                                                Ver
                                                            </a>
                                                            <a href="descargar_documento.php?id=<?php echo $doc['id']; ?>&action=download"
                                                               class="btn btn-sm btn-outline-primary">
                                                                <i class="bi bi-download me-1"></i>
                                                                Descargar
                                                            </a>
                                                        </div>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </form>
                                    <?php endif; ?>
                                </div>

                                <!-- Tab de checklist -->
                                <div class="tab-pane fade" id="checklist-tab">
                                    <?php if (empty($checklist_prospecto)): ?>
                                        <div class="empty-state">
                                            <i class="bi bi-list-check empty-state-icon"></i>
                                            <p class="empty-state-text">No hay checklist disponible</p>
                                        </div>
                                    <?php else: ?>
                                        <!-- Resumen del checklist -->
                                        <div class="row mb-4">
                                            <div class="col-md-12">
                                                <?php
                                                $total_documentos = count($checklist_prospecto);
                                                $documentos_subidos = count(array_filter($checklist_prospecto, function($item) { return $item['estado'] == 'Subido'; }));
                                                $documentos_requeridos = count(array_filter($checklist_prospecto, function($item) { return $item['es_requerido']; }));
                                                $requeridos_subidos = count(array_filter($checklist_prospecto, function($item) { return $item['es_requerido'] && $item['estado'] == 'Subido'; }));
                                                $porcentaje_total = $total_documentos > 0 ? round(($documentos_subidos / $total_documentos) * 100) : 0;
                                                $porcentaje_requeridos = $documentos_requeridos > 0 ? round(($requeridos_subidos / $documentos_requeridos) * 100) : 0;
                                                ?>
                                                <div class="alert alert-info">
                                                    <div class="row text-center">
                                                        <div class="col-md-3">
                                                            <h5 class="mb-1"><?php echo $documentos_subidos; ?>/<?php echo $total_documentos; ?></h5>
                                                            <small>Total Documentos</small>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <h5 class="mb-1"><?php echo $requeridos_subidos; ?>/<?php echo $documentos_requeridos; ?></h5>
                                                            <small>Requeridos</small>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <h5 class="mb-1 text-success"><?php echo $porcentaje_requeridos; ?>%</h5>
                                                            <small>Completitud Requeridos</small>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <h5 class="mb-1 text-primary"><?php echo $porcentaje_total; ?>%</h5>
                                                            <small>Completitud Total</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Tabla de Documentos Requeridos -->
                                        <div class="mb-4">
                                            <h6 class="mb-3">
                                                <i class="bi bi-exclamation-triangle-fill text-warning me-2"></i>
                                                Documentos Requeridos (<?php echo $requeridos_subidos; ?>/<?php echo $documentos_requeridos; ?>)
                                            </h6>
                                            <div class="table-responsive">
                                                <table class="table table-hover checklist-table">
                                                    <thead>
                                                        <tr>
                                                            <th width="10%">Estado</th>
                                                            <th width="45%">Tipo de Documento</th>
                                                            <th width="20%">Fecha</th>
                                                            <th width="25%">Acciones</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($checklist_prospecto as $item): ?>
                                                            <?php if ($item['es_requerido']): ?>
                                                                <tr class="<?php echo $item['estado'] == 'Subido' ? 'table-success' : 'table-warning'; ?>">
                                                                    <td class="text-center">
                                                                        <?php if ($item['estado'] == 'Subido'): ?>
                                                                            <i class="bi bi-check-circle-fill text-success status-icon"></i>
                                                                        <?php else: ?>
                                                                            <i class="bi bi-x-circle-fill text-danger status-icon"></i>
                                                                        <?php endif; ?>
                                                                    </td>
                                                                    <td>
                                                                        <strong><?php echo htmlspecialchars($item['nombre']); ?></strong>
                                                                        <br>
                                                                        <small class="text-muted"><?php echo htmlspecialchars($item['descripcion']); ?></small>
                                                                    </td>
                                                                    <td>
                                                                        <?php if ($item['estado'] == 'Subido' && $item['documento_info']): ?>
                                                                            <small class="text-muted">
                                                                                <?php echo date('d/m/Y', strtotime($item['documento_info']['fecha_subida'])); ?>
                                                                            </small>
                                                                        <?php else: ?>
                                                                            <small class="text-muted">-</small>
                                                                        <?php endif; ?>
                                                                    </td>
                                                                    <td>
                                                                        <?php if ($item['estado'] == 'Subido' && $item['documento_info']): ?>
                                                                            <div class="btn-group btn-group-sm">
                                                                                <a href="descargar_documento.php?id=<?php echo $item['documento_info']['id']; ?>&action=view"
                                                                                   class="btn btn-outline-primary btn-sm" target="_blank" title="Ver documento">
                                                                                    <i class="bi bi-eye"></i> Ver
                                                                                </a>
                                                                                <a href="descargar_documento.php?id=<?php echo $item['documento_info']['id']; ?>&action=download"
                                                                                   class="btn btn-outline-secondary btn-sm" title="Descargar">
                                                                                    <i class="bi bi-download"></i> Descargar
                                                                                </a>
                                                                            </div>
                                                                        <?php else: ?>
                                                                            <button type="button" class="btn btn-outline-primary btn-sm" 
                                                                                    onclick="uploadDocumentForType(<?php echo $item['tipo_documento_id']; ?>, '<?php echo htmlspecialchars($item['nombre'], ENT_QUOTES); ?>')">
                                                                                <i class="bi bi-upload"></i> Subir
                                                                            </button>
                                                                        <?php endif; ?>
                                                                    </td>
                                                                </tr>
                                                            <?php endif; ?>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>

                                        <!-- Tabla de Documentos Opcionales -->
                                        <div class="mb-4">
                                            <h6 class="mb-3">
                                                <i class="bi bi-info-circle-fill text-info me-2"></i>
                                                Documentos Opcionales (<?php echo count(array_filter($checklist_prospecto, function($item) { return !$item['es_requerido'] && $item['estado'] == 'Subido'; })); ?>/<?php echo count(array_filter($checklist_prospecto, function($item) { return !$item['es_requerido']; })); ?>)
                                            </h6>
                                            <div class="table-responsive">
                                                <table class="table table-hover checklist-table">
                                                    <thead>
                                                        <tr>
                                                            <th width="10%">Estado</th>
                                                            <th width="45%">Tipo de Documento</th>
                                                            <th width="20%">Fecha</th>
                                                            <th width="25%">Acciones</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($checklist_prospecto as $item): ?>
                                                            <?php if (!$item['es_requerido']): ?>
                                                                <tr class="<?php echo $item['estado'] == 'Subido' ? 'table-success' : ''; ?>">
                                                                    <td class="text-center">
                                                                        <?php if ($item['estado'] == 'Subido'): ?>
                                                                            <i class="bi bi-check-circle-fill text-success status-icon"></i>
                                                                        <?php else: ?>
                                                                            <i class="bi bi-circle text-muted status-icon"></i>
                                                                        <?php endif; ?>
                                                                    </td>
                                                                    <td>
                                                                        <strong><?php echo htmlspecialchars($item['nombre']); ?></strong>
                                                                        <br>
                                                                        <small class="text-muted"><?php echo htmlspecialchars($item['descripcion']); ?></small>
                                                                    </td>
                                                                    <td>
                                                                        <?php if ($item['estado'] == 'Subido' && $item['documento_info']): ?>
                                                                            <small class="text-muted">
                                                                                <?php echo date('d/m/Y', strtotime($item['documento_info']['fecha_subida'])); ?>
                                                                            </small>
                                                                        <?php else: ?>
                                                                            <small class="text-muted">-</small>
                                                                        <?php endif; ?>
                                                                    </td>
                                                                    <td>
                                                                        <?php if ($item['estado'] == 'Subido' && $item['documento_info']): ?>
                                                                            <div class="btn-group btn-group-sm">
                                                                                <a href="descargar_documento.php?id=<?php echo $item['documento_info']['id']; ?>&action=view"
                                                                                   class="btn btn-outline-primary btn-sm" target="_blank" title="Ver documento">
                                                                                    <i class="bi bi-eye"></i> Ver
                                                                                </a>
                                                                                <a href="descargar_documento.php?id=<?php echo $item['documento_info']['id']; ?>&action=download"
                                                                                   class="btn btn-outline-secondary btn-sm" title="Descargar">
                                                                                    <i class="bi bi-download"></i> Descargar
                                                                                </a>
                                                                            </div>
                                                                        <?php else: ?>
                                                                            <button type="button" class="btn btn-outline-secondary btn-sm" 
                                                                                    onclick="uploadDocumentForType(<?php echo $item['tipo_documento_id']; ?>, '<?php echo htmlspecialchars($item['nombre'], ENT_QUOTES); ?>')">
                                                                                <i class="bi bi-upload"></i> Subir
                                                                            </button>
                                                                        <?php endif; ?>
                                                                    </td>
                                                                </tr>
                                                            <?php endif; ?>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="modern-card">
                        <div class="empty-state">
                            <i class="bi bi-arrow-left-circle empty-state-icon"></i>
                            <p class="empty-state-text">Selecciona un prospecto para ver sus documentos</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Floating action buttons -->
    <?php if ($prospecto_seleccionado): ?>
    <!-- <div class="floating-actions">
        <button class="btn btn-success floating-btn" onclick="uploadNewDocument()" title="Subir documento">
            <i class="bi bi-plus-lg"></i>
        </button>
        
        <button class="btn btn-primary floating-btn" onclick="downloadAllDocuments()" title="Descargar todos">
            <i class="bi bi-download"></i>
        </button>
    </div> -->
    <?php endif; ?>

    <!-- Modal personalizado para subir documentos (reemplaza el modal Bootstrap) -->
    <div id="customUploadModal" class="custom-modal" style="display:none;">
        <div class="custom-modal-backdrop"></div>
        <div class="custom-modal-dialog">
            <div class="custom-modal-content">
                <div class="custom-modal-header">
                    <h5 class="custom-modal-title">Subir Nuevo Documento</h5>
                    <button type="button" class="custom-modal-close" onclick="closeCustomModal()">&times;</button>
                </div>
                <form method="POST" enctype="multipart/form-data" action="inteletgroup_upload_document.php">
                    <div class="custom-modal-body">
                        <input type="hidden" name="prospecto_id" value="<?php echo $prospecto_seleccionado['id'] ?? ''; ?>">
                        <input type="hidden" name="rut_cliente" value="<?php echo $prospecto_seleccionado['rut_cliente'] ?? ''; ?>">
                        
                        <div class="mb-3">
                            <label class="form-label">Tipo de Documento</label>
                            <select name="tipo_documento_id" id="tipo_documento_select" class="form-select">
                                <option value="">Seleccionar tipo...</option>
                                <?php foreach ($checklist_prospecto as $item): ?>
                                    <option value="<?php echo $item['tipo_documento_id']; ?>">
                                        <?php echo htmlspecialchars($item['nombre']); ?>
                                        <?php echo $item['estado'] == 'Subido' ? '(Ya subido)' : ''; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Archivo</label>
                            <div class="upload-area-modal" onclick="document.getElementById('documento-input').click();" style="cursor: pointer;">
                                <div class="text-center p-3 border border-2 border-dashed rounded">
                                    <i class="bi bi-cloud-upload text-primary" style="font-size: 2rem;"></i>
                                    <p class="mb-0 mt-2">
                                        <span id="file-selected-text">Haz clic para seleccionar archivo</span>
                                    </p>
                                    <small class="text-muted">o arrastra y suelta aquí</small>
                                </div>
                            </div>
                            <input type="file" name="documento" id="documento-input" class="d-none" required
                                   accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.xlsx,.xls"
                                   onchange="updateFileSelectedText(this)">
                            <div class="form-text mt-2">
                                Formatos permitidos: PDF, DOC, DOCX, JPG, JPEG, PNG, XLSX, XLS. Máximo 5MB.
                            </div>
                        </div>
                    </div>
                    <div class="custom-modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="closeCustomModal()">Cancelar</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-upload me-2"></i>
                            Subir Documento
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        // Configuración inicial al cargar la página
        document.addEventListener('DOMContentLoaded', function() {
            // Eliminar cualquier modal-backdrop existente
            const existingBackdrops = document.querySelectorAll('.modal-backdrop');
            existingBackdrops.forEach(backdrop => {
                backdrop.remove();
            });
            
            // Forzar limpieza de cualquier modal abierto anteriormente
            const bodyClasses = document.body.classList;
            if (bodyClasses.contains('modal-open')) {
                bodyClasses.remove('modal-open');
            }
        });
        
        // Initialize modal state
        window.uploadModalOpen = false;

        // Update file selected text
        function updateFileSelectedText(input) {
            const textElement = document.getElementById('file-selected-text');
            if (input.files && input.files.length > 0) {
                textElement.textContent = input.files[0].name;
                textElement.parentElement.parentElement.classList.add('border-success');
                textElement.parentElement.parentElement.classList.remove('border-dashed');
            } else {
                textElement.textContent = 'Haz clic para seleccionar archivo';
                textElement.parentElement.parentElement.classList.remove('border-success');
                textElement.parentElement.parentElement.classList.add('border-dashed');
            }
        }

        // Toggle all documents selection
        function toggleAllDocuments() {
            const checkboxes = document.querySelectorAll('.document-checkbox');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);
            
            checkboxes.forEach(cb => {
                cb.checked = !allChecked;
            });
        }
        
        // Validate selection before download
        function validateSelection() {
            const checkboxes = document.querySelectorAll('.document-checkbox:checked');
            if (checkboxes.length === 0) {
                alert('Por favor selecciona al menos un documento');
                return false;
            }
            return true;
        }
        
        // Upload new document (compatibilidad retroactiva)
        function uploadNewDocument() {
            openCustomModal();
        }
        
        // Funciones para manejar el modal personalizado
        function openCustomModal(tipoDocumentoId, tipoDocumentoNombre) {
            if (window.uploadModalOpen) return;
            
            // Actualizar el título del modal si se proporciona
            if (tipoDocumentoNombre) {
                const modalTitle = document.querySelector('.custom-modal-title');
                if (modalTitle) {
                    modalTitle.textContent = 'Subir: ' + tipoDocumentoNombre;
                }
            }
            
            // Establecer el tipo de documento en el select si se proporciona
            if (tipoDocumentoId) {
                const selectElement = document.getElementById('tipo_documento_select');
                if (selectElement) {
                    selectElement.value = tipoDocumentoId;
                }
            }
            
            // Mostrar el modal
            const modal = document.getElementById('customUploadModal');
            if (modal) {
                window.uploadModalOpen = true;
                document.body.classList.add('custom-modal-open');
                modal.style.display = 'block';
            }
        }
        
        function closeCustomModal() {
            const modal = document.getElementById('customUploadModal');
            if (modal) {
                modal.style.display = 'none';
                document.body.classList.remove('custom-modal-open');
                window.uploadModalOpen = false;
                
                // Resetear el formulario
                const selectElement = document.getElementById('tipo_documento_select');
                if (selectElement) {
                    selectElement.value = '';
                }
                
                const fileInput = document.getElementById('documento-input');
                if (fileInput) {
                    fileInput.value = '';
                    updateFileSelectedText(fileInput);
                }
                
                // Resetear el título del modal
                const modalTitle = document.querySelector('.custom-modal-title');
                if (modalTitle) {
                    modalTitle.textContent = 'Subir Nuevo Documento';
                }
            }
        }
        
        // Cerrar modal al hacer clic en el backdrop
        document.addEventListener('DOMContentLoaded', function() {
            const modalBackdrop = document.querySelector('.custom-modal-backdrop');
            if (modalBackdrop) {
                modalBackdrop.addEventListener('click', closeCustomModal);
            }
        });
        
        // Función para upload con compatibilidad retroactiva
        function uploadDocumentForType(tipoDocumentoId, tipoDocumentoNombre) {
            openCustomModal(tipoDocumentoId, tipoDocumentoNombre);
        }
        
        // Download all documents
        function downloadAllDocuments() {
            const checkboxes = document.querySelectorAll('.document-checkbox');
            checkboxes.forEach(cb => cb.checked = true);
            document.getElementById('documents-form').submit();
        }
        
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
        
        // Search filter functionality
        const searchInput = document.getElementById('searchProspects');
        if (searchInput) {
            searchInput.addEventListener('input', function(e) {
                const searchTerm = e.target.value.toLowerCase().trim();
                const prospectCards = document.querySelectorAll('.prospect-card');
                let visibleCount = 0;
                
                console.log('Búsqueda iniciada:', searchTerm);
                console.log('Total de prospectos:', prospectCards.length);
            
            prospectCards.forEach(card => {
                const razonSocial = card.getAttribute('data-razon-social') || '';
                const rut = card.getAttribute('data-rut') || '';
                const ejecutivo = card.getAttribute('data-ejecutivo') || '';
                
                // Check if search term matches any of the fields
                const matches = razonSocial.includes(searchTerm) || 
                               rut.includes(searchTerm) || 
                               ejecutivo.includes(searchTerm);
                
                if (searchTerm === '' || matches) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });
            
            // Show/hide empty state if no results
            const emptyState = document.querySelector('#prospectsList .empty-state');
            if (visibleCount === 0 && prospectCards.length > 0) {
                // Create and show "no results" message if it doesn't exist
                let noResultsMsg = document.getElementById('noResultsMessage');
                if (!noResultsMsg) {
                    noResultsMsg = document.createElement('div');
                    noResultsMsg.id = 'noResultsMessage';
                    noResultsMsg.className = 'text-center p-4 text-muted';
                    noResultsMsg.innerHTML = '<i class="bi bi-search me-2"></i>No se encontraron prospectos que coincidan con "' + searchTerm + '"';
                    document.getElementById('prospectsList').appendChild(noResultsMsg);
                } else {
                    noResultsMsg.innerHTML = '<i class="bi bi-search me-2"></i>No se encontraron prospectos que coincidan con "' + searchTerm + '"';
                    noResultsMsg.style.display = 'block';
                }
            } else {
                // Hide "no results" message if it exists
                const noResultsMsg = document.getElementById('noResultsMessage');
                if (noResultsMsg) {
                    noResultsMsg.style.display = 'none';
                }
            }
            
                console.log('Prospectos visibles después del filtro:', visibleCount);
            });
        } else {
            console.warn('No se encontró el elemento searchProspects');
        }
        
        // Configurar eventos para drag and drop
        document.addEventListener('DOMContentLoaded', function() {
            // Setup drag and drop for upload area
            const uploadArea = document.querySelector('.upload-area-modal');
            if (uploadArea) {
                uploadArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.classList.add('border-primary');
                });

                uploadArea.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.classList.remove('border-primary');
                });

                uploadArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.classList.remove('border-primary');

                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        const fileInput = document.getElementById('documento-input');
                        fileInput.files = files;
                        updateFileSelectedText(fileInput);
                    }
                });
            }
            
            // Soporte para tecla ESC para cerrar el modal
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && window.uploadModalOpen) {
                    closeCustomModal();
                }
            });
        });
    </script>
</body>
</html>